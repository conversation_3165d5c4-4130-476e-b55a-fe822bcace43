# Dependencies
node_modules/
/.pnp
.pnp.js

# Build outputs
/frontend/dist/
/frontend/build/
/backend/dist/
/backend/build/

# Environment variables
.env
.env.local
.env.dev
.env.docker
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS files
.DS_Store
Thumbs.db

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
/dist
/build
/frontend/public/images/optimized
/backend/uploads

# Docker data volumes
/data/mongodb/
/data/mongodb-config/
/data/redis/

# Test files and temporary scripts
test-*.js
test-*.html
test-*.md
*-test.js
*-test.bat
*-test.sh

# Temporary automation files
CryptoBasriko*
*Otomasyon*
*.spec

# Old setup scripts
run-*.sh
run-*.bat
init-*.sh
init-*.bat
setup-*.old
verify-*.sh

# Coverage reports
coverage/
*.lcov

# Temporary files
*.tmp
*.temp
.cache/
