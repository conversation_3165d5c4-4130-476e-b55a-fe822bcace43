# Node environment
NODE_ENV=production

# Server port
PORT=5000

# MongoDB connection (Production)
MONGO_URI=mongodb+srv://username:<EMAIL>/cryptoyield?retryWrites=true&w=majority
MONGO_USER=production_user
MONGO_PASSWORD=CHANGE_THIS_SECURE_PASSWORD

# Redis configuration (Production)
REDIS_URL=redis://username:password@redis-server:6379
REDIS_HOST=redis-server
REDIS_PORT=6379
REDIS_PASSWORD=CHANGE_THIS_REDIS_PASSWORD

# JWT configuration (Production - MUST CHANGE)
JWT_SECRET=CHANGE_THIS_TO_A_VERY_SECURE_RANDOM_STRING_AT_LEAST_64_CHARACTERS_LONG
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# CORS (Production domain)
FRONTEND_URL=https://your-domain.com
CORS_ORIGIN=https://your-domain.com

# Email service (Production)
EMAIL_SERVICE=sendgrid
EMAIL_API_KEY=CHANGE_THIS_SENDGRID_API_KEY
EMAIL_FROM=<EMAIL>

# Cryptocurrency API keys (Production)
COINMARKETCAP_API_KEY=CHANGE_THIS_CMC_API_KEY
BLOCKCHAIN_API_KEY=CHANGE_THIS_BLOCKCHAIN_API_KEY

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=CHANGE_THIS_SESSION_SECRET

# Logging
LOG_LEVEL=warn

# Rate limiting (Production)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100

# Cache
CACHE_TTL=300

# SSL/TLS
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem

# Monitoring
SENTRY_DSN=https://<EMAIL>/project-id

# Email Configuration
EMAIL_HOST=mail.shpnfinance.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=ThisIsPass@123
EMAIL_FROM=<EMAIL>