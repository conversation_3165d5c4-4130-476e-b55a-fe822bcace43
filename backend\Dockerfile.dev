# Development Dockerfile for CryptoYield Backend
# Optimized for live code editing and hot-reload

FROM node:20-alpine

# Set working directory
WORKDIR /app

# Install system dependencies for development
RUN apk add --no-cache \
    curl \
    bash \
    git \
    python3 \
    make \
    g++

# Copy package files first for better caching
COPY package*.json ./

# Install all dependencies (including dev dependencies)
# Configure npm for SSL compatibility
RUN npm config set strict-ssl false && \
    npm config set registry https://registry.npmjs.org/ && \
    npm config set legacy-peer-deps true && \
    npm install --no-audit --no-fund

# Install global development tools
RUN npm install -g nodemon ts-node typescript cross-env dotenv-cli

# Copy TypeScript configuration
COPY tsconfig.json ./
COPY nodemon.json ./

# Copy environment files
COPY .env.docker ./

# Create necessary directories
RUN mkdir -p src uploads logs scripts

# Set proper permissions for development
RUN chown -R node:node /app && \
    chmod -R 755 /app

# Switch to non-root user for security
USER node

# Expose the port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Default command for development with hot-reload
CMD ["npm", "run", "dev:docker"]
