{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "jest", "lint": "eslint . --ext .ts,.js", "lint:fix": "eslint . --ext .ts,.js --fix", "format": "prettier --write \"src/**/*.ts\" \"tests/**/*.ts\"", "prepare": "husky", "start": "node dist/index.js", "start:prod": "cross-env NODE_ENV=production node dist/index.js", "dev": "node dev.js", "dev:direct": "nodemon --exec ts-node src/index.ts", "dev:local": "cross-env NODE_ENV=development dotenv -e .env.local -- nodemon --exec ts-node --transpile-only src/index.ts", "dev:docker": "NODE_ENV=development nodemon", "debug": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true dotenv -e .env.local -- node --inspect -r ts-node/register src/index.ts", "debug:break": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true dotenv -e .env.local -- node --inspect-brk -r ts-node/register src/index.ts", "debug:docker": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true dotenv -e .env.docker -- node --inspect -r ts-node/register src/index.ts", "build": "echo 'Build completed - using ts-node for runtime compilation'", "build:prod": "echo 'Production build completed - using ts-node for runtime compilation'", "build:tsc": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON> --noEmit<PERSON>n<PERSON><PERSON>r false", "create-admin": "ts-node src/scripts/createAdminUser.ts", "create-admin:local": "ts-node src/scripts/createAdminUserLocal.ts", "create-admin:docker": "cross-env NODE_ENV=development dotenv -e .env.docker -- ts-node src/scripts/createAdminUserLocal.ts", "create-simple-user": "ts-node src/scripts/createSimpleUser.ts", "init-commission-config": "ts-node src/scripts/initReferralCommissionConfig.ts", "init-commission-config:local": "cross-env NODE_ENV=development dotenv -e .env.local -- ts-node src/scripts/initReferralCommissionConfig.ts", "init-commission-config:docker": "cross-env NODE_ENV=development dotenv -e .env.docker -- ts-node src/scripts/initReferralCommissionConfig.ts", "seed:crypto-addresses": "ts-node src/scripts/seedCryptoAddresses.ts", "seed:crypto-addresses:local": "cross-env NODE_ENV=development dotenv -e .env.local -- ts-node src/scripts/seedCryptoAddresses.ts", "seed:crypto-addresses:docker": "cross-env NODE_ENV=development dotenv -e .env.docker -- ts-node src/scripts/seedCryptoAddresses.ts", "test:transactions": "ts-node src/scripts/testTransactions.ts", "test:transactions:local": "cross-env NODE_ENV=development dotenv -e .env.local -- ts-node src/scripts/testTransactions.ts", "test:transactions:docker": "cross-env NODE_ENV=development dotenv -e .env.docker -- ts-node src/scripts/testTransactions.ts", "deploy:prod": "npm run build:prod && npm run start:prod"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/bull": "^3.15.9", "@types/multer": "^1.4.12", "@types/nodemailer": "^6.4.17", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "axios": "^1.9.0", "backend": "file:", "bcrypt": "^5.1.1", "bip32": "^5.0.0-rc.0", "bip39": "^3.1.0", "bitcoinjs-lib": "^6.1.6", "bull": "^4.16.5", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cron": "^4.3.0", "dotenv": "^16.5.0", "ethers": "^6.14.0", "express": "^5.1.0", "express-async-handler": "^1.2.0", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "hpp": "^0.2.3", "isomorphic-dompurify": "^2.25.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "node-cache": "^5.1.2", "node-cron": "^4.0.7", "nodemailer": "^7.0.3", "prom-client": "^15.1.3", "qrcode": "^1.5.4", "redis": "^5.1.0", "socket.io": "^4.8.1", "speakeasy": "^2.0.0", "web3": "^4.16.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.18.2", "xlsx": "^0.18.5", "xss-clean": "^0.1.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/express-mongo-sanitize": "^1.3.2", "@types/express-rate-limit": "^5.1.3", "@types/helmet": "^0.0.48", "@types/hpp": "^0.2.6", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.96", "@types/morgan": "^1.9.9", "@types/node": "^22.15.2", "cross-env": "^7.0.3", "dotenv-cli": "^7.4.4", "jest": "^29.7.0", "mongodb-memory-server": "^10.1.4", "nodemon": "^3.1.10", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "tsc-silent": "^1.2.2", "typescript": "^5.8.3", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "prettier": "^3.3.3", "husky": "^9.1.4", "lint-staged": "^15.2.7"}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "prettier --write"]}}