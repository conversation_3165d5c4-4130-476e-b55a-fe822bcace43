/**
 * Script to calculate and update earnings for existing investment packages
 * This will calculate accumulated interest for all active packages based on their creation date
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import InvestmentPackage from '../src/models/investmentPackageModel';

dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/cryptoyield', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    } as any);
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

const calculateExistingEarnings = async () => {
  try {
    console.log('🔍 Finding existing active investment packages...');

    // Find all active investment packages
    const activePackages = await InvestmentPackage.find({
      status: 'active'
    });

    console.log(`📊 Found ${activePackages.length} active investment packages`);

    if (activePackages.length === 0) {
      console.log('ℹ️ No active investment packages found');
      return;
    }

    let updatedCount = 0;
    const now = new Date();

    for (const pkg of activePackages) {
      try {
        // Calculate days since package creation or activation
        const startDate = pkg.activatedAt || pkg.createdAt;
        const daysSinceStart = Math.floor((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

        // Only calculate if package has been active for at least 1 day
        if (daysSinceStart >= 1) {
          // Calculate daily interest (1% = 0.01)
          const dailyRate = 0.01;
          const dailyInterest = pkg.amount * dailyRate;
          const totalAccumulatedInterest = dailyInterest * daysSinceStart;

          // Update the package with calculated earnings
          const previousEarned = pkg.totalEarned || 0;
          pkg.totalEarned = totalAccumulatedInterest;
          pkg.accumulatedInterest = totalAccumulatedInterest;
          pkg.lastInterestDistribution = now;
          pkg.lastCalculatedAt = now;

          // Update ROI
          pkg.updateROI();

          await pkg.save();

          console.log(`✅ Updated package ${pkg._id}:`);
          console.log(`   - Currency: ${pkg.currency}`);
          console.log(`   - Amount: ${pkg.amount}`);
          console.log(`   - Days active: ${daysSinceStart}`);
          console.log(`   - Previous earned: ${previousEarned}`);
          console.log(`   - New total earned: ${totalAccumulatedInterest.toFixed(6)}`);
          console.log(`   - Daily interest: ${dailyInterest.toFixed(6)}`);
          console.log('');

          updatedCount++;
        } else {
          console.log(`⏳ Package ${pkg._id} (${pkg.currency}) is too new (${daysSinceStart} days), skipping`);
        }
      } catch (error) {
        console.error(`❌ Error updating package ${pkg._id}:`, error);
      }
    }

    console.log(`🎉 Successfully updated ${updatedCount} investment packages with calculated earnings`);

    // Show summary
    const updatedPackages = await InvestmentPackage.find({
      status: 'active',
      totalEarned: { $gt: 0 }
    });

    console.log('\n📈 Summary of packages with earnings:');
    const summary: Record<string, any> = {};

    for (const pkg of updatedPackages) {
      if (!summary[pkg.currency]) {
        summary[pkg.currency] = {
          count: 0,
          totalInvested: 0,
          totalEarned: 0
        };
      }
      summary[pkg.currency].count++;
      summary[pkg.currency].totalInvested += pkg.amount;
      summary[pkg.currency].totalEarned += pkg.totalEarned;
    }

    for (const [currency, data] of Object.entries(summary)) {
      console.log(`${currency}:`);
      console.log(`  - Packages: ${data.count}`);
      console.log(`  - Total Invested: ${data.totalInvested.toFixed(6)} ${currency}`);
      console.log(`  - Total Earned: ${data.totalEarned.toFixed(6)} ${currency}`);
      console.log(`  - Available for Withdrawal: ${data.totalEarned.toFixed(6)} ${currency}`);
      console.log('');
    }

  } catch (error) {
    console.error('❌ Error calculating existing earnings:', error);
  }
};

const main = async () => {
  console.log('🚀 Starting calculation of existing investment earnings...\n');

  await connectDB();
  await calculateExistingEarnings();

  console.log('✅ Calculation completed successfully!');
  console.log('💡 You can now check your withdrawal balances in the frontend');

  await mongoose.disconnect();
  process.exit(0);
};

// Handle errors
process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled Promise Rejection:', err);
  process.exit(1);
});

process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err);
  process.exit(1);
});

// Run the script
main();
