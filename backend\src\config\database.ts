import mongoose from 'mongoose';
import { logger } from '../utils/logger';

interface ConnectionOptions extends mongoose.ConnectOptions {
  maxPoolSize: number;
  minPoolSize: number;
  connectTimeoutMS: number;
  socketTimeoutMS: number;
}

class Database {
  private static instance: Database;
  private retryAttempts: number = 5;
  private retryDelay: number = 5000;
  private isConnected: boolean = false;
  private mockMode: boolean = false;

  private constructor() {
    // Check if we're in development mode with mock database
    this.mockMode = process.env.NODE_ENV === 'development' && (process.env.USE_MOCK_DATABASE === 'true' || false);

    // Only initialize mongoose if not in mock mode
    if (!this.mockMode) {
      this.initializeMongoose();
    }
  }

  public static getInstance(): Database {
    if (!Database.instance) {
      Database.instance = new Database();
    }
    return Database.instance;
  }

  private initializeMongoose() {
    mongoose.connection.on('connected', () => {
      this.isConnected = true;
      logger.info('MongoDB connection successful');
    });

    mongoose.connection.on('error', (err) => {
      this.isConnected = false;
      logger.error('MongoDB connection error:', err);
    });

    mongoose.connection.on('disconnected', () => {
      this.isConnected = false;
      logger.warn('MongoDB connection disconnected');
      this.reconnect();
    });

    process.on('SIGINT', async () => {
      await this.closeConnection();
      process.exit(0);
    });
  }

  private getConnectionOptions(): ConnectionOptions {
    return {
      maxPoolSize: 50,
      minPoolSize: 5,
      connectTimeoutMS: 30000,        // Increased from 10000
      socketTimeoutMS: 60000,         // Increased from 45000
      serverSelectionTimeoutMS: 30000, // Increased from 5000
      heartbeatFrequencyMS: 10000,
      retryWrites: true,
      retryReads: true,
      w: 'majority',
      wtimeoutMS: 10000,              // Increased from 2500
      // Transaction support options
      readPreference: 'primary',
      readConcern: { level: 'majority' },
      writeConcern: { w: 'majority', j: true, wtimeout: 10000 }, // Increased from 2500
      // Replica set options
      // replicaSet: 'rs0', // Geliştirme ortamında genellikle gerekli değildir
      directConnection: false,
    };
  }

  public async connect(retries: number = this.retryAttempts): Promise<void> {
    // If in mock mode, just set connected flag and return
    if (this.mockMode) {
      if (!this.isConnected) {
        logger.info('Running in MOCK DATABASE mode - no actual MongoDB connection');
        this.isConnected = true;
      }
      return;
    }

    try {
      // Get MongoDB URI from environment variables
      const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/cryptoyieldhub';

      // Log connection attempt (without credentials)
      const sanitizedUri = mongoUri.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@');
      logger.info(`Attempting to connect to MongoDB: ${sanitizedUri}`);

      // Get connection options
      const options = this.getConnectionOptions();

      // Connect to MongoDB
      await mongoose.connect(mongoUri, options);

      // Log successful connection
      logger.info('MongoDB connection successful');

      // Validate transaction support
      // await this.validateTransactionSupport(); // Temporarily disabled

      // Create indexes - temporarily disabled
      // await this.createIndexes();

    } catch (error) {
      // Check if error is related to hostname resolution (common in Docker)
      const errorMessage = error.message || '';
      const isHostnameError = errorMessage.includes('getaddrinfo') ||
                              errorMessage.includes('ENOTFOUND') ||
                              errorMessage.includes('EHOSTUNREACH');

      if (isHostnameError) {
        logger.error(`MongoDB hostname resolution error. If using Docker, make sure the container name matches the hostname in MONGO_URI: ${error.message}`);
      } else {
        logger.error('MongoDB connection error:', error);
      }

      if (retries > 0) {
        const waitTime = this.retryDelay * (this.retryAttempts - retries + 1); // Exponential backoff
        logger.info(`Reconnecting in ${waitTime/1000} seconds... (${retries} attempts remaining)`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        return this.connect(retries - 1);
      } else {
        logger.error('MongoDB connection failed after multiple attempts');

        // In development mode, only switch to mock mode if explicitly enabled
        if (process.env.NODE_ENV === 'development' && process.env.USE_MOCK_DATABASE === 'true') {
          logger.info('Switching to MOCK DATABASE mode due to connection failure');
          logger.info('Note: If using Docker, make sure the MongoDB container name matches the hostname in MONGO_URI');
          logger.info('You can use "mongodb" as the hostname when connecting from another container in the same Docker network');
          this.mockMode = true;
          this.isConnected = true;
          return;
        }

        throw error;
      }
    }
  }

  private async createIndexes(): Promise<void> {
    // Skip in mock mode
    if (this.mockMode) {
      logger.info('Skipping index creation in MOCK DATABASE mode');
      return;
    }

    try {
      // User indexes
      await mongoose.model('User').createIndexes();

      // Wallet indexes
      await mongoose.model('Wallet').createIndexes();

      // Transaction indexes
      await mongoose.model('Transaction').createIndexes();

      logger.info('Database indexes created successfully');
    } catch (error) {
      logger.error('Error creating database indexes:', error);

      // In development mode, don't throw the error
      if (process.env.NODE_ENV !== 'development') {
        throw error;
      }
    }
  }

  private async reconnect(): Promise<void> {
    // Skip in mock mode
    if (this.mockMode) {
      this.isConnected = true;
      return;
    }

    if (!this.isConnected) {
      logger.info('Attempting to reconnect...');
      await this.connect();
    }
  }

  public async closeConnection(): Promise<void> {
    // Skip in mock mode
    if (this.mockMode) {
      this.isConnected = false;
      logger.info('Mock database connection closed');
      return;
    }

    try {
      await mongoose.connection.close();
      this.isConnected = false;
      logger.info('MongoDB connection safely closed');
    } catch (error) {
      logger.error('Error closing MongoDB connection:', error);

      // In development mode, don't throw the error
      if (process.env.NODE_ENV !== 'development') {
        throw error;
      }
    }
  }

  public getConnection(): mongoose.Connection {
    // In mock mode, return a mock connection or the real one
    if (this.mockMode) {
      logger.warn('Attempted to get database connection in MOCK mode');
    }
    return mongoose.connection;
  }

  public isConnectedToDatabase(): boolean {
    // In mock mode, always return true
    return this.mockMode || this.isConnected;
  }

  /**
   * Validate that the MongoDB instance supports transactions
   */
  private async validateTransactionSupport(): Promise<void> {
    try {
      // Check if we're connected to a replica set
      const admin = mongoose.connection.db?.admin();
      if (!admin) {
        logger.warn('Cannot access database admin interface');
        return;
      }

      const result = await admin.command({ isMaster: 1 });

      if (!result.setName) {
        logger.warn('MongoDB is not running as a replica set. Transactions may not be available.');
        return;
      }

      logger.info(`Connected to replica set: ${result.setName}`);

      // Test transaction support with a simple operation
      const session = await mongoose.startSession();
      try {
        await session.withTransaction(async () => {
          // Simple test operation
          const testCollection = mongoose.connection.db.collection('_transaction_test');
          await testCollection.insertOne({ test: true }, { session });
          await testCollection.deleteOne({ test: true }, { session });
        });

        logger.info('✅ Transaction support validated successfully');
      } catch (error) {
        logger.error('❌ Transaction support validation failed:', error);
        throw new Error('MongoDB transactions are not supported in this configuration');
      } finally {
        await session.endSession();
      }
    } catch (error) {
      logger.error('Error validating transaction support:', error);
      // Don't throw in development mode to allow fallback
      if (process.env.NODE_ENV === 'production') {
        throw error;
      }
    }
  }

  /**
   * Start a new database session for transactions
   */
  public async startSession(): Promise<mongoose.ClientSession> {
    if (this.mockMode) {
      logger.warn('Cannot start session in mock mode');
      throw new Error('Sessions not available in mock mode');
    }

    return await mongoose.startSession();
  }

  /**
   * Execute a function within a transaction with automatic retry logic
   */
  public async withTransaction<T>(
    operation: (session: mongoose.ClientSession) => Promise<T>,
    options?: {
      maxRetries?: number;
      retryDelay?: number;
    }
  ): Promise<T> {
    if (this.mockMode) {
      logger.warn('Executing operation without transaction in mock mode');
      // In mock mode, execute without session
      return await operation(null as any);
    }

    const maxRetries = options?.maxRetries || 3;
    const retryDelay = options?.retryDelay || 100;

    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const session = await this.startSession();

      try {
        const result = await session.withTransaction(async () => {
          return await operation(session);
        });

        return result;
      } catch (error: any) {
        lastError = error;
        logger.warn(`Transaction attempt ${attempt}/${maxRetries} failed:`, error.message);

        // Check if error is retryable
        if (this.isRetryableError(error) && attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
          continue;
        }

        throw error;
      } finally {
        await session.endSession();
      }
    }

    throw lastError!;
  }

  /**
   * Check if an error is retryable for transactions
   */
  private isRetryableError(error: any): boolean {
    if (!error) return false;

    const retryableErrors = [
      'TransientTransactionError',
      'UnknownTransactionCommitResult',
      'WriteConflict',
      'LockTimeout'
    ];

    return retryableErrors.some(errorType =>
      error.errorLabels?.includes(errorType) ||
      error.message?.includes(errorType)
    );
  }
}

export const db = Database.getInstance();