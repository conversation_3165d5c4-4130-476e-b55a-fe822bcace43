import { Request, Response } from 'express';
import UnifiedTransactionService from '../services/unifiedTransactionService';
import logger from '../utils/logger';
import { catchAsync } from '../utils/catchAsync';

/**
 * Admin Unified Transaction Controller
 * Handles admin operations for deposits and withdrawals with real-time updates
 */
const unifiedTransactionService = new UnifiedTransactionService();

/**
 * @desc    Get all transactions for admin panel
 * @route   GET /api/admin/transactions
 * @access  Admin
 */
export const getAdminTransactions = catchAsync(async (req: Request, res: Response) => {
  try {
    const {
      type,
      status,
      cryptocurrency,
      page = 1,
      limit = 20,
      search
    } = req.query;

    const filters: any = {};
    
    if (type && ['deposit', 'withdrawal'].includes(type as string)) {
      filters.type = type as string;
    }
    if (status) {
      filters.status = status as string;
    }
    if (cryptocurrency) {
      filters.cryptocurrency = cryptocurrency as string;
    }
    if (search) {
      filters.search = search as string;
    }
    
    filters.page = parseInt(page as string);
    filters.limit = parseInt(limit as string);

    const result = await unifiedTransactionService.getAdminTransactions(filters);

    logger.info('Admin transactions retrieved', {
      adminId: req.user._id,
      filters,
      resultCount: result.transactions.length
    });

    res.json({
      success: true,
      data: {
        transactions: result.transactions,
        pagination: result.pagination
      }
    });

  } catch (error: any) {
    logger.error('Get admin transactions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get transactions',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Get deposits for admin panel
 * @route   GET /api/admin/transactions/deposits
 * @access  Admin
 */
export const getAdminDeposits = catchAsync(async (req: Request, res: Response) => {
  try {
    const {
      status,
      cryptocurrency,
      page = 1,
      limit = 20,
      search
    } = req.query;

    const filters: any = {
      type: 'deposit'
    };
    
    if (status) filters.status = status as string;
    if (cryptocurrency) filters.cryptocurrency = cryptocurrency as string;
    if (search) filters.search = search as string;
    
    filters.page = parseInt(page as string);
    filters.limit = parseInt(limit as string);

    const result = await unifiedTransactionService.getAdminTransactions(filters);

    // Transform data for admin deposits format
    const deposits = result.transactions.map(transaction => ({
      id: transaction.transactionId,
      user: {
        id: transaction.userId,
        name: (transaction as any).userId?.name || 'Unknown User',
        email: (transaction as any).userId?.email || '<EMAIL>'
      },
      cryptocurrency: transaction.cryptocurrency,
      amount: transaction.amount,
      usdValue: transaction.usdValue,
      walletAddress: transaction.walletAddress,
      txHash: transaction.txHash,
      status: transaction.status,
      adminStatus: transaction.adminStatus,
      adminNotes: transaction.adminNotes,
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
      approvedAt: transaction.approvedAt,
      metadata: transaction.metadata
    }));

    res.json({
      success: true,
      data: {
        deposits,
        pagination: result.pagination
      }
    });

  } catch (error: any) {
    logger.error('Get admin deposits error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get deposits',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Get withdrawals for admin panel
 * @route   GET /api/admin/transactions/withdrawals
 * @access  Admin
 */
export const getAdminWithdrawals = catchAsync(async (req: Request, res: Response) => {
  try {
    const {
      status,
      cryptocurrency,
      withdrawalType,
      page = 1,
      limit = 20,
      search
    } = req.query;

    const filters: any = {
      type: 'withdrawal'
    };
    
    if (status) filters.status = status as string;
    if (cryptocurrency) filters.cryptocurrency = cryptocurrency as string;
    if (search) filters.search = search as string;
    
    filters.page = parseInt(page as string);
    filters.limit = parseInt(limit as string);

    const result = await unifiedTransactionService.getAdminTransactions(filters);

    // Transform data for admin withdrawals format
    const withdrawals = result.transactions.map(transaction => ({
      id: transaction.transactionId,
      user: {
        id: transaction.userId,
        name: (transaction as any).userId?.name || 'Unknown User',
        email: (transaction as any).userId?.email || '<EMAIL>'
      },
      cryptocurrency: transaction.cryptocurrency,
      withdrawalType: transaction.subType,
      amount: transaction.amount,
      usdValue: transaction.usdValue,
      networkFee: transaction.networkFee,
      netAmount: transaction.netAmount,
      walletAddress: transaction.walletAddress,
      network: transaction.network,
      status: transaction.status,
      adminStatus: transaction.adminStatus,
      adminNotes: transaction.adminNotes,
      txHash: transaction.txHash,
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
      approvedAt: transaction.approvedAt,
      metadata: transaction.metadata
    }));

    res.json({
      success: true,
      data: {
        withdrawals,
        pagination: result.pagination
      }
    });

  } catch (error: any) {
    logger.error('Get admin withdrawals error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get withdrawals',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Approve a transaction
 * @route   PUT /api/admin/transactions/:transactionId/approve
 * @access  Admin
 */
export const approveTransaction = catchAsync(async (req: Request, res: Response) => {
  try {
    const { transactionId } = req.params;
    const { notes } = req.body;

    const transaction = await unifiedTransactionService.approveTransaction(
      transactionId,
      req.user._id.toString(),
      notes
    );

    logger.info('Transaction approved by admin', {
      transactionId,
      adminId: req.user._id,
      type: transaction.type,
      amount: transaction.amount
    });

    res.json({
      success: true,
      message: `${transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)} approved successfully`,
      data: {
        transactionId: transaction.transactionId,
        type: transaction.type,
        status: transaction.status,
        adminStatus: transaction.adminStatus,
        approvedAt: transaction.approvedAt
      }
    });

  } catch (error: any) {
    logger.error('Approve transaction error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to approve transaction',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Reject a transaction
 * @route   PUT /api/admin/transactions/:transactionId/reject
 * @access  Admin
 */
export const rejectTransaction = catchAsync(async (req: Request, res: Response) => {
  try {
    const { transactionId } = req.params;
    const { notes } = req.body;

    // For now, we'll use the same service method but with 'rejected' status
    // In a full implementation, you'd have a separate reject method
    const transaction = await unifiedTransactionService.updateTransactionAmount(
      transactionId,
      0, // Set amount to 0 for rejected transactions
      req.user._id.toString(),
      `Transaction rejected: ${notes || 'No reason provided'}`
    );

    logger.info('Transaction rejected by admin', {
      transactionId,
      adminId: req.user._id,
      notes
    });

    res.json({
      success: true,
      message: 'Transaction rejected successfully',
      data: {
        transactionId: transaction.transactionId,
        status: 'rejected',
        rejectedAt: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Reject transaction error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to reject transaction',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Update transaction amount
 * @route   PUT /api/admin/transactions/:transactionId/amount
 * @access  Admin
 */
export const updateTransactionAmount = catchAsync(async (req: Request, res: Response) => {
  try {
    const { transactionId } = req.params;
    const { amount, notes } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid amount is required'
      });
    }

    const transaction = await unifiedTransactionService.updateTransactionAmount(
      transactionId,
      parseFloat(amount),
      req.user._id.toString(),
      notes
    );

    logger.info('Transaction amount updated by admin', {
      transactionId,
      newAmount: amount,
      adminId: req.user._id,
      notes
    });

    res.json({
      success: true,
      message: 'Transaction amount updated successfully',
      data: {
        transactionId: transaction.transactionId,
        amount: transaction.amount,
        usdValue: transaction.usdValue,
        netAmount: transaction.netAmount,
        updatedAt: transaction.updatedAt
      }
    });

  } catch (error: any) {
    logger.error('Update transaction amount error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update transaction amount',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Get transaction statistics for admin dashboard
 * @route   GET /api/admin/transactions/stats
 * @access  Admin
 */
export const getTransactionStats = catchAsync(async (req: Request, res: Response) => {
  try {
    // Mock statistics - in real implementation, this would aggregate from database
    const stats = {
      deposits: {
        total: 150,
        pending: 12,
        approved: 120,
        rejected: 18,
        totalValue: 2500000
      },
      withdrawals: {
        total: 89,
        pending: 8,
        approved: 65,
        rejected: 16,
        totalValue: 1200000
      },
      dailyStats: {
        depositsToday: 8,
        withdrawalsToday: 5,
        volumeToday: 125000
      },
      cryptocurrencies: {
        BTC: { deposits: 45, withdrawals: 23, volume: 850000 },
        ETH: { deposits: 38, withdrawals: 19, volume: 620000 },
        USDT: { deposits: 67, withdrawals: 47, volume: 1230000 }
      }
    };

    res.json({
      success: true,
      data: stats
    });

  } catch (error: any) {
    logger.error('Get transaction stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get transaction statistics',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});
