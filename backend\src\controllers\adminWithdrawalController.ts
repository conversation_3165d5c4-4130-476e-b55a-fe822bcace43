import { Request, Response } from 'express';
import mongoose from 'mongoose';
import { catchAsync } from '../utils/errorHandler';
import Withdrawal from '../models/withdrawalModel';
import Wallet from '../models/walletModel';
import Transaction from '../models/transactionModel';
import User from '../models/User';
import { logger } from '../utils/logger';
import referralCommissionService from '../services/referralCommissionService';

// @desc    Get all withdrawals for admin
// @route   GET /api/admin/withdrawals
// @access  Admin
export const getAdminWithdrawals = catchAsync(async (req: Request, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const status = req.query.status as string;
  const cryptocurrency = req.query.cryptocurrency as string;
  const search = req.query.search as string;
  const withdrawalType = req.query.withdrawalType as string;

  try {
    // Build query
    const query: any = {};
    
    if (status) {
      query.status = status;
    }
    
    if (cryptocurrency) {
      query.cryptocurrency = cryptocurrency.toUpperCase();
    }
    
    if (withdrawalType) {
      query.withdrawalType = withdrawalType;
    }

    // Handle user search
    if (search) {
      const users = await User.find({
        $or: [
          { firstName: { $regex: search, $options: 'i' } },
          { lastName: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } }
        ]
      }).select('_id');

      if (users.length > 0) {
        query.userId = { $in: users.map(u => u._id) };
      } else {
        // Check if search is a withdrawal ID or wallet address
        query.$or = [
          { _id: search.length === 24 ? search : null },
          { walletAddress: { $regex: search, $options: 'i' } },
          { txHash: { $regex: search, $options: 'i' } }
        ];
      }
    }

    const skip = (page - 1) * limit;

    const [withdrawals, total] = await Promise.all([
      Withdrawal.findWithInvestmentPackage(query)
        .skip(skip)
        .limit(limit),
      Withdrawal.countDocuments(query)
    ]);

    // Format withdrawals for admin view
    const formattedWithdrawals = withdrawals.map(withdrawal => {
      const user = withdrawal.userId as any;
      const wallet = withdrawal.walletId as any;
      const investmentPackage = withdrawal.investmentPackageId as any;

      return {
        id: withdrawal._id,
        user: {
          id: user?._id || withdrawal.userId,
          name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() : 'Unknown User',
          email: user?.email || 'No email',
          phoneNumber: user?.phoneNumber || '',
          country: user?.country || ''
        },
        cryptocurrency: withdrawal.cryptocurrency,
        withdrawalType: withdrawal.withdrawalType,
        investmentPackageId: withdrawal.investmentPackageId,
        investmentPackage: investmentPackage ? {
          id: investmentPackage._id,
          amount: investmentPackage.amount,
          currency: investmentPackage.currency,
          status: investmentPackage.status,
          activeDays: investmentPackage.activeDays,
          totalEarned: investmentPackage.totalEarned,
          createdAt: investmentPackage.createdAt,
          activatedAt: investmentPackage.activatedAt
        } : null,
        wallet: wallet ? {
          id: wallet._id,
          assets: wallet.assets
        } : null,
        amount: withdrawal.amount,
        usdValue: withdrawal.usdValue,
        networkFee: withdrawal.networkFee,
        netAmount: withdrawal.netAmount,
        walletAddress: withdrawal.walletAddress,
        network: withdrawal.network,
        status: withdrawal.status,
        txHash: withdrawal.txHash,
        adminNotes: withdrawal.adminNotes,
        createdAt: withdrawal.createdAt,
        updatedAt: withdrawal.updatedAt,
        approvedAt: withdrawal.approvedAt,
        completedAt: withdrawal.completedAt,
        metadata: withdrawal.metadata
      };
    });

    res.json({
      success: true,
      data: {
        withdrawals: formattedWithdrawals,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error: any) {
    logger.error('Admin get withdrawals error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get withdrawals',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Update withdrawal status
// @route   PUT /api/admin/withdrawals/:id/status
// @access  Admin
export const updateWithdrawalStatus = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { status, adminNotes, txHash } = req.body;

  // Validate status
  if (!['pending', 'approved', 'rejected', 'completed', 'failed'].includes(status)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid status. Must be: pending, approved, rejected, completed, failed'
    });
  }

  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    let withdrawal;

    // Use appropriate method based on status change
    if (status === 'approved') {
      // Use the new static method for balance withdrawals
      const tempWithdrawal = await Withdrawal.findById(id).session(session);
      if (!tempWithdrawal) {
        await session.abortTransaction();
        session.endSession();
        return res.status(404).json({
          success: false,
          message: 'Withdrawal not found'
        });
      }

      if (tempWithdrawal.withdrawalType === 'balance') {
        // Use the new static method that handles investment package updates
        withdrawal = await Withdrawal.approveBalanceWithdrawal(id, req.user._id.toString(), adminNotes);
      } else {
        // Handle interest/commission withdrawals normally
        withdrawal = tempWithdrawal;
        withdrawal.status = 'approved';
        withdrawal.approvedAt = new Date();
        withdrawal.adminNotes = adminNotes || withdrawal.adminNotes;
        withdrawal.txHash = txHash || withdrawal.txHash;

        if (!withdrawal.metadata.adminActions) {
          withdrawal.metadata.adminActions = [];
        }
        withdrawal.metadata.adminActions.push({
          action: 'approve',
          adminId: req.user._id,
          timestamp: new Date(),
          notes: adminNotes,
          previousStatus: 'pending',
          newStatus: 'approved'
        });

        await withdrawal.save({ session });
      }
    } else if (status === 'rejected') {
      // Use the new static method for rejections
      withdrawal = await Withdrawal.rejectWithdrawal(id, req.user._id.toString(), adminNotes || 'Rejected by admin');
    } else {
      // Handle other status updates normally
      withdrawal = await Withdrawal.findById(id)
        .populate('userId', 'firstName lastName email')
        .session(session);

      if (!withdrawal) {
        await session.abortTransaction();
        session.endSession();
        return res.status(404).json({
          success: false,
          message: 'Withdrawal not found'
        });
      }

      // Prevent updating completed/failed withdrawals
      if (['completed', 'failed'].includes(withdrawal.status) && status !== withdrawal.status) {
        await session.abortTransaction();
        session.endSession();
        return res.status(400).json({
          success: false,
          message: `Cannot update withdrawal that is already ${withdrawal.status}`
        });
      }

      const previousStatus = withdrawal.status;

      // Update withdrawal
      withdrawal.status = status;
      withdrawal.adminNotes = adminNotes || withdrawal.adminNotes;
      withdrawal.txHash = txHash || withdrawal.txHash;

      if (status === 'completed' && !withdrawal.completedAt) {
        withdrawal.completedAt = new Date();
      }

      // Add admin action to metadata
      if (!withdrawal.metadata.adminActions) {
        withdrawal.metadata.adminActions = [];
      }

      withdrawal.metadata.adminActions.push({
        action: `status_update_${status}`,
        adminId: req.user._id,
        timestamp: new Date(),
        notes: adminNotes,
        previousStatus,
        newStatus: status
      });

      await withdrawal.save({ session });
    }

    // Update corresponding transaction
    await Transaction.updateOne(
      { 'metadata.withdrawalId': withdrawal._id },
      { 
        status,
        txHash: txHash || undefined,
        updatedAt: new Date()
      },
      { session }
    );
    
    // Eğer komisyon çekimi onaylandıysa, referral komisyon servisini kullanarak bakiyeyi güncelle
    if (status === 'approved' && withdrawal.withdrawalType === 'commission') {
      try {
        await referralCommissionService.processCommissionWithdrawal(
          withdrawal.userId.toString(),
          withdrawal.amount,
          withdrawal.cryptocurrency
        );
        logger.info(`Commission withdrawal processed for user ${withdrawal.userId}, amount: ${withdrawal.amount} ${withdrawal.cryptocurrency}`);
      } catch (commissionError) {
        logger.error('Error processing commission withdrawal:', commissionError);
        // İşleme devam et, hata durumunda admin manuel olarak düzeltebilir
      }
    }

    await session.commitTransaction();
    session.endSession();

    res.json({
      success: true,
      message: `Withdrawal status updated to ${status}`,
      data: {
        id: withdrawal._id,
        status: withdrawal.status,
        adminNotes: withdrawal.adminNotes,
        txHash: withdrawal.txHash,
        updatedAt: withdrawal.updatedAt
      }
    });

  } catch (error: any) {
    await session.abortTransaction();
    session.endSession();
    logger.error('Update withdrawal status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update withdrawal status',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }

});

// Helper method to restore funds when withdrawal is rejected
async function restoreFunds(withdrawal: any, session: any) {
    try {
      // Since funds are now deducted immediately upon withdrawal request,
      // we need to restore them to the wallet for all withdrawal types
      const wallet = await Wallet.findById(withdrawal.walletId).session(session);
      if (!wallet) {
        throw new Error('Wallet not found for fund restoration');
      }

      const assetIndex = wallet.assets.findIndex(a => a.symbol === withdrawal.cryptocurrency);
      if (assetIndex === -1) {
        throw new Error(`Asset ${withdrawal.cryptocurrency} not found in wallet`);
      }

      // Restore funds to appropriate balance based on withdrawal type
      switch (withdrawal.withdrawalType) {
        case 'balance':
          wallet.assets[assetIndex].balance = (wallet.assets[assetIndex].balance || 0) + withdrawal.amount;
          break;
        case 'interest':
          wallet.assets[assetIndex].interestBalance = (wallet.assets[assetIndex].interestBalance || 0) + withdrawal.amount;
          break;
        case 'commission':
          wallet.assets[assetIndex].commissionBalance = (wallet.assets[assetIndex].commissionBalance || 0) + withdrawal.amount;
          break;
      }

      await wallet.save({ session });

      // For balance withdrawals, also restore investment package if needed
      if (withdrawal.withdrawalType === 'balance' && withdrawal.investmentPackageId) {
        const InvestmentPackage = mongoose.model('InvestmentPackage');
        const investmentPackage = await InvestmentPackage.findById(withdrawal.investmentPackageId).session(session);

        if (investmentPackage) {
          // Restore amount to investment package
          investmentPackage.amount += withdrawal.amount;

          // Restore status if it was changed to withdrawn
          if (investmentPackage.status === 'withdrawn') {
            investmentPackage.status = 'active';
            investmentPackage.withdrawnAt = undefined;
          }

          await investmentPackage.save({ session });

          logger.info('Funds restored to both wallet and investment package for rejected withdrawal', {
            withdrawalId: withdrawal._id,
            investmentPackageId: withdrawal.investmentPackageId,
            amount: withdrawal.amount
          });
        }
      }

      logger.info('Funds restored to wallet for rejected withdrawal', {
        withdrawalId: withdrawal._id,
        userId: withdrawal.userId,
        cryptocurrency: withdrawal.cryptocurrency,
        amount: withdrawal.amount,
        withdrawalType: withdrawal.withdrawalType
      });

    } catch (error) {
      logger.error('Fund restoration error:', error);
      throw new Error(`Failed to restore funds: ${error.message}`);
    }
  }

// @desc    Update withdrawal amount
// @route   PUT /api/admin/withdrawals/:id/amount
// @access  Admin
export const updateWithdrawalAmount = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { amount, adminNotes } = req.body;

  if (!amount || amount <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid amount is required'
    });
  }

  try {
    const withdrawal = await Withdrawal.findById(id);

    if (!withdrawal) {
      return res.status(404).json({
        success: false,
        message: 'Withdrawal not found'
      });
    }

    // Allow unlimited amount modifications for all withdrawal statuses
    // No status restrictions - admin can edit amounts at any time
    logger.info(`Admin ${req.user._id} is modifying amount for withdrawal ${id} with status: ${withdrawal.status}`);

    const previousAmount = withdrawal.amount;
    withdrawal.amount = parseFloat(amount);
    withdrawal.netAmount = withdrawal.amount - withdrawal.networkFee;
    withdrawal.adminNotes = adminNotes || withdrawal.adminNotes;

    // Add admin action to metadata
    if (!withdrawal.metadata.adminActions) {
      withdrawal.metadata.adminActions = [];
    }

    withdrawal.metadata.adminActions.push({
      action: 'amount_update',
      adminId: req.user._id,
      timestamp: new Date(),
      notes: `Amount updated from ${previousAmount} to ${amount}. ${adminNotes || ''}`,
      previousStatus: withdrawal.status,
      newStatus: withdrawal.status
    });

    await withdrawal.save();

    // Log audit trail for withdrawal amount modification
    try {
      const AuditLoggingService = require('../services/AuditLoggingService').default;
      await AuditLoggingService.logAdminOperation(
        req.user._id.toString(),
        'WITHDRAWAL_AMOUNT_UPDATE',
        withdrawal.userId.toString(),
        true,
        {
          withdrawalId: withdrawal._id,
          previousAmount,
          newAmount: amount,
          netAmount: withdrawal.netAmount,
          cryptocurrency: withdrawal.cryptocurrency,
          withdrawalType: withdrawal.withdrawalType,
          walletAddress: withdrawal.walletAddress,
          network: withdrawal.network,
          withdrawalStatus: withdrawal.status,
          adminNotes,
          adminEmail: req.user?.email,
          timestamp: new Date().toISOString(),
          userAgent: req.headers['user-agent'],
          sessionId: req.sessionID
        },
        req.ip
      );
    } catch (auditError) {
      logger.error('Failed to log audit trail for withdrawal amount update:', auditError);
    }

    // Emit real-time update for cross-section synchronization
    try {
      const io = req.app?.get('io');
      if (io) {
        io.emit('admin_withdrawal_amount_updated', {
          withdrawalId: withdrawal._id,
          userId: withdrawal.userId,
          previousAmount,
          newAmount: amount,
          netAmount: withdrawal.netAmount,
          cryptocurrency: withdrawal.cryptocurrency,
          withdrawalType: withdrawal.withdrawalType,
          modifiedAt: new Date(),
          modifiedBy: req.user._id,
          adminNotes
        });
      }
    } catch (socketError) {
      logger.error('Failed to emit withdrawal amount update event:', socketError);
    }

    logger.info('Withdrawal amount updated', {
      withdrawalId: id,
      previousAmount,
      newAmount: amount,
      adminId: req.user._id
    });

    res.json({
      success: true,
      message: 'Withdrawal amount updated successfully',
      data: {
        id: withdrawal._id,
        amount: withdrawal.amount,
        netAmount: withdrawal.netAmount,
        adminNotes: withdrawal.adminNotes,
        modifiedAt: new Date(),
        modifiedBy: req.user._id
      }
    });

  } catch (error: any) {
    logger.error('Update withdrawal amount error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update withdrawal amount',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get withdrawal statistics for admin
// @route   GET /api/admin/withdrawals/stats
// @access  Admin
export const getWithdrawalStats = catchAsync(async (req: Request, res: Response) => {
  try {
    const stats = await Withdrawal.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          totalUsdValue: { $sum: '$usdValue' }
        }
      }
    ]);

    const cryptoStats = await Withdrawal.aggregate([
      {
        $group: {
          _id: '$cryptocurrency',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          totalUsdValue: { $sum: '$usdValue' }
        }
      }
    ]);

    const typeStats = await Withdrawal.aggregate([
      {
        $group: {
          _id: '$withdrawalType',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          totalUsdValue: { $sum: '$usdValue' }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        statusStats: stats,
        cryptoStats,
        typeStats
      }
    });

  } catch (error: any) {
    logger.error('Get withdrawal stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get withdrawal statistics',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});
