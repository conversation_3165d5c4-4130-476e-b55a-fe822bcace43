import { Request, Response } from 'express';
import enhancedCryptoService from '../services/enhancedCryptoService';
import cryptoTimeService from '../services/cryptoTimeService';
import InvestmentPackage from '../models/investmentPackageModel';
import UserWallet from '../models/userWalletModel';
import { catchAsync } from '../utils/catchAsync';

/**
 * Get user's crypto balances and investment overview
 */
export const getCryptoBalances = catchAsync(async (req: Request, res: Response) => {
  const userId = req.user?.id;

  if (!userId) {
    return res.status(401).json({
      status: 'error',
      message: 'User not authenticated'
    });
  }

  try {
    const balances = await enhancedCryptoService.getUserCryptoBalances(userId);
    const summary = await enhancedCryptoService.getInvestmentSummary(userId);
    const timeLockStatus = cryptoTimeService.getTimeLockStatus();

    res.json({
      status: 'success',
      data: {
        balances,
        summary,
        timeLockStatus,
        supportedCurrencies: ['BTC', 'ETH', 'USDT', 'BNB', 'SOL']
      }
    });
  } catch (error: any) {
    console.error('Error fetching crypto balances:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch crypto balances',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Get investment packages with enhanced details
 */
export const getEnhancedInvestmentPackages = catchAsync(async (req: Request, res: Response) => {
  const userId = req.user?.id;

  if (!userId) {
    return res.status(401).json({
      status: 'error',
      message: 'User not authenticated'
    });
  }

  try {
    const packages = await InvestmentPackage.find({ userId })
      .sort({ createdAt: -1 })
      .lean();

    // Enhance packages with real-time data
    const enhancedPackages = packages.map(pkg => {
      const timeUntilNext = cryptoTimeService.getTimeUntilNextInterest();
      const canWithdraw = pkg.realTimeUSDTValue >= 50 && cryptoTimeService.isWithdrawalTimeAllowed();
      
      return {
        ...pkg,
        timeUntilNextInterest: timeUntilNext,
        canWithdraw,
        withdrawalTimeStatus: cryptoTimeService.getTimeUntilWithdrawalAllowed()
      };
    });

    res.json({
      status: 'success',
      data: {
        packages: enhancedPackages,
        totalPackages: packages.length,
        activePackages: packages.filter(pkg => pkg.status === 'active').length
      }
    });
  } catch (error: any) {
    console.error('Error fetching investment packages:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch investment packages',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Validate withdrawal eligibility
 */
export const validateWithdrawal = catchAsync(async (req: Request, res: Response) => {
  const userId = req.user?.id;
  const { packageId } = req.params;

  if (!userId) {
    return res.status(401).json({
      status: 'error',
      message: 'User not authenticated'
    });
  }

  try {
    const validation = await enhancedCryptoService.validateWithdrawal(userId, packageId);

    res.json({
      status: 'success',
      data: validation
    });
  } catch (error: any) {
    console.error('Error validating withdrawal:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to validate withdrawal',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Process crypto deposit
 */
export const processDeposit = catchAsync(async (req: Request, res: Response) => {
  const userId = req.user?.id;
  const { currency, amount, transactionHash, walletAddress } = req.body;

  if (!userId) {
    return res.status(401).json({
      status: 'error',
      message: 'User not authenticated'
    });
  }

  // Validate input
  if (!currency || !amount || !transactionHash || !walletAddress) {
    return res.status(400).json({
      status: 'error',
      message: 'Missing required fields: currency, amount, transactionHash, walletAddress'
    });
  }

  if (!['BTC', 'ETH', 'USDT', 'BNB', 'SOL'].includes(currency.toUpperCase())) {
    return res.status(400).json({
      status: 'error',
      message: 'Unsupported currency'
    });
  }

  if (amount <= 0) {
    return res.status(400).json({
      status: 'error',
      message: 'Amount must be greater than 0'
    });
  }

  try {
    const result = await enhancedCryptoService.processDeposit({
      userId,
      currency: currency.toUpperCase(),
      amount: parseFloat(amount),
      transactionHash,
      walletAddress
    });

    res.status(201).json({
      status: 'success',
      message: 'Deposit processed successfully',
      data: {
        depositTransaction: result.depositTransaction,
        investmentPackage: result.investmentPackage
      }
    });
  } catch (error: any) {
    console.error('Error processing deposit:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to process deposit',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Get time lock status and next interest calculation
 */
export const getTimeLockStatus = catchAsync(async (req: Request, res: Response) => {
  try {
    const timeLockStatus = cryptoTimeService.getTimeLockStatus();
    const nextInterestTime = cryptoTimeService.getNextInterestTime();
    const timeUntilNext = cryptoTimeService.getTimeUntilNextInterest();
    const withdrawalStatus = cryptoTimeService.getTimeUntilWithdrawalAllowed();

    res.json({
      status: 'success',
      data: {
        ...timeLockStatus,
        nextInterestTime,
        timeUntilNextInterest: timeUntilNext,
        withdrawalStatus,
        currentTurkeyTime: cryptoTimeService.getTurkeyTime()
      }
    });
  } catch (error: any) {
    console.error('Error getting time lock status:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get time lock status',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Get investment summary and statistics
 */
export const getInvestmentSummary = catchAsync(async (req: Request, res: Response) => {
  const userId = req.user?.id;

  if (!userId) {
    return res.status(401).json({
      status: 'error',
      message: 'User not authenticated'
    });
  }

  try {
    const summary = await enhancedCryptoService.getInvestmentSummary(userId);
    const balances = await enhancedCryptoService.getUserCryptoBalances(userId);
    
    // Calculate additional statistics
    const totalCryptoValue = balances.reduce((sum, balance) => sum + balance.usdtValue, 0);
    const totalDailyInterest = balances.reduce((sum, balance) => sum + balance.dailyInterest, 0);

    res.json({
      status: 'success',
      data: {
        ...summary,
        totalCryptoValue,
        totalDailyInterest,
        cryptoBreakdown: balances.map(balance => ({
          currency: balance.symbol,
          balance: balance.balance,
          usdtValue: balance.usdtValue,
          dailyInterest: balance.dailyInterest,
          percentage: totalCryptoValue > 0 ? (balance.usdtValue / totalCryptoValue) * 100 : 0
        }))
      }
    });
  } catch (error: any) {
    console.error('Error getting investment summary:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get investment summary',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Generate deposit address for a specific currency
 */
export const generateDepositAddress = catchAsync(async (req: Request, res: Response) => {
  const userId = req.user?.id;
  const { currency } = req.params;

  if (!userId) {
    return res.status(401).json({
      status: 'error',
      message: 'User not authenticated'
    });
  }

  if (!['BTC', 'ETH', 'USDT', 'BNB', 'SOL'].includes(currency.toUpperCase())) {
    return res.status(400).json({
      status: 'error',
      message: 'Unsupported currency'
    });
  }

  try {
    // Check if wallet already exists
    let wallet = await UserWallet.findOne({ 
      userId, 
      currency: currency.toUpperCase() 
    });

    if (!wallet) {
      // Create new wallet (this would integrate with actual wallet generation service)
      const mockAddress = `${currency.toLowerCase()}1qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlh`;
      
      wallet = new UserWallet({
        userId,
        currency: currency.toUpperCase(),
        address: mockAddress,
        balance: 0,
        isActive: true,
        network: 'mainnet',
        addressIndex: 0
      });

      await wallet.save();
    }

    // Generate QR code URL
    const qrCodeUrl = wallet.generateQRCode();

    res.json({
      status: 'success',
      data: {
        currency: currency.toUpperCase(),
        address: wallet.address,
        qrCodeUrl,
        network: wallet.network,
        isActive: wallet.isActive
      }
    });
  } catch (error: any) {
    console.error('Error generating deposit address:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to generate deposit address',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Calculate interest preview for deposit amount
 */
export const calculateInterestPreview = catchAsync(async (req: Request, res: Response) => {
  const { currency, amount } = req.query;

  if (!currency || !amount) {
    return res.status(400).json({
      status: 'error',
      message: 'Currency and amount are required'
    });
  }

  const depositAmount = parseFloat(amount as string);
  if (depositAmount <= 0) {
    return res.status(400).json({
      status: 'error',
      message: 'Amount must be greater than 0'
    });
  }

  try {
    const dailyInterest = depositAmount * 0.01; // 1% daily
    const weeklyInterest = dailyInterest * 7;
    const monthlyInterest = dailyInterest * 30;

    res.json({
      status: 'success',
      data: {
        currency: currency.toString().toUpperCase(),
        principalAmount: depositAmount,
        interestRate: 0.01,
        preview: {
          daily: {
            amount: dailyInterest,
            percentage: 1
          },
          weekly: {
            amount: weeklyInterest,
            percentage: 7
          },
          monthly: {
            amount: monthlyInterest,
            percentage: 30
          }
        },
        projectedReturns: {
          after30Days: depositAmount + monthlyInterest,
          after90Days: depositAmount + (dailyInterest * 90),
          after365Days: depositAmount + (dailyInterest * 365)
        }
      }
    });
  } catch (error: any) {
    console.error('Error calculating interest preview:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to calculate interest preview',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});
