import { Request, Response } from 'express';
import Investment from '../models/investmentModel';
import Wallet from '../models/walletModel';
import Transaction from '../models/transactionModel';
import SystemConfig from '../models/systemConfigModel';
import { logger } from '../utils/logger';
import referralCommissionService from '../services/referralCommissionService';

// No need to extend Express Request as <PERSON><PERSON> already does this

// Helper function to get the next available address for a currency
const getNextAddress = async (currency: string, network?: string): Promise<string> => {
  try {
    // Get system config
    const config = await SystemConfig.findOneOrCreate();

    // Find the crypto address configuration for the currency
    const cryptoAddressConfig = config.cryptoAddresses.find(
      (ca) => ca.currency === currency.toUpperCase() && ca.enabled
    );

    if (!cryptoAddressConfig || cryptoAddressConfig.addresses.length === 0) {
      logger.warn(`No addresses found for currency: ${currency}`);
      return '';
    }

    // Get the next address
    const addressEntry = cryptoAddressConfig.addresses[cryptoAddressConfig.currentIndex];

    // Update the current index
    cryptoAddressConfig.currentIndex =
      (cryptoAddressConfig.currentIndex + 1) % cryptoAddressConfig.addresses.length;

    // Save the updated config
    await config.save();

    // Handle different address formats (string or object with network)
    if (typeof addressEntry === 'string') {
      return addressEntry;
    } else if (typeof addressEntry === 'object' && addressEntry !== null) {
      // If network is specified, try to find an address for that network
      if (network && 'network' in addressEntry && addressEntry.network === network) {
        return addressEntry.address;
      }
      // Otherwise return the address property
      if ('address' in addressEntry) {
        return addressEntry.address;
      }
    }

    logger.warn(`Invalid address format for currency: ${currency}`);
    return '';
  } catch (error) {
    logger.error('Error getting next address:', error);
    return '';
  }
};

// @desc    Create a new investment
// @route   POST /api/investments
// @access  Private
export const createInvestment = async (req: Request, res: Response): Promise<void> => {
  try {
    const { currency, amount, description, network } = req.body;

    // Validate input
    if (!currency || !amount || amount <= 0) {
      res.status(400).json({
        message: 'Invalid input',
        errors: {
          currency: !currency ? 'Currency is required' : undefined,
          amount: !amount ? 'Amount is required' : amount <= 0 ? 'Amount must be greater than 0' : undefined
        }
      });
      return;
    }

    // Get system config to check if deposits are enabled
    const config = await SystemConfig.findOneOrCreate();
    if (!config.depositsEnabled) {
      res.status(403).json({ message: 'Deposits are currently disabled' });
      return;
    }

    // Check if the currency is supported
    if (!config.supportedCurrencies.includes(currency.toUpperCase())) {
      res.status(400).json({ message: 'Currency not supported' });
      return;
    }

    // Get crypto address for the investment
    const cryptoAddress = await getNextAddress(currency, network);
    if (!cryptoAddress) {
      res.status(400).json({ message: 'No available addresses for this currency' });
      return;
    }

    // Create investment
    const investment = await Investment.create({
      userId: req.user._id,
      currency: currency.toUpperCase(),
      amount,
      description,
      cryptoAddress,
      network: network || 'default',
      status: 'pending'
    });

    logger.info(`New investment created: ${investment._id} for user: ${req.user._id}`);

    // AUTO-INTEGRATION: Add investment to unified transaction system for admin panel visibility
    try {
      const Transaction = (await import('../models/transactionModel')).default;

      const unifiedTransaction = await Transaction.create({
        userId: req.user._id,
        type: 'deposit',
        source: 'investment',
        amount: investment.amount,
        currency: investment.currency,
        status: investment.status,
        cryptoAddress: investment.cryptoAddress,
        network: investment.network,
        description: investment.description || 'Investment deposit',
        investmentId: investment._id,
        transactionHash: `investment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createdAt: investment.createdAt,
        updatedAt: investment.updatedAt
      });

      logger.info(`Investment automatically added to unified transaction system: ${unifiedTransaction._id}`);
    } catch (unifiedError) {
      logger.warn('Failed to add investment to unified transaction system:', unifiedError);
      // Don't fail the investment creation if unified system fails
    }

    res.status(201).json({
      success: true,
      message: 'Investment created successfully',
      data: {
        investment
      }
    });

  } catch (error: any) {
    logger.error('Investment creation error:', error);
    res.status(500).json({
      message: 'An error occurred while creating the investment',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Upload investment receipt
// @route   POST /api/investments/:id/receipt
// @access  Private
export const uploadReceipt = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // Check if file was uploaded
    if (!req.file) {
      res.status(400).json({ message: 'No file uploaded' });
      return;
    }

    // Find investment
    const investment = await Investment.findOne({ _id: id, userId: req.user._id });
    if (!investment) {
      res.status(404).json({ message: 'Investment not found' });
      return;
    }

    // Check if investment is in a valid state for uploading receipt
    if (investment.status !== 'pending') {
      res.status(400).json({ message: 'Cannot upload receipt for non-pending investment' });
      return;
    }

    // Save receipt URL with absolute path
    const receiptUrl = `/uploads/receipts/${req.file.filename}`;
    investment.receiptUrl = receiptUrl;

    // Log the receipt URL for debugging
    logger.info(`Receipt URL saved: ${receiptUrl} for investment: ${investment._id}`);

    // Get additional data from form if available
    if (req.body.txHash) {
      investment.txHash = req.body.txHash;
    }

    // Get additional data from form
    if (req.body.amount) {
      // Validate amount
      const amount = parseFloat(req.body.amount);
      if (!isNaN(amount) && amount > 0) {
        investment.amount = amount;
      }
    }

    if (req.body.currency) {
      investment.currency = req.body.currency.toUpperCase();
    }

    if (req.body.network) {
      investment.network = req.body.network;
    }

    if (req.body.description) {
      investment.description = req.body.description;
    }

    // Update status to 'processing' since we now have a receipt
    investment.status = 'processing';

    // Save the updated investment
    await investment.save();

    // Find or create wallet for the user
    const Wallet = require('../models/walletModel').default;
    let wallet = await Wallet.findOne({ userId: req.user._id });

    if (!wallet) {
      wallet = await Wallet.create({
        userId: req.user._id,
        assets: [
          {
            symbol: investment.currency,
            balance: 0,
            commissionBalance: 0,
            interestBalance: 0,
            network: investment.network || 'default'
          }
        ],
        totalCommissionEarned: 0,
        totalInterestEarned: 0
      });

      logger.info(`Auto-created wallet for user: ${req.user._id} during deposit receipt upload`);
    } else {
      // Kiểm tra symbol đã có trong assets chưa
      const exists = wallet.assets.some(
        asset => asset.symbol === investment.currency
      );

      if (!exists) {
        wallet.assets.push({
          symbol: investment.currency,
          balance: 0,
          commissionBalance: 0,
          interestBalance: 0,
        });

        await wallet.save();
        logger.info(`Added new asset ${investment.currency} to wallet of user: ${req.user._id}`);
      }
    }

    // Create a transaction record for this deposit
    const transaction = await Transaction.create({
      userId: req.user._id,
      walletId: wallet._id,
      walletAddress: investment.cryptoAddress, // Fix: Add the missing walletAddress field
      type: 'deposit',
      asset: investment.currency,
      amount: investment.amount,
      status: 'pending',
      description: req.body.description || `Deposit of ${investment.amount} ${investment.currency}`,
      txHash: investment.txHash || '',
      investmentId: investment._id,
      blockchainNetwork: investment.network === 'ethereum' ? 'ethereum' :
        investment.network === 'bsc' ? 'bsc' :
          investment.network === 'tron' ? 'tron' : undefined,
      metadata: {
        network: investment.network,
        receiptUrl: investment.receiptUrl
      }
    });

    // Notify admins about the new deposit with receipt
    const { notificationService } = require('../services/notificationService');
    await notificationService.notifyAdminsAboutDeposit({
      ...transaction.toObject(),
      userName: `${req.user.firstName || ''} ${req.user.lastName || ''}`.trim(),
      userEmail: req.user.email,
      receiptUrl: investment.receiptUrl,
      network: investment.network
    });

    logger.info(`Receipt uploaded for investment: ${investment._id}, transaction created: ${transaction._id}`);

    res.status(200).json({
      success: true,
      message: 'Receipt uploaded successfully and transaction created',
      data: {
        investment: {
          _id: investment._id,
          status: investment.status,
          receiptUrl: investment.receiptUrl,
          amount: investment.amount,
          currency: investment.currency
        },
        transaction: {
          _id: transaction._id,
          status: transaction.status,
          type: transaction.type
        }
      }
    });

  } catch (error: any) {
    logger.error('Receipt upload error:', error);
    res.status(500).json({
      message: 'An error occurred while uploading the receipt',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Get all investments for a user
// @route   GET /api/investments
// @access  Private
export const getInvestments = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const status = req.query.status as string;
    const currency = req.query.currency as string;
    const grouped = req.query.grouped === 'true'; // Thêm tham số grouped

    const query: any = { userId: req.user._id };

    // Apply filters
    if (status) query.status = status;
    if (currency) query.currency = currency;

    // Nếu không yêu cầu nhóm, trả về danh sách thông thường
    if (!grouped) {
      const [investments, total] = await Promise.all([
        Investment.find(query)
          .sort({ createdAt: -1 })
          .skip((page - 1) * limit)
          .limit(limit),
        Investment.countDocuments(query)
      ]);

      res.json({
        investments,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      });
      return;
    }

    // Nếu yêu cầu nhóm, thực hiện nhóm theo loại tiền tệ
    // Lấy tất cả các khoản đầu tư
    const allInvestments = await Investment.find(query).sort({ createdAt: -1 });

    // Nhóm các khoản đầu tư theo loại tiền tệ
    const groupedInvestments = groupInvestmentsByCurrency(allInvestments);

    res.json({
      groupedInvestments,
      investments: allInvestments, // Vẫn trả về danh sách gốc để tương thích ngược
      total: allInvestments.length
    });

  } catch (error: any) {
    logger.error('Investments fetch error:', error);
    res.status(500).json({
      message: 'An error occurred while fetching investments',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Hàm nhóm các khoản đầu tư theo loại tiền tệ
const groupInvestmentsByCurrency = (investments: any[]): any[] => {
  // Lọc chỉ lấy các khoản đầu tư đã được phê duyệt
  const approvedInvestments = investments.filter(inv => inv.status === 'approved');

  // Nhóm theo loại tiền tệ
  const groupedByCurrency: Record<string, any[]> = {};

  approvedInvestments.forEach(investment => {
    // Chuẩn hóa currency để đảm bảo nhóm đúng (viết hoa)
    const currency = investment.currency.toUpperCase();

    if (!groupedByCurrency[currency]) {
      groupedByCurrency[currency] = [];
    }
    groupedByCurrency[currency].push(investment);
  });

  // Chuyển đổi thành mảng kết quả
  return Object.entries(groupedByCurrency).map(([currency, investments]) => {
    // Sắp xếp theo ngày (cũ nhất trước) để lấy ngày đầu tư đầu tiên
    const sortedInvestments = [...investments].sort(
      (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );

    // Tính tổng số tiền
    const totalAmount = investments.reduce((sum, inv) => sum + inv.amount, 0);

    // Tất cả các khoản đầu tư đều đã được phê duyệt vì chúng ta đã lọc ở trên
    const status = 'approved';

    // Lấy các mạng duy nhất
    const networks = [...new Set(investments.map(inv => inv.network || 'Default'))];

    // Lấy các địa chỉ duy nhất
    const addresses = [...new Set(investments.map(inv => inv.cryptoAddress))];

    return {
      currency,
      totalAmount,
      investments,
      firstInvestmentDate: sortedInvestments[0].createdAt,
      status,
      networks,
      addresses,
    };
  });
};

// @desc    Get investment by ID
// @route   GET /api/investments/:id
// @access  Private
export const getInvestmentById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const investment = await Investment.findOne({ _id: id, userId: req.user._id });
    if (!investment) {
      res.status(404).json({ message: 'Investment not found' });
      return;
    }

    res.json({ investment });

  } catch (error: any) {
    logger.error('Investment fetch error:', error);
    res.status(500).json({
      message: 'An error occurred while fetching the investment',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Update investment transaction hash
// @route   PUT /api/investments/:id/txhash
// @access  Private
export const updateTransactionHash = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { txHash } = req.body;

    if (!txHash) {
      res.status(400).json({ message: 'Transaction hash is required' });
      return;
    }

    const investment = await Investment.findOne({ _id: id, userId: req.user._id });
    if (!investment) {
      res.status(404).json({ message: 'Investment not found' });
      return;
    }

    // Check if investment is in a valid state for updating txHash
    if (investment.status !== 'pending' && investment.status !== 'processing') {
      res.status(400).json({ message: 'Cannot update transaction hash for investments that are not in pending or processing state' });
      return;
    }

    // Update transaction hash
    investment.txHash = txHash;
    await investment.save();

    logger.info(`Transaction hash updated for investment: ${investment._id}`);

    res.status(200).json({
      message: 'Transaction hash updated successfully',
      investment
    });

  } catch (error: any) {
    logger.error('Transaction hash update error:', error);
    res.status(500).json({
      message: 'An error occurred while updating the transaction hash',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

export default {
  createInvestment,
  uploadReceipt,
  getInvestments,
  getInvestmentById,
  updateTransactionHash
};
