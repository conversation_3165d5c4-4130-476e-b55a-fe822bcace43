import { Request, Response } from 'express';
import UnifiedTransactionService from '../services/unifiedTransactionService';
import EnhancedWalletService from '../services/enhancedWalletService';
import logger from '../utils/logger';
import { catchAsync } from '../utils/catchAsync';

/**
 * Unified Transaction Controller
 * Handles all deposit and withdrawal operations with real-time updates
 */
const unifiedTransactionService = new UnifiedTransactionService();
const enhancedWalletService = new EnhancedWalletService();

/**
 * @desc    Create a new deposit transaction
 * @route   POST /api/transactions/deposit
 * @access  Private
 */
export const createDeposit = catchAsync(async (req: Request, res: Response) => {
  try {
    const {
      cryptocurrency,
      network,
      amount,
      walletAddress,
      txHash,
      autoInvestmentEnabled
    } = req.body;

    // Validation
    if (!cryptocurrency || !network || !amount || !walletAddress) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: cryptocurrency, network, amount, walletAddress'
      });
    }

    if (amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Amount must be greater than 0'
      });
    }

    // Create or get user wallet
    const wallet = await enhancedWalletService.createOrUpdateWallet(
      req.user._id.toString(),
      cryptocurrency
    );

    // Create deposit transaction
    const transaction = await unifiedTransactionService.createDeposit({
      userId: req.user._id.toString(),
      walletId: wallet._id.toString(),
      cryptocurrency,
      network,
      amount: parseFloat(amount),
      walletAddress,
      txHash,
      autoInvestmentEnabled: autoInvestmentEnabled ?? true
    });

    logger.info('Deposit transaction created successfully', {
      transactionId: transaction.transactionId,
      userId: req.user._id,
      amount,
      cryptocurrency
    });

    res.status(201).json({
      success: true,
      message: 'Deposit transaction created successfully',
      data: {
        transactionId: transaction.transactionId,
        amount: transaction.amount,
        cryptocurrency: transaction.cryptocurrency,
        status: transaction.status,
        adminStatus: transaction.adminStatus,
        createdAt: transaction.createdAt
      }
    });

  } catch (error: any) {
    logger.error('Create deposit error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create deposit transaction',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Create a new withdrawal transaction
 * @route   POST /api/transactions/withdrawal
 * @access  Private
 */
export const createWithdrawal = catchAsync(async (req: Request, res: Response) => {
  try {
    const {
      cryptocurrency,
      network,
      amount,
      walletAddress,
      withdrawalType,
      investmentPackageId
    } = req.body;

    // Validation
    if (!cryptocurrency || !network || !amount || !walletAddress || !withdrawalType) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: cryptocurrency, network, amount, walletAddress, withdrawalType'
      });
    }

    if (amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Amount must be greater than 0'
      });
    }

    if (!['balance', 'interest', 'commission'].includes(withdrawalType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid withdrawal type. Must be: balance, interest, or commission'
      });
    }

    // Validate wallet address
    if (!enhancedWalletService.validateWalletAddress(walletAddress, cryptocurrency, network)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid wallet address format'
      });
    }

    // Get user wallet
    const wallet = await enhancedWalletService.createOrUpdateWallet(
      req.user._id.toString(),
      cryptocurrency
    );

    // Create withdrawal transaction
    const transaction = await unifiedTransactionService.createWithdrawal({
      userId: req.user._id.toString(),
      walletId: wallet._id.toString(),
      cryptocurrency,
      network,
      amount: parseFloat(amount),
      walletAddress,
      withdrawalType,
      investmentPackageId
    });

    logger.info('Withdrawal transaction created successfully', {
      transactionId: transaction.transactionId,
      userId: req.user._id,
      amount,
      cryptocurrency,
      withdrawalType
    });

    res.status(201).json({
      success: true,
      message: 'Withdrawal transaction created successfully',
      data: {
        transactionId: transaction.transactionId,
        amount: transaction.amount,
        netAmount: transaction.netAmount,
        networkFee: transaction.networkFee,
        cryptocurrency: transaction.cryptocurrency,
        status: transaction.status,
        adminStatus: transaction.adminStatus,
        createdAt: transaction.createdAt
      }
    });

  } catch (error: any) {
    logger.error('Create withdrawal error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create withdrawal transaction',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Get user transaction history
 * @route   GET /api/transactions/history
 * @access  Private
 */
export const getTransactionHistory = catchAsync(async (req: Request, res: Response) => {
  try {
    const {
      type,
      cryptocurrency,
      page = 1,
      limit = 20
    } = req.query;

    const filters: any = {};
    if (type && ['deposit', 'withdrawal'].includes(type as string)) {
      filters.type = type as string;
    }
    if (cryptocurrency) {
      filters.cryptocurrency = cryptocurrency as string;
    }
    filters.page = parseInt(page as string);
    filters.limit = parseInt(limit as string);

    const result = await unifiedTransactionService.getUserTransactions(
      req.user._id.toString(),
      filters
    );

    res.json({
      success: true,
      data: {
        transactions: result.transactions,
        pagination: result.pagination
      }
    });

  } catch (error: any) {
    logger.error('Get transaction history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get transaction history',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Get transaction details
 * @route   GET /api/transactions/:transactionId
 * @access  Private
 */
export const getTransactionDetails = catchAsync(async (req: Request, res: Response) => {
  try {
    const { transactionId } = req.params;

    // In a real implementation, you would fetch the transaction from the database
    // For now, we'll return a mock response
    res.json({
      success: true,
      message: 'Transaction details retrieved successfully',
      data: {
        transactionId,
        message: 'Transaction details would be returned here'
      }
    });

  } catch (error: any) {
    logger.error('Get transaction details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get transaction details',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Get user wallet balances
 * @route   GET /api/transactions/wallet/balances
 * @access  Private
 */
export const getWalletBalances = catchAsync(async (req: Request, res: Response) => {
  try {
    // Try to get real wallet data first
    let balances, stats;

    try {
      // Import wallet model
      const Wallet = (await import('../models/walletModel')).default;

      // Get user's wallet
      const wallet = await Wallet.findOne({ userId: req.user._id });

      if (wallet && wallet.assets && wallet.assets.length > 0) {
        // Convert wallet assets to unified format
        balances = {};
        wallet.assets.forEach((asset: any) => {
          balances[asset.symbol] = {
            balance: asset.balance || 0,
            interestBalance: asset.interestBalance || 0,
            commissionBalance: asset.commissionBalance || 0
          };
        });

        // Calculate stats
        stats = await enhancedWalletService.getWalletStats(req.user._id.toString());

        logger.info(`Real wallet data found for user ${req.user._id}:`, {
          assetsCount: wallet.assets.length,
          totalCommission: wallet.totalCommissionEarned,
          totalInterest: wallet.totalInterestEarned
        });
      } else {
        // No real wallet data, use enhanced service mock data
        balances = await enhancedWalletService.getUserBalances(req.user._id.toString());
        stats = await enhancedWalletService.getWalletStats(req.user._id.toString());

        logger.info(`Using mock wallet data for user ${req.user._id}`);
      }
    } catch (walletError) {
      // Fallback to enhanced service
      logger.warn('Error getting real wallet data, using enhanced service:', walletError);
      balances = await enhancedWalletService.getUserBalances(req.user._id.toString());
      stats = await enhancedWalletService.getWalletStats(req.user._id.toString());
    }

    res.json({
      success: true,
      data: {
        balances,
        stats,
        lastUpdated: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Get wallet balances error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get wallet balances',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Get withdrawable balance for specific cryptocurrency
 * @route   GET /api/transactions/wallet/withdrawable/:cryptocurrency
 * @access  Private
 */
export const getWithdrawableBalance = catchAsync(async (req: Request, res: Response) => {
  try {
    const { cryptocurrency } = req.params;
    const { type = 'balance' } = req.query;

    if (!['balance', 'interest', 'commission'].includes(type as string)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid withdrawal type. Must be: balance, interest, or commission'
      });
    }

    const withdrawableBalance = await enhancedWalletService.getWithdrawableBalance(
      req.user._id.toString(),
      cryptocurrency.toUpperCase(),
      type as 'balance' | 'interest' | 'commission'
    );

    res.json({
      success: true,
      data: {
        cryptocurrency: cryptocurrency.toUpperCase(),
        withdrawalType: type,
        withdrawableBalance,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Get withdrawable balance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get withdrawable balance',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Sync wallet data
 * @route   POST /api/transactions/wallet/sync
 * @access  Private
 */
export const syncWalletData = catchAsync(async (req: Request, res: Response) => {
  try {
    const success = await enhancedWalletService.syncWalletData(req.user._id.toString());

    if (success) {
      res.json({
        success: true,
        message: 'Wallet data synchronized successfully',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to synchronize wallet data'
      });
    }

  } catch (error: any) {
    logger.error('Sync wallet data error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to sync wallet data',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Validate withdrawal request
 * @route   POST /api/transactions/withdrawal/validate
 * @access  Private
 */
export const validateWithdrawal = catchAsync(async (req: Request, res: Response) => {
  try {
    const {
      cryptocurrency,
      amount,
      withdrawalType,
      investmentPackageId
    } = req.body;

    // Basic validation
    if (!cryptocurrency || !amount || !withdrawalType) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: cryptocurrency, amount, withdrawalType'
      });
    }

    // Check withdrawable balance
    const withdrawableBalance = await enhancedWalletService.getWithdrawableBalance(
      req.user._id.toString(),
      cryptocurrency.toUpperCase(),
      withdrawalType
    );

    const isValid = withdrawableBalance >= parseFloat(amount);
    const errors = [];

    if (!isValid) {
      errors.push(`Insufficient ${withdrawalType} balance. Available: ${withdrawableBalance} ${cryptocurrency}`);
    }

    res.json({
      success: true,
      data: {
        isValid,
        errors,
        withdrawableBalance,
        requestedAmount: parseFloat(amount),
        cryptocurrency: cryptocurrency.toUpperCase(),
        withdrawalType
      }
    });

  } catch (error: any) {
    logger.error('Validate withdrawal error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to validate withdrawal',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});
