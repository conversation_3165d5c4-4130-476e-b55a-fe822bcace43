import { Request, Response } from 'express';
import WithdrawalAddress from '../models/withdrawalAddressModel';
import UserWallet from '../models/userWalletModel';
import { catchAsync } from '../utils/catchAsync';
import { AppError } from '../utils/AppError';
import { logger } from '../utils/logger';

// Hot Reload Test - Docker Development Environment 🔥🚀
// Updated at: 2025-06-16 02:54:00

// @desc    Get all withdrawal addresses for user
// @route   GET /api/wallet-management/addresses
// @access  Private
export const getUserWithdrawalAddresses = catchAsync(async (req: Request, res: Response) => {
  const { currency } = req.query;
  
  const addresses = await WithdrawalAddress.getUserAddresses(
    req.user._id.toString(),
    currency as string
  );

  res.json({
    success: true,
    data: addresses,
    count: addresses.length
  });
});

// @desc    Add new withdrawal address
// @route   POST /api/wallet-management/addresses
// @access  Private
export const addWithdrawalAddress = catchAsync(async (req: Request, res: Response) => {
  const { currency, address, label, network } = req.body;

  // Validation
  if (!currency || !address || !label) {
    throw new AppError('Currency, address, and label are required', 400);
  }

  // Check if user already has maximum addresses for this currency (limit: 10)
  const existingAddresses = await WithdrawalAddress.getUserAddresses(
    req.user._id.toString(),
    currency
  );

  if (existingAddresses.length >= 10) {
    throw new AppError('Maximum 10 withdrawal addresses allowed per currency', 400);
  }

  // Create new withdrawal address
  const newAddress = await WithdrawalAddress.createAddress(
    req.user._id.toString(),
    currency,
    address,
    label,
    network || 'mainnet'
  );

  // Generate verification code
  const verificationCode = newAddress.generateVerificationCode();
  await newAddress.save();

  logger.info(`New withdrawal address added for user: ${req.user._id}, currency: ${currency}`);

  res.status(201).json({
    success: true,
    message: 'Withdrawal address added successfully. Please verify with the code sent.',
    data: {
      id: newAddress._id,
      currency: newAddress.currency,
      address: newAddress.formattedAddress,
      label: newAddress.label,
      network: newAddress.network,
      isVerified: newAddress.isVerified,
      verificationRequired: true
    },
    verificationCode: process.env.NODE_ENV === 'development' ? verificationCode : undefined
  });
});

// @desc    Verify withdrawal address
// @route   POST /api/wallet-management/addresses/:id/verify
// @access  Private
export const verifyWithdrawalAddress = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { verificationCode } = req.body;

  if (!verificationCode) {
    throw new AppError('Verification code is required', 400);
  }

  const address = await WithdrawalAddress.findOne({
    _id: id,
    userId: req.user._id,
    isActive: true
  }).select('+verificationCode +verificationCodeExpires');

  if (!address) {
    throw new AppError('Withdrawal address not found', 404);
  }

  if (address.isVerified) {
    throw new AppError('Address is already verified', 400);
  }

  const isValid = address.verifyAddress(verificationCode);
  if (!isValid) {
    throw new AppError('Invalid or expired verification code', 400);
  }

  await address.save();

  logger.info(`Withdrawal address verified for user: ${req.user._id}, address ID: ${id}`);

  res.json({
    success: true,
    message: 'Withdrawal address verified successfully',
    data: {
      id: address._id,
      isVerified: address.isVerified
    }
  });
});

// @desc    Update withdrawal address
// @route   PUT /api/wallet-management/addresses/:id
// @access  Private
export const updateWithdrawalAddress = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { label, isDefault } = req.body;

  const address = await WithdrawalAddress.findOne({
    _id: id,
    userId: req.user._id,
    isActive: true
  });

  if (!address) {
    throw new AppError('Withdrawal address not found', 404);
  }

  // Update label if provided
  if (label) {
    if (label.length > 50) {
      throw new AppError('Label must be 50 characters or less', 400);
    }
    address.label = label;
  }

  // Handle default address setting
  if (typeof isDefault === 'boolean' && isDefault) {
    if (!address.isVerified) {
      throw new AppError('Only verified addresses can be set as default', 400);
    }
    await WithdrawalAddress.setDefaultAddress(req.user._id.toString(), id);
  }

  await address.save();

  logger.info(`Withdrawal address updated for user: ${req.user._id}, address ID: ${id}`);

  res.json({
    success: true,
    message: 'Withdrawal address updated successfully',
    data: address
  });
});

// @desc    Delete withdrawal address
// @route   DELETE /api/wallet-management/addresses/:id
// @access  Private
export const deleteWithdrawalAddress = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;

  const address = await WithdrawalAddress.findOne({
    _id: id,
    userId: req.user._id,
    isActive: true
  });

  if (!address) {
    throw new AppError('Withdrawal address not found', 404);
  }

  // Soft delete
  address.isActive = false;
  await address.save();

  // If this was the default address, set another verified address as default
  if (address.isDefault) {
    const nextAddress = await WithdrawalAddress.findOne({
      userId: req.user._id,
      currency: address.currency,
      isActive: true,
      isVerified: true,
      _id: { $ne: id }
    });

    if (nextAddress) {
      nextAddress.isDefault = true;
      await nextAddress.save();
    }
  }

  logger.info(`Withdrawal address deleted for user: ${req.user._id}, address ID: ${id}`);

  res.json({
    success: true,
    message: 'Withdrawal address deleted successfully'
  });
});

// @desc    Set default withdrawal address
// @route   POST /api/wallet-management/addresses/:id/set-default
// @access  Private
export const setDefaultWithdrawalAddress = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;

  const address = await WithdrawalAddress.findOne({
    _id: id,
    userId: req.user._id,
    isActive: true
  });

  if (!address) {
    throw new AppError('Withdrawal address not found', 404);
  }

  if (!address.isVerified) {
    throw new AppError('Only verified addresses can be set as default', 400);
  }

  const success = await WithdrawalAddress.setDefaultAddress(req.user._id.toString(), id);
  
  if (!success) {
    throw new AppError('Failed to set default address', 500);
  }

  logger.info(`Default withdrawal address set for user: ${req.user._id}, address ID: ${id}`);

  res.json({
    success: true,
    message: 'Default withdrawal address set successfully'
  });
});

// @desc    Get withdrawal address by ID
// @route   GET /api/wallet-management/addresses/:id
// @access  Private
export const getWithdrawalAddress = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;

  const address = await WithdrawalAddress.findOne({
    _id: id,
    userId: req.user._id,
    isActive: true
  });

  if (!address) {
    throw new AppError('Withdrawal address not found', 404);
  }

  res.json({
    success: true,
    data: address
  });
});

// @desc    Resend verification code
// @route   POST /api/wallet-management/addresses/:id/resend-verification
// @access  Private
export const resendVerificationCode = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;

  const address = await WithdrawalAddress.findOne({
    _id: id,
    userId: req.user._id,
    isActive: true
  });

  if (!address) {
    throw new AppError('Withdrawal address not found', 404);
  }

  if (address.isVerified) {
    throw new AppError('Address is already verified', 400);
  }

  const verificationCode = address.generateVerificationCode();
  await address.save();

  logger.info(`Verification code resent for user: ${req.user._id}, address ID: ${id}`);

  res.json({
    success: true,
    message: 'Verification code sent successfully',
    verificationCode: process.env.NODE_ENV === 'development' ? verificationCode : undefined
  });
});

// @desc    Get supported currencies and networks
// @route   GET /api/wallet-management/supported-currencies
// @access  Private
export const getSupportedCurrencies = catchAsync(async (req: Request, res: Response) => {
  const supportedCurrencies = [
    {
      currency: 'BTC',
      name: 'Bitcoin',
      networks: ['bitcoin', 'lightning', 'brc20'],
      addressFormat: 'Legacy (1...) or SegWit (bc1...)'
    },
    {
      currency: 'ETH',
      name: 'Ethereum',
      networks: ['erc20', 'eth2'],
      addressFormat: '0x... format (42 characters)'
    },
    {
      currency: 'USDT',
      name: 'Tether USD',
      networks: ['erc20', 'trc20', 'bep20'],
      addressFormat: '0x... (ERC-20/BEP-20) or T... (TRC-20)'
    },
    {
      currency: 'DOGE',
      name: 'Dogecoin',
      networks: ['dogecoin', 'drc20'],
      addressFormat: 'D... format (Dogecoin address)'
    },
    {
      currency: 'TRX',
      name: 'TRON',
      networks: ['tron', 'trc20'],
      addressFormat: 'T... format (TRON address)'
    },
    {
      currency: 'BNB',
      name: 'Binance Coin',
      networks: ['bep2', 'bep20'],
      addressFormat: '0x... (BEP-20) or bnb... (BEP-2)'
    },
    {
      currency: 'SOL',
      name: 'Solana',
      networks: ['solana', 'spl'],
      addressFormat: 'Base58 format (Solana address)'
    }
  ];

  res.json({
    success: true,
    data: supportedCurrencies
  });
});
