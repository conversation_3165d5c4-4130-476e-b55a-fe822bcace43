import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import SystemConfig from '../models/systemConfigModel';
import User from '../models/User';
import { logger } from '../utils/logger';

interface MaintenanceResponse {
  maintenance: boolean;
  message?: string;
  siteName?: string;
  estimatedTime?: string;
}

/**
 * Check if the request is from an admin user
 * @param req Express request object
 * @returns Promise<boolean> - true if user is admin, false otherwise
 */
const isAdminUser = async (req: Request): Promise<boolean> => {
  try {
    // In development mode, check for mock admin
    if (process.env.NODE_ENV === 'development') {
      // Check if there's a token in cookies or headers
      const hasToken = (req.cookies && req.cookies.token) ||
                      (req.headers.authorization && req.headers.authorization.startsWith('Bearer'));

      if (!hasToken) {
        // No token in development mode means mock admin
        return true;
      }
    }

    // Get token from cookies (primary) or Authorization header (fallback)
    let token;
    if (req.cookies && req.cookies.token) {
      token = req.cookies.token;
    } else if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (!token) {
      return false;
    }

    // Verify token
    const decoded: any = jwt.verify(token, process.env.JWT_SECRET || 'fallback_secret');

    // Get user from the token
    const user = await User.findById(decoded.id).select('-password');

    if (!user) {
      return false;
    }

    // Check if user is admin
    return user.isAdmin === true;
  } catch (error) {
    logger.error('Error checking admin status in maintenance middleware:', error);
    return false;
  }
};

/**
 * Middleware to check if the system is in maintenance mode
 * Allows admin users and certain routes to bypass maintenance mode
 */
export const maintenanceMiddleware = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Skip maintenance check for certain routes
    const skipRoutes = [
      '/api/system/config/public',
      '/api/system/maintenance-status',
      '/health',
      '/metrics'
    ];

    // Check if current route should skip maintenance check
    const shouldSkip = skipRoutes.some(route => req.path.startsWith(route));

    if (shouldSkip) {
      return next();
    }

    // Get system configuration
    const config = await SystemConfig.findOneOrCreate();

    // If maintenance mode is enabled
    if (config.maintenanceMode) {
      // Check if user is admin
      const userIsAdmin = await isAdminUser(req);

      if (userIsAdmin) {
        logger.info(`Admin user bypassing maintenance mode for request to ${req.path}`);
        return next();
      }

      // For non-admin users, block the request
      logger.info(`Maintenance mode active - blocking non-admin request to ${req.path}`);

      const maintenanceResponse: MaintenanceResponse = {
        maintenance: true,
        message: config.maintenanceMessage || 'The system is currently under maintenance. Please try again later.',
        siteName: config.siteName || 'Shipping Finance',
        estimatedTime: 'We will be back soon'
      };

      return res.status(503).json({
        success: false,
        ...maintenanceResponse
      });
    }

    // If not in maintenance mode, continue normally
    next();
  } catch (error: any) {
    logger.error('Error in maintenance middleware:', error);
    // If there's an error checking maintenance status, allow the request to continue
    // to prevent the entire system from being blocked
    next();
  }
};

/**
 * Public endpoint to check maintenance status
 * This endpoint is always accessible regardless of maintenance mode
 */
export const getMaintenanceStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const config = await SystemConfig.findOneOrCreate();

    const response: MaintenanceResponse = {
      maintenance: config.maintenanceMode,
      message: config.maintenanceMessage || 'The system is currently under maintenance. Please try again later.',
      siteName: config.siteName || 'Shipping Finance',
      estimatedTime: 'We will be back soon'
    };

    res.status(200).json({
      success: true,
      data: response
    });
  } catch (error: any) {
    logger.error('Error getting maintenance status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get maintenance status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
