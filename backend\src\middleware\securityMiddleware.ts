import { Request, Response, NextFunction, Express } from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import mongoSanitize from 'express-mongo-sanitize';
// @ts-ignore
import xss from 'xss-clean';
import hpp from 'hpp';
import cors from 'cors';
import { logger } from '../utils/logger';

// Create a simple in-memory cache for development
const memoryCache = new Map<string, { data: any, expiry: number }>();

// Rate limiting with dynamic window
const createDynamicLimiter = (windowMs: number, max: number, customKeyGenerator?: (req: Request) => string) => {
  return rateLimit({
    windowMs,
    max,
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
      if (customKeyGenerator) {
        return customKeyGenerator(req) || 'unknown';
      }
      return req.ip || 'unknown';
    },
    handler: (req: Request, res: Response) => {
      logger.warn('Rate limit exceeded:', {
        ip: req.ip,
        path: req.path,
        method: req.method
      });
      res.status(429).json({
        message: 'Too many requests. Please try again later.'
      });
    }
  });
};

// Global rate limiter
export const globalLimiter = createDynamicLimiter(15 * 60 * 1000, 100);

// General limiter for routes
export const limiter = createDynamicLimiter(5 * 60 * 1000, 50);

// API specific limiter
export const apiLimiter = createDynamicLimiter(60 * 60 * 1000, 1000);

// Strict login limiter
export const loginLimiter = createDynamicLimiter(60 * 60 * 1000, 5, (req: Request) => {
  return `${req.ip}-${req.body.email}`;
});

// Wallet operations limiter
export const walletLimiter = createDynamicLimiter(60 * 1000, 10);

// Cache middleware using in-memory cache
export const cacheMiddleware = (duration: number) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (req.method !== 'GET') {
      return next();
    }

    const key = `cache:${req.originalUrl || req.url}`;

    try {
      const cached = memoryCache.get(key);
      const now = Date.now();

      if (cached && cached.expiry > now) {
        return res.json(cached.data);
      }

      // Override res.json to cache the response
      const originalJson = res.json;
      res.json = function(body) {
        const expiry = Date.now() + (duration * 1000);
        memoryCache.set(key, { data: body, expiry });

        // Clean up expired cache entries occasionally
        if (Math.random() < 0.1) { // 10% chance to clean up on each request
          for (const [cacheKey, value] of memoryCache.entries()) {
            if (value.expiry < now) {
              memoryCache.delete(cacheKey);
            }
          }
        }

        return originalJson.call(this, body);
      };

      next();
    } catch (error) {
      logger.error('Cache error:', error);
      next();
    }
  };
};

// Sanitize data
export const sanitizeData = mongoSanitize();

// Prevent XSS attacks
export const preventXSS = xss();

// Prevent HTTP Parameter Pollution
export const preventHPP = hpp();

// Enhanced security headers with CORS-friendly configuration
export const securityHeaders = helmet({
  contentSecurityPolicy: process.env.NODE_ENV === 'development' ? false : {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:", "http:", "blob:", "*"],
      connectSrc: ["'self'", process.env.API_URL || "https://api.example.com", "http://localhost:*", "ws://localhost:*", "*"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'", "data:", "https:", "http:", "*"],
      fontSrc: ["'self'", "data:", "https:", "http:"],
      workerSrc: ["'self'", "blob:"],
    }
  },
  crossOriginEmbedderPolicy: false, // Disabled to allow cross-origin requests
  crossOriginOpenerPolicy: false, // Disabled for development compatibility
  crossOriginResourcePolicy: { policy: "cross-origin" }, // Allow cross-origin resource sharing
  dnsPrefetchControl: true,
  frameguard: process.env.NODE_ENV === 'development' ? false : {
    action: "deny"
  },
  hidePoweredBy: true,
  hsts: process.env.NODE_ENV === 'production' ? {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  } : false,
  ieNoOpen: true,
  noSniff: false, // Disable MIME type checking to allow serving images correctly
  permittedCrossDomainPolicies: { permittedPolicies: 'none' },
  referrerPolicy: { policy: "no-referrer-when-downgrade" }, // More permissive
  xssFilter: true
});

// CORS configuration
export const corsOptions = {
  origin: function (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) {
    // Check if CORS_ORIGIN is set to wildcard
    if (process.env.CORS_ORIGIN === '*') {
      // Allow all origins
      callback(null, true);
      return;
    }

    // Define allowed origins
    const allowedOrigins = [
      process.env.FRONTEND_URL || 'http://localhost',
      'http://localhost',
      'http://localhost:80',
      'http://localhost:3000',
      'http://localhost:3003', // Frontend development server port
      'http://localhost:3004', // Frontend development server port (alternative)
      'http://localhost:5173', // Vite default port
      'http://frontend',
      'http://frontend:80',
      'http://cryptoyield-frontend',
      'http://cryptoyield-frontend:80',
      'https://shpnfinance.com'
    ];

    // Allow requests with no origin (like mobile apps, curl, etc)
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      logger.debug('CORS request from origin:', origin);
      // In development mode, allow all origins
      if (process.env.NODE_ENV === 'development') {
        callback(null, true);
      } else {
        // For image requests, allow all origins in production too
        const isImageRequest = origin && (
          origin.includes('/uploads/') ||
          origin.includes('/receipts/') ||
          origin.endsWith('.jpg') ||
          origin.endsWith('.jpeg') ||
          origin.endsWith('.png') ||
          origin.endsWith('.gif') ||
          origin.endsWith('.pdf')
        );

        if (isImageRequest) {
          callback(null, true);
        } else {
          // Log the request but allow it anyway since we want to allow all domains
          logger.info('CORS request from origin:', origin);
          callback(null, true);
        }
      }
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH', 'HEAD'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin', 'Cache-Control', 'Pragma', 'Range'],
  exposedHeaders: ['Content-Length', 'Content-Type', 'Content-Disposition', 'Content-Range', 'Accept-Ranges'],
  maxAge: 86400 // 24 hours
};

// Apply all security middleware (CORS is handled in main middleware)
export const securityMiddleware = (app: Express): void => {
  // Apply security headers
  app.use(securityHeaders);

  // Apply data sanitization
  app.use(sanitizeData);
  app.use(preventXSS);
  app.use(preventHPP);

  // Apply rate limiting
  app.use(globalLimiter);

  // Specific route rate limiting can be applied at the route level

  // Log security middleware applied
  logger.info('Security middleware applied (CORS handled separately)');
};