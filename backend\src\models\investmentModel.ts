import mongoose, { Document, Schema } from 'mongoose';
import { logger } from '../utils/logger';

export interface IInvestment extends Document {
  userId: mongoose.Types.ObjectId;
  currency: string;
  amount: number;
  description?: string;
  status: 'pending' | 'processing' | 'approved' | 'rejected';
  receiptUrl?: string;
  cryptoAddress: string;
  txHash?: string;
  network?: string;
  adminNotes?: string;
  approvedAt?: Date;
  rejectedAt?: Date;
  rejectionReason?: string;
  // Amount correction fields for admin verification
  originalAmount?: number;
  adminVerifiedAmount?: number;
  amountModifiedBy?: mongoose.Types.ObjectId;
  amountModifiedAt?: Date;
  amountCorrectionReason?: string;
  createdAt: Date;
  updatedAt: Date;
}

const investmentSchema = new Schema<IInvestment>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    currency: {
      type: String,
      required: true,
      enum: ['BTC', 'ETH', 'USDT', 'BNB', 'SOL', 'DOGE', 'TRX'],
      default: 'BTC',
    },
    amount: {
      type: Number,
      required: true,
      min: 0.0001,
    },
    description: {
      type: String,
      trim: true,
    },
    status: {
      type: String,
      enum: ['pending', 'processing', 'approved', 'rejected'],
      default: 'pending',
    },
    receiptUrl: {
      type: String,
      trim: true,
    },
    cryptoAddress: {
      type: String,
      required: true,
      trim: true,
    },
    txHash: {
      type: String,
      trim: true,
    },
    network: {
      type: String,
      trim: true,
      default: 'default',
    },
    adminNotes: {
      type: String,
      trim: true,
    },
    approvedAt: {
      type: Date,
    },
    rejectedAt: {
      type: Date,
    },
    rejectionReason: {
      type: String,
      trim: true,
    },
    // Amount correction fields for admin verification
    originalAmount: {
      type: Number,
      required: false,
    },
    adminVerifiedAmount: {
      type: Number,
      required: false,
    },
    amountModifiedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: false,
    },
    amountModifiedAt: {
      type: Date,
      required: false,
    },
    amountCorrectionReason: {
      type: String,
      trim: true,
      maxlength: 500,
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

// Middleware to emit WebSocket events after investment is saved
investmentSchema.post('save', async function(doc) {
  try {
    // Import services dynamically to avoid circular dependencies
    const { initializeSocketService } = require('../services/socketService');
    const socketService = initializeSocketService(null);

    logger.info(`Investment saved: ${doc._id}`, {
      currency: doc.currency,
      status: doc.status,
      userId: doc.userId.toString()
    });

    // Emit investment update to user
    socketService.broadcastToUser(doc.userId.toString(), {
      type: 'investment_update',
      payload: {
        id: doc._id.toString(),
        currency: doc.currency,
        amount: doc.amount,
        status: doc.status,
        timestamp: doc.updatedAt || doc.createdAt,
        cryptoAddress: doc.cryptoAddress,
        network: doc.network
      }
    });

    // Notify admins about new investment
    socketService.broadcastToAdmins({
      type: 'new_investment',
      payload: {
        id: doc._id.toString(),
        userId: doc.userId.toString(),
        currency: doc.currency,
        amount: doc.amount,
        status: doc.status,
        timestamp: doc.createdAt,
        cryptoAddress: doc.cryptoAddress,
        network: doc.network
      }
    });
  } catch (error) {
    logger.error('Error in investment post-save hook:', error);
  }
});

// Middleware to emit WebSocket events after investment is updated
investmentSchema.post('findOneAndUpdate', async function(doc) {
  if (!doc) return;

  try {
    // Import services dynamically to avoid circular dependencies
    const { initializeSocketService } = require('../services/socketService');
    const socketService = initializeSocketService(null);

    logger.info(`Investment updated: ${doc._id}`, {
      currency: doc.currency,
      status: doc.status,
      userId: doc.userId.toString()
    });

    // Emit investment update to user
    socketService.broadcastToUser(doc.userId.toString(), {
      type: 'investment_status_updated',
      payload: {
        id: doc._id.toString(),
        currency: doc.currency,
        amount: doc.amount,
        status: doc.status,
        timestamp: new Date(),
        message: `Your investment status has been updated to ${doc.status}`
      }
    });

    // Notify admins about investment update
    socketService.broadcastToAdmins({
      type: 'investment_updated',
      payload: {
        id: doc._id.toString(),
        userId: doc.userId.toString(),
        currency: doc.currency,
        amount: doc.amount,
        status: doc.status,
        timestamp: doc.updatedAt,
        cryptoAddress: doc.cryptoAddress,
        network: doc.network,
        receiptUrl: doc.receiptUrl,
        adminNotes: doc.adminNotes
      }
    });
  } catch (error) {
    logger.error('Error in investment post-update hook:', error);
  }
});

// Create indexes for better query performance
investmentSchema.index({ userId: 1 });
investmentSchema.index({ status: 1 });
investmentSchema.index({ currency: 1 });
investmentSchema.index({ createdAt: 1 });

const Investment = mongoose.model<IInvestment>('Investment', investmentSchema);

export default Investment;
