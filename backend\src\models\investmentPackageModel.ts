import mongoose, { Document, Schema } from 'mongoose';

export interface IInvestmentPackage extends Document {
  userId: mongoose.Types.ObjectId;
  transactionId: mongoose.Types.ObjectId; // Link to originating transaction
  packageId: string; // Unique package identifier
  amount: number;
  currency: string;
  createdAt: Date;
  activatedAt: Date;
  dailyInterest: number;
  totalEarned: number;
  accumulatedInterest: number;
  status: 'pending' | 'active' | 'completed' | 'withdrawn';
  interestRate: number; // Fixed at 1% daily (0.01)
  lastCalculatedAt: Date;
  lastInterestDistribution: Date | null;
  withdrawnAt?: Date;
  compoundEnabled: boolean;
  emergencyWithdrawFee: number;
  packageHash: string;
  activeDays: number;
  totalDays: number;
  roi: number; // Return on Investment percentage

  // New deposit-related fields
  depositTransactionId?: mongoose.Types.ObjectId;
  depositCurrency?: string;
  depositAmount?: number;
  conversionRate?: number;
  autoCreated: boolean;
  originalUSDTValue?: number;

  // Enhanced crypto fields
  nextInterestTime?: Date;
  withdrawalEligibleTime?: Date;
  minimumWithdrawalUSDT: number;
  realTimeUSDTValue?: number;
  lastUSDTUpdate?: Date;

  // Interest-only withdrawal system fields
  principalLockUntil?: Date; // Principal locked for 30 days from activation
  withdrawableInterest: number; // Calculated field for withdrawable interest amount
  principalLocked: boolean; // Computed field: true if current date < principalLockUntil

  // Instance methods
  calculateDailyInterest(): number;
  canWithdraw(): boolean;
  canWithdrawPrincipal(): boolean; // New method for principal withdrawal eligibility
  getWithdrawableAmount(): number; // New method to get withdrawable interest amount
  getPrincipalLockStatus(): { locked: boolean; unlockDate: Date | null; daysRemaining: number }; // New method for lock status
  activate(): Promise<IInvestmentPackage>;
  updateROI(): void;
}

const investmentPackageSchema = new Schema<IInvestmentPackage>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
      index: true
    },
    transactionId: {
      type: Schema.Types.ObjectId,
      ref: 'Transaction',
      required: [true, 'Transaction ID is required'],
      index: true
    },
    packageId: {
      type: String,
      required: true,
      unique: true,
      index: true
    },
    amount: {
      type: Number,
      required: [true, 'Investment amount is required'],
      min: [0.000001, 'Investment amount must be greater than 0'],
      validate: {
        validator: function(value: number) {
          return value > 0;
        },
        message: 'Investment amount must be positive'
      }
    },
    currency: {
      type: String,
      required: [true, 'Currency is required'],
      uppercase: true,
      enum: ['BTC', 'ETH', 'USDT', 'BNB', 'SOL', 'DOGE', 'TRX'],
      default: 'USDT'
    },
    createdAt: {
      type: Date,
      default: Date.now,
      index: true
    },
    activatedAt: {
      type: Date,
      default: null,
      index: true
    },
    dailyInterest: {
      type: Number,
      default: 0,
      min: 0
    },
    totalEarned: {
      type: Number,
      default: 0,
      min: 0
    },
    accumulatedInterest: {
      type: Number,
      default: 0,
      min: 0
    },
    status: {
      type: String,
      enum: ['pending', 'active', 'completed', 'withdrawn'],
      default: 'pending',
      index: true
    },
    interestRate: {
      type: Number,
      default: 0.01, // Fixed 1% daily interest
      min: 0,
      max: 1
    },
    lastCalculatedAt: {
      type: Date,
      default: null
    },
    lastInterestDistribution: {
      type: Date,
      default: null,
      index: true
    },
    withdrawnAt: {
      type: Date,
      default: null
    },
    compoundEnabled: {
      type: Boolean,
      default: false
    },
    emergencyWithdrawFee: {
      type: Number,
      default: 0.05, // 5% emergency withdrawal fee
      min: 0,
      max: 1
    },
    packageHash: {
      type: String,
      required: true,
      unique: true,
      index: true
    },
    activeDays: {
      type: Number,
      default: 0,
      min: 0
    },
    totalDays: {
      type: Number,
      default: 365, // Default 1 year investment period
      min: 1
    },
    roi: {
      type: Number,
      default: 0,
      min: 0
    },

    // New deposit-related fields
    depositTransactionId: {
      type: Schema.Types.ObjectId,
      ref: 'DepositTransaction',
      index: true
    },
    depositCurrency: {
      type: String,
      uppercase: true,
      enum: ['BTC', 'ETH', 'USDT', 'BNB', 'SOL', 'DOGE', 'TRX']
    },
    depositAmount: {
      type: Number,
      min: 0
    },
    conversionRate: {
      type: Number,
      min: 0
    },
    autoCreated: {
      type: Boolean,
      default: false,
      index: true
    },
    originalUSDTValue: {
      type: Number,
      min: 0
    },

    // Enhanced crypto fields
    nextInterestTime: {
      type: Date,
      index: true
    },
    withdrawalEligibleTime: {
      type: Date,
      index: true
    },
    minimumWithdrawalUSDT: {
      type: Number,
      default: 50,
      min: 0
    },
    realTimeUSDTValue: {
      type: Number,
      default: 0,
      min: 0
    },
    lastUSDTUpdate: {
      type: Date,
      default: Date.now
    },

    // Interest-only withdrawal system fields
    principalLockUntil: {
      type: Date,
      index: true,
      default: null
    },
    withdrawableInterest: {
      type: Number,
      default: 0,
      min: 0,
      get: function() {
        // Calculate withdrawable interest (totalEarned + accumulatedInterest)
        return (this.totalEarned || 0) + (this.accumulatedInterest || 0);
      }
    },
    principalLocked: {
      type: Boolean,
      default: true,
      get: function() {
        // Compute if principal is still locked
        if (!this.principalLockUntil) return true;
        return new Date() < this.principalLockUntil;
      }
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Virtual fields
investmentPackageSchema.virtual('currentValue').get(function() {
  return this.amount + this.totalEarned;
});

investmentPackageSchema.virtual('dailyEarningRate').get(function() {
  if (this.amount === 0) return 0;
  return (this.dailyInterest / this.amount) * 100;
});

investmentPackageSchema.virtual('isActive').get(function() {
  return this.status === 'active' && this.activatedAt !== null;
});

// Indexes for performance
investmentPackageSchema.index({ userId: 1, status: 1 });
investmentPackageSchema.index({ activatedAt: 1, status: 1 });
investmentPackageSchema.index({ createdAt: -1 });
investmentPackageSchema.index({ packageHash: 1 }, { unique: true });

// Pre-save middleware
investmentPackageSchema.pre('save', function(next) {
  // Generate package hash if not exists
  if (!this.packageHash) {
    const crypto = require('crypto');
    const hashData = `${this.userId}-${this.amount}-${this.currency}-${this.createdAt}`;
    this.packageHash = crypto.createHash('sha256').update(hashData).digest('hex');
  }

  // Generate packageId if not exists
  if (!this.packageId) {
    const crypto = require('crypto');
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8);
    this.packageId = `PKG-${timestamp}-${random}`.toUpperCase();
  }

  // Set fixed 1% daily interest rate for all packages
  this.interestRate = 0.01; // Fixed 1% daily interest as per requirements

  // Update ROI if package is active
  if (this.status === 'active' && this.amount > 0) {
    this.updateROI();
  }

  next();
});

// Static methods interface
interface IInvestmentPackageModel extends mongoose.Model<IInvestmentPackage> {
  getActivePackages(): Promise<IInvestmentPackage[]>;
  getUserPackages(userId: string): Promise<IInvestmentPackage[]>;
  getTotalEarnings(userId: string): Promise<any[]>;
}

// Static methods
investmentPackageSchema.statics.getActivePackages = function() {
  return this.find({
    status: 'active',
    activatedAt: { $ne: null }
  }).populate('userId', 'email firstName lastName');
};

investmentPackageSchema.statics.getUserPackages = function(userId: string) {
  return this.find({ userId })
    .sort({ createdAt: -1 })
    .populate('userId', 'email firstName lastName');
};

investmentPackageSchema.statics.getTotalEarnings = function(userId: string) {
  return this.aggregate([
    { $match: { userId: new mongoose.Types.ObjectId(userId) } },
    {
      $group: {
        _id: null,
        totalEarned: { $sum: '$totalEarned' },
        totalInvested: { $sum: '$amount' },
        activePackages: {
          $sum: {
            $cond: [{ $eq: ['$status', 'active'] }, 1, 0]
          }
        }
      }
    }
  ]);
};

// Instance methods
investmentPackageSchema.methods.calculateDailyInterest = function() {
  if (this.status !== 'active' || !this.activatedAt) {
    return 0;
  }

  // Fixed 1% daily interest on the principal amount
  const baseAmount = this.compoundEnabled ? (this.amount + this.accumulatedInterest) : this.amount;
  return baseAmount * 0.01; // Fixed 1% daily interest
};

investmentPackageSchema.methods.updateROI = function() {
  if (this.amount > 0) {
    this.roi = ((this.totalEarned + this.accumulatedInterest) / this.amount) * 100;
  } else {
    this.roi = 0;
  }
};

investmentPackageSchema.methods.canWithdraw = function() {
  if (this.status !== 'active' || this.totalEarned <= 0) {
    return false;
  }

  // Check minimum USDT threshold (50 USDT)
  const currentUSDTValue = this.realTimeUSDTValue || this.totalEarned;
  if (currentUSDTValue < this.minimumWithdrawalUSDT) {
    return false;
  }

  // Check time lock (only after 03:00 UTC+3)
  const now = new Date();
  const turkeyTime = new Date(now.getTime() + (3 * 60 * 60 * 1000)); // UTC+3
  const today3AM = new Date(turkeyTime);
  today3AM.setHours(3, 0, 0, 0);

  // If current time is before 03:00 today, check if we passed yesterday's 03:00
  if (turkeyTime.getHours() < 3) {
    today3AM.setDate(today3AM.getDate() - 1);
  }

  return turkeyTime >= today3AM;
};

investmentPackageSchema.methods.activate = function() {
  // Activate at next 03:00 Turkey time
  const now = new Date();
  const turkeyTime = new Date(now.getTime() + (3 * 60 * 60 * 1000)); // UTC+3

  const nextActivation = new Date(turkeyTime);
  nextActivation.setHours(3, 0, 0, 0);

  // If current time is after 03:00, activate tomorrow at 03:00
  if (turkeyTime.getHours() >= 3) {
    nextActivation.setDate(nextActivation.getDate() + 1);
  }

  this.activatedAt = nextActivation;
  this.status = 'active';
  this.lastCalculatedAt = nextActivation;

  // Set principal lock until 30 days from activation
  const principalLockDate = new Date(nextActivation);
  principalLockDate.setDate(principalLockDate.getDate() + 30);
  this.principalLockUntil = principalLockDate;

  // Set next interest calculation time
  this.nextInterestTime = new Date(nextActivation.getTime() + (24 * 60 * 60 * 1000)); // Next day at 03:00

  // Set withdrawal eligible time (after first interest calculation)
  this.withdrawalEligibleTime = this.nextInterestTime;

  return this.save();
};

// New methods for interest-only withdrawal system
investmentPackageSchema.methods.canWithdrawPrincipal = function() {
  // Principal can only be withdrawn after the 30-day lock period
  if (!this.principalLockUntil) return false;
  return new Date() >= this.principalLockUntil;
};

investmentPackageSchema.methods.getWithdrawableAmount = function() {
  // Only interest earnings are withdrawable during lock period
  // Principal is locked for 30 days from activation
  const interestAmount = (this.totalEarned || 0) + (this.accumulatedInterest || 0);

  // If principal lock has expired, return total (principal + interest)
  if (this.canWithdrawPrincipal()) {
    return this.amount + interestAmount;
  }

  // During lock period, only interest is withdrawable
  return interestAmount;
};

investmentPackageSchema.methods.getPrincipalLockStatus = function() {
  if (!this.principalLockUntil) {
    return {
      locked: true,
      unlockDate: null,
      daysRemaining: 30 // Default if not set
    };
  }

  const now = new Date();
  const locked = now < this.principalLockUntil;
  const daysRemaining = locked
    ? Math.ceil((this.principalLockUntil.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    : 0;

  return {
    locked,
    unlockDate: this.principalLockUntil,
    daysRemaining: Math.max(0, daysRemaining)
  };
};

const InvestmentPackage = mongoose.model<IInvestmentPackage, IInvestmentPackageModel>('InvestmentPackage', investmentPackageSchema);

export default InvestmentPackage;
