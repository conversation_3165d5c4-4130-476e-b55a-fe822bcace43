import mongoose, { Document, Schema } from 'mongoose';

export interface IPaymentHistory extends Document {
  userId: mongoose.Types.ObjectId;
  investmentPackageId: mongoose.Types.ObjectId;
  amount: number;
  currency: string;
  paymentDate: Date;
  paymentType: 'interest' | 'commission' | 'bonus' | 'withdrawal' | 'deposit';
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  transactionId?: mongoose.Types.ObjectId;
  walletId?: mongoose.Types.ObjectId;
  metadata?: {
    dailyInterestRate?: number;
    activeDays?: number;
    totalEarned?: number;
    compoundEnabled?: boolean;
    calculationMethod?: string;
    previousBalance?: number;
    newBalance?: number;
    [key: string]: any;
  };
  createdAt: Date;
  updatedAt: Date;
}

const paymentHistorySchema = new Schema<IPaymentHistory>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    investmentPackageId: {
      type: Schema.Types.ObjectId,
      ref: 'InvestmentPackage',
      required: true,
      index: true
    },
    amount: {
      type: Number,
      required: true,
      min: 0
    },
    currency: {
      type: String,
      required: true,
      trim: true,
      uppercase: true,
      index: true
    },
    paymentDate: {
      type: Date,
      required: true,
      index: true
    },
    paymentType: {
      type: String,
      enum: ['interest', 'commission', 'bonus', 'withdrawal', 'deposit'],
      required: true,
      index: true
    },
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed', 'cancelled'],
      default: 'completed',
      index: true
    },
    transactionId: {
      type: Schema.Types.ObjectId,
      ref: 'Transaction',
      required: false
    },
    walletId: {
      type: Schema.Types.ObjectId,
      ref: 'Wallet',
      required: false
    },
    metadata: {
      type: Object,
      default: {}
    }
  },
  {
    timestamps: true
  }
);

// Compound indexes for efficient queries
paymentHistorySchema.index({ userId: 1, paymentDate: -1 });
paymentHistorySchema.index({ investmentPackageId: 1, paymentDate: -1 });
paymentHistorySchema.index({ paymentType: 1, paymentDate: -1 });
paymentHistorySchema.index({ currency: 1, paymentDate: -1 });

// Static methods interface
interface IPaymentHistoryModel extends mongoose.Model<IPaymentHistory> {
  createPaymentRecord(data: Partial<IPaymentHistory>): Promise<IPaymentHistory>;
  getUserPaymentHistory(userId: string, options?: {
    paymentType?: string;
    currency?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    page?: number;
  }): Promise<IPaymentHistory[]>;
  getPackagePaymentHistory(packageId: string): Promise<IPaymentHistory[]>;
  getTotalPaymentsByType(userId: string, paymentType: string, currency?: string): Promise<number>;
}

// Static methods
paymentHistorySchema.statics.createPaymentRecord = function(data: Partial<IPaymentHistory>) {
  return this.create({
    paymentDate: new Date(),
    status: 'completed',
    ...data
  });
};

paymentHistorySchema.statics.getUserPaymentHistory = function(
  userId: string,
  options: {
    paymentType?: string;
    currency?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    page?: number;
  } = {}
) {
  const query: any = { userId };
  
  if (options.paymentType) {
    query.paymentType = options.paymentType;
  }
  
  if (options.currency) {
    query.currency = options.currency;
  }
  
  if (options.startDate || options.endDate) {
    query.paymentDate = {};
    if (options.startDate) query.paymentDate.$gte = options.startDate;
    if (options.endDate) query.paymentDate.$lte = options.endDate;
  }
  
  const limit = options.limit || 50;
  const skip = ((options.page || 1) - 1) * limit;
  
  return this.find(query)
    .sort({ paymentDate: -1 })
    .limit(limit)
    .skip(skip)
    .populate('investmentPackageId', 'amount currency status activeDays')
    .populate('transactionId', 'txHash status')
    .populate('userId', 'email firstName lastName');
};

paymentHistorySchema.statics.getPackagePaymentHistory = function(packageId: string) {
  return this.find({ investmentPackageId: packageId })
    .sort({ paymentDate: -1 })
    .populate('userId', 'email firstName lastName');
};

paymentHistorySchema.statics.getTotalPaymentsByType = function(
  userId: string,
  paymentType: string,
  currency?: string
) {
  const query: any = { userId, paymentType, status: 'completed' };
  
  if (currency) {
    query.currency = currency;
  }
  
  return this.aggregate([
    { $match: query },
    {
      $group: {
        _id: null,
        totalAmount: { $sum: '$amount' },
        count: { $sum: 1 }
      }
    }
  ]).then(result => result[0]?.totalAmount || 0);
};

const PaymentHistory = mongoose.model<IPaymentHistory, IPaymentHistoryModel>('PaymentHistory', paymentHistorySchema);

export default PaymentHistory;
