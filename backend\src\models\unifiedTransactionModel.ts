import mongoose, { Document, Schema } from 'mongoose';

/**
 * Unified Transaction Model for all cryptocurrency operations
 * Replaces separate deposit and withdrawal models for consistency
 */
export interface IUnifiedTransaction extends Document {
  // Core identification
  userId: mongoose.Types.ObjectId;
  walletId: mongoose.Types.ObjectId;
  transactionId: string; // Unique identifier for tracking
  
  // Transaction type and details
  type: 'deposit' | 'withdrawal';
  subType: 'balance' | 'interest' | 'commission' | 'investment';
  cryptocurrency: string;
  network: string;
  
  // Amount information
  amount: number;
  usdValue: number;
  networkFee: number;
  netAmount: number; // amount - networkFee for withdrawals, amount for deposits
  
  // Blockchain information
  walletAddress: string;
  txHash?: string;
  blockHeight?: number;
  confirmations: number;
  requiredConfirmations: number;
  
  // Status management
  status: 'pending' | 'processing' | 'approved' | 'completed' | 'rejected' | 'failed' | 'expired';
  adminStatus?: 'pending_review' | 'approved' | 'rejected';
  
  // Investment package relationship
  investmentPackageId?: mongoose.Types.ObjectId;
  autoInvestmentEnabled: boolean;
  
  // Admin management
  adminNotes?: string;
  adminUserId?: mongoose.Types.ObjectId;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  approvedAt?: Date;
  completedAt?: Date;
  confirmedAt?: Date;
  
  // Metadata for extensibility
  metadata: {
    // Lock validation for withdrawals
    lockValidation?: {
      isLocked: boolean;
      lockExpiryDate?: Date;
      daysRemaining?: number;
      firstInvestmentDate?: Date;
    };
    
    // Fee calculation details
    feeCalculation?: {
      baseFee: number;
      networkFee: number;
      totalFee: number;
      feePercentage: number;
    };
    
    // Validation results
    validationResults?: {
      balanceCheck: boolean;
      lockPeriodCheck: boolean;
      minimumAmountCheck: boolean;
      addressValidation: boolean;
    };
    
    // Admin actions history
    adminActions?: Array<{
      action: string;
      adminId: mongoose.Types.ObjectId;
      timestamp: Date;
      notes?: string;
      previousStatus?: string;
      newStatus?: string;
      previousAmount?: number;
      newAmount?: number;
    }>;
    
    // Real-time sync tracking
    syncStatus?: {
      walletUpdated: boolean;
      balanceUpdated: boolean;
      investmentCreated: boolean;
      notificationSent: boolean;
      lastSyncAt: Date;
    };
    
    // Blockchain tracking
    blockchainData?: {
      gasUsed?: number;
      gasPrice?: number;
      blockTimestamp?: Date;
      transactionIndex?: number;
    };
  };
  
  // Instance methods
  canCreateInvestment(): boolean;
  markAsConfirmed(): Promise<IUnifiedTransaction>;
  calculateUSDValue(): Promise<number>;
  updateAdminStatus(status: string, adminId: string, notes?: string): Promise<IUnifiedTransaction>;
  syncToWallet(): Promise<boolean>;
}

const unifiedTransactionSchema = new Schema<IUnifiedTransaction>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
      index: true
    },
    walletId: {
      type: Schema.Types.ObjectId,
      ref: 'Wallet',
      required: [true, 'Wallet ID is required'],
      index: true
    },
    transactionId: {
      type: String,
      required: true,
      unique: true,
      index: true
    },
    type: {
      type: String,
      required: true,
      enum: ['deposit', 'withdrawal'],
      index: true
    },
    subType: {
      type: String,
      required: true,
      enum: ['balance', 'interest', 'commission', 'investment'],
      index: true
    },
    cryptocurrency: {
      type: String,
      required: true,
      uppercase: true,
      enum: ['BTC', 'ETH', 'USDT', 'BNB', 'SOL', 'DOGE', 'TRX'],
      index: true
    },
    network: {
      type: String,
      required: true,
      enum: ['Bitcoin', 'Ethereum', 'BSC', 'Tron', 'Solana'],
      index: true
    },
    amount: {
      type: Number,
      required: [true, 'Amount is required'],
      min: [0.000001, 'Amount must be positive'],
      validate: {
        validator: function(value: number) {
          return value > 0;
        },
        message: 'Amount must be positive'
      }
    },
    usdValue: {
      type: Number,
      required: true,
      min: 0
    },
    networkFee: {
      type: Number,
      default: 0,
      min: 0
    },
    netAmount: {
      type: Number,
      required: true,
      min: 0
    },
    walletAddress: {
      type: String,
      required: [true, 'Wallet address is required'],
      trim: true,
      index: true
    },
    txHash: {
      type: String,
      trim: true,
      sparse: true, // Allow multiple null values
      index: true
    },
    blockHeight: {
      type: Number,
      min: 0
    },
    confirmations: {
      type: Number,
      default: 0,
      min: 0
    },
    requiredConfirmations: {
      type: Number,
      default: 3,
      min: 1
    },
    status: {
      type: String,
      enum: ['pending', 'processing', 'approved', 'completed', 'rejected', 'failed', 'expired'],
      default: 'pending',
      index: true
    },
    adminStatus: {
      type: String,
      enum: ['pending_review', 'approved', 'rejected'],
      index: true
    },
    investmentPackageId: {
      type: Schema.Types.ObjectId,
      ref: 'InvestmentPackage',
      index: true
    },
    autoInvestmentEnabled: {
      type: Boolean,
      default: true
    },
    adminNotes: {
      type: String,
      trim: true,
      maxlength: 1000
    },
    adminUserId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      index: true
    },
    approvedAt: {
      type: Date,
      index: true
    },
    completedAt: {
      type: Date,
      index: true
    },
    confirmedAt: {
      type: Date,
      index: true
    },
    metadata: {
      type: Schema.Types.Mixed,
      default: {}
    }
  },
  {
    timestamps: true,
    collection: 'unified_transactions'
  }
);

// Compound indexes for efficient queries
unifiedTransactionSchema.index({ userId: 1, type: 1, status: 1 });
unifiedTransactionSchema.index({ userId: 1, cryptocurrency: 1, type: 1 });
unifiedTransactionSchema.index({ status: 1, type: 1, createdAt: -1 });
unifiedTransactionSchema.index({ txHash: 1 }, { sparse: true });
unifiedTransactionSchema.index({ transactionId: 1 }, { unique: true });

// Pre-save middleware
unifiedTransactionSchema.pre('save', function(next) {
  // Generate transaction ID if not provided
  if (!this.transactionId) {
    const prefix = this.type === 'deposit' ? 'DEP' : 'WTH';
    this.transactionId = `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  // Calculate net amount
  if (this.type === 'withdrawal') {
    this.netAmount = this.amount - this.networkFee;
  } else {
    this.netAmount = this.amount;
  }
  
  next();
});

// Instance methods
unifiedTransactionSchema.methods.canCreateInvestment = function(): boolean {
  return this.type === 'deposit' && 
         this.status === 'completed' && 
         this.autoInvestmentEnabled && 
         !this.investmentPackageId;
};

unifiedTransactionSchema.methods.markAsConfirmed = async function(): Promise<IUnifiedTransaction> {
  this.status = 'completed';
  this.confirmedAt = new Date();
  this.confirmations = this.requiredConfirmations;
  
  // Update sync status
  if (!this.metadata.syncStatus) {
    this.metadata.syncStatus = {};
  }
  this.metadata.syncStatus.lastSyncAt = new Date();
  
  return this.save();
};

unifiedTransactionSchema.methods.calculateUSDValue = async function(): Promise<number> {
  // Implementation for USD value calculation
  // This would integrate with price service
  return this.usdValue || 0;
};

unifiedTransactionSchema.methods.updateAdminStatus = async function(
  status: string, 
  adminId: string, 
  notes?: string
): Promise<IUnifiedTransaction> {
  this.adminStatus = status as any;
  this.adminUserId = new mongoose.Types.ObjectId(adminId);
  if (notes) this.adminNotes = notes;
  
  // Add to admin actions history
  if (!this.metadata.adminActions) {
    this.metadata.adminActions = [];
  }
  
  this.metadata.adminActions.push({
    action: `status_update_${status}`,
    adminId: new mongoose.Types.ObjectId(adminId),
    timestamp: new Date(),
    notes,
    previousStatus: this.status,
    newStatus: status
  });
  
  if (status === 'approved') {
    this.approvedAt = new Date();
    this.status = 'approved';
  } else if (status === 'rejected') {
    this.status = 'rejected';
  }
  
  return this.save();
};

unifiedTransactionSchema.methods.syncToWallet = async function(): Promise<boolean> {
  // Implementation for wallet synchronization
  // This would trigger wallet balance updates
  if (!this.metadata.syncStatus) {
    this.metadata.syncStatus = {};
  }
  
  this.metadata.syncStatus.walletUpdated = true;
  this.metadata.syncStatus.lastSyncAt = new Date();
  
  await this.save();
  return true;
};

export const UnifiedTransaction = mongoose.model<IUnifiedTransaction>('UnifiedTransaction', unifiedTransactionSchema);
export default UnifiedTransaction;
