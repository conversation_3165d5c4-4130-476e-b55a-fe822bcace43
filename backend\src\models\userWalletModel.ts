import mongoose, { Document, Schema } from 'mongoose';
import crypto from 'crypto';

export interface IUserWallet extends Document {
  userId: mongoose.Types.ObjectId;
  currency: string;
  address: string;
  privateKey?: string; // Encrypted
  balance: number;
  lastUpdated: Date;
  isActive: boolean;
  network: string;
  addressIndex: number;
  qrCodeUrl?: string;
  label?: string; // User-defined label for the wallet
  isDefault?: boolean; // Whether this is the default wallet for this currency
  withdrawalEnabled?: boolean; // Whether this wallet can be used for withdrawals

  // Instance methods
  updateBalance(newBalance: number): Promise<IUserWallet>;
  generateQRCode(): string;
  getDecryptedPrivateKey(): string | null;
  setPrivateKey(privateKey: string): void;
}

const userWalletSchema = new Schema<IUserWallet>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
      index: true
    },
    currency: {
      type: String,
      required: [true, 'Currency is required'],
      uppercase: true,
      enum: ['BTC', 'ETH', 'USDT', 'BNB', 'ADA', 'DOT', 'LINK', 'UNI'],
      index: true
    },
    address: {
      type: String,
      required: [true, 'Wallet address is required'],
      trim: true,
      unique: true,
      index: true
    },
    privateKey: {
      type: String,
      trim: true,
      select: false // Don't include in queries by default
    },
    balance: {
      type: Number,
      default: 0,
      min: 0
    },
    lastUpdated: {
      type: Date,
      default: Date.now,
      index: true
    },
    isActive: {
      type: Boolean,
      default: true,
      index: true
    },
    network: {
      type: String,
      required: [true, 'Network is required'],
      trim: true,
      default: 'mainnet'
    },
    addressIndex: {
      type: Number,
      default: 0,
      min: 0
    },
    qrCodeUrl: {
      type: String,
      trim: true
    },
    label: {
      type: String,
      trim: true,
      maxlength: 50
    },
    isDefault: {
      type: Boolean,
      default: false,
      index: true
    },
    withdrawalEnabled: {
      type: Boolean,
      default: true,
      index: true
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Compound indexes for performance
userWalletSchema.index({ userId: 1, currency: 1 }, { unique: true });
userWalletSchema.index({ currency: 1, isActive: 1 });
userWalletSchema.index({ lastUpdated: -1 });

// Virtual for formatted address (truncated)
userWalletSchema.virtual('formattedAddress').get(function() {
  if (!this.address) return '';
  if (this.address.length <= 10) return this.address;
  return `${this.address.slice(0, 6)}...${this.address.slice(-4)}`;
});

// Virtual for balance in USDT (would need real-time conversion)
userWalletSchema.virtual('balanceUSDT').get(function() {
  // This would be calculated using current exchange rates
  // For now, return the balance if currency is USDT
  return this.currency === 'USDT' ? this.balance : 0;
});

// Instance method to update balance
userWalletSchema.methods.updateBalance = async function(newBalance: number): Promise<IUserWallet> {
  this.balance = newBalance;
  this.lastUpdated = new Date();
  return await this.save();
};

// Instance method to generate QR code URL
userWalletSchema.methods.generateQRCode = function(): string {
  // This would generate a QR code for the wallet address
  // For now, return a placeholder URL
  return `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=${encodeURIComponent(this.address)}`;
};

// Instance method to get decrypted private key
userWalletSchema.methods.getDecryptedPrivateKey = function(): string | null {
  if (!this.privateKey) return null;

  try {
    const algorithm = 'aes-256-cbc';
    const key = process.env.WALLET_ENCRYPTION_KEY || 'default-key-change-in-production';
    const keyBuffer = crypto.scryptSync(key, 'salt', 32);

    const parts = this.privateKey.split(':');
    if (parts.length !== 2) return null;

    const iv = Buffer.from(parts[0], 'hex');
    const encryptedData = Buffer.from(parts[1], 'hex');

    const decipher = crypto.createDecipheriv(algorithm, keyBuffer, iv);
    let decrypted = decipher.update(encryptedData, undefined, 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    console.error('Error decrypting private key:', error);
    return null;
  }
};

// Instance method to set encrypted private key
userWalletSchema.methods.setPrivateKey = function(privateKey: string): void {
  try {
    const algorithm = 'aes-256-cbc';
    const key = process.env.WALLET_ENCRYPTION_KEY || 'default-key-change-in-production';
    const keyBuffer = crypto.scryptSync(key, 'salt', 32);
    const iv = crypto.randomBytes(16);

    const cipher = crypto.createCipheriv(algorithm, keyBuffer, iv);
    let encrypted = cipher.update(privateKey, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    this.privateKey = `${iv.toString('hex')}:${encrypted}`;
  } catch (error) {
    console.error('Error encrypting private key:', error);
    throw new Error('Failed to encrypt private key');
  }
};

// Static methods interface
interface IUserWalletModel extends mongoose.Model<IUserWallet> {
  getUserWallets(userId: string): Promise<IUserWallet[]>;
  getWalletByAddress(address: string): Promise<IUserWallet | null>;
  getActiveWalletsByCurrency(currency: string): Promise<IUserWallet[]>;
  createUserWallet(userId: string, currency: string, address: string, network?: string): Promise<IUserWallet>;
}

// Static method to get user wallets
userWalletSchema.statics.getUserWallets = function(userId: string) {
  return this.find({
    userId,
    isActive: true
  }).sort({ currency: 1 });
};

// Static method to get wallet by address
userWalletSchema.statics.getWalletByAddress = function(address: string) {
  return this.findOne({ address, isActive: true });
};

// Static method to get active wallets by currency
userWalletSchema.statics.getActiveWalletsByCurrency = function(currency: string) {
  return this.find({
    currency: currency.toUpperCase(),
    isActive: true
  }).populate('userId', 'email firstName lastName');
};

// Static method to create user wallet
userWalletSchema.statics.createUserWallet = async function(
  userId: string,
  currency: string,
  address: string,
  network: string = 'mainnet'
): Promise<IUserWallet> {
  // Check if wallet already exists
  const existingWallet = await this.findOne({ userId, currency: currency.toUpperCase() });
  if (existingWallet) {
    throw new Error(`Wallet for ${currency} already exists for this user`);
  }

  const wallet = new this({
    userId,
    currency: currency.toUpperCase(),
    address,
    network,
    isActive: true,
    balance: 0
  });

  return await wallet.save();
};

// Pre-save middleware
userWalletSchema.pre('save', function(next) {
  // Generate QR code URL if not exists
  if (!this.qrCodeUrl && this.address) {
    this.qrCodeUrl = this.generateQRCode();
  }
  next();
});

// Pre-find middleware to exclude inactive wallets by default
userWalletSchema.pre(/^find/, function(this: any) {
  // Only apply this filter if isActive is not explicitly set in the query
  const query = this.getQuery();
  if (query && !query.hasOwnProperty('isActive')) {
    this.where({ isActive: { $ne: false } });
  }
});

const UserWallet = mongoose.model<IUserWallet, IUserWalletModel>(
  'UserWallet',
  userWalletSchema
);

export default UserWallet;
