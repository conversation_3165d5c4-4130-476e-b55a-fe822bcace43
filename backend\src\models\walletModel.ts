import mongoose, { Document, Schema } from 'mongoose';

export interface IWallet extends Document {
  userId: mongoose.Types.ObjectId;
  assets: {
    symbol: string;
    balance: number;
    commissionBalance: number;
    interestBalance: number;
    mode: 'commission' | 'interest';
    network?: string;
    address?: string;
  }[];
  totalCommissionEarned: number;
  totalInterestEarned: number;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  getEarnedBalance(symbol: string): number;
  getAssetsWithEarnedBalance(): any[];
}

const walletSchema = new Schema<IWallet>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    assets: [
      {
        symbol: {
          type: String,
          required: true,
          trim: true,
          uppercase: true,
        },
        balance: {
          type: Number,
          required: true,
          default: 0,
        },
        commissionBalance: {
          type: Number,
          default: 0,
        },
        interestBalance: {
          type: Number,
          default: 0,
        },
        mode: {
          type: String,
          enum: ['commission', 'interest'],
        },
        network: {
          type: String,
          trim: true,
        },
        address: {
          type: String,
          trim: true,
        },
      },
    ],
    totalCommissionEarned: {
      type: Number,
      default: 0,
    },
    totalInterestEarned: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
  }
);

// Set JSON and Object transforms
walletSchema.set('toJSON', {
  virtuals: true
});

walletSchema.set('toObject', {
  virtuals: true
});

// Instance method to get earned balance for a specific asset
walletSchema.methods.getEarnedBalance = function(symbol: string): number {
  const asset = this.assets.find((a: any) => a.symbol === symbol);
  if (!asset) return 0;
  return (asset.commissionBalance || 0) + (asset.interestBalance || 0);
};

// Instance method to get all assets with earned balance calculation
walletSchema.methods.getAssetsWithEarnedBalance = function() {
  return this.assets.map((asset: any) => ({
    ...asset.toObject(),
    totalEarnings: (asset.commissionBalance || 0) + (asset.interestBalance || 0)
  }));
};

const Wallet = mongoose.model<IWallet>('Wallet', walletSchema);

export default Wallet;
