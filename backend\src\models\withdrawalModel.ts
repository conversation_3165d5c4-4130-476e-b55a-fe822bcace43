import mongoose, { Document, Schema } from 'mongoose';

export interface IWithdrawal extends Document {
  userId: mongoose.Types.ObjectId;
  walletId: mongoose.Types.ObjectId;
  cryptocurrency: string;
  withdrawalType: 'balance' | 'interest' | 'commission';
  investmentPackageId?: mongoose.Types.ObjectId; // Required for 'balance' withdrawals
  amount: number;
  usdValue: number;
  networkFee: number;
  netAmount: number;
  walletAddress: string;
  network: string;
  status: 'pending' | 'approved' | 'rejected' | 'completed' | 'failed';
  adminNotes?: string;
  txHash?: string;
  createdAt: Date;
  updatedAt: Date;
  approvedAt?: Date;
  completedAt?: Date;
  metadata: {
    lockValidation?: {
      isLocked: boolean;
      lockExpiryDate?: Date;
      daysRemaining?: number;
      firstInvestmentDate?: Date;
    };
    feeCalculation?: {
      baseFee: number;
      networkFee: number;
      totalFee: number;
      feePercentage: number;
    };
    adminActions?: Array<{
      action: string;
      adminId: mongoose.Types.ObjectId;
      timestamp: Date;
      notes?: string;
      previousStatus?: string;
      newStatus?: string;
    }>;
    validationResults?: {
      balanceCheck: boolean;
      minimumAmountCheck: boolean;
      lockCheck: boolean;
      addressValidation: boolean;
      networkValidation: boolean;
    };
    conversionRate?: {
      rate: number;
      timestamp: Date;
      source: string;
    };
  };
}

const withdrawalSchema = new Schema<IWithdrawal>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  walletId: {
    type: Schema.Types.ObjectId,
    ref: 'Wallet',
    required: true,
    index: true
  },
  cryptocurrency: {
    type: String,
    required: true,
    uppercase: true,
    enum: ['BTC', 'ETH', 'USDT', 'BNB', 'SOL', 'DOGE', 'TRX'],
    index: true
  },
  withdrawalType: {
    type: String,
    required: true,
    enum: ['balance', 'interest', 'commission'],
    index: true
  },
  investmentPackageId: {
    type: Schema.Types.ObjectId,
    ref: 'InvestmentPackage',
    required: function(this: IWithdrawal) {
      return this.withdrawalType === 'balance';
    },
    validate: {
      validator: function(this: IWithdrawal, value: mongoose.Types.ObjectId) {
        // investmentPackageId should only be set for 'balance' withdrawals
        if (this.withdrawalType === 'balance') {
          return value != null;
        } else {
          return value == null;
        }
      },
      message: 'investmentPackageId is required for balance withdrawals and should be null for interest/commission withdrawals'
    },
    index: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  usdValue: {
    type: Number,
    required: true,
    min: 0
  },
  networkFee: {
    type: Number,
    default: 0,
    min: 0
  },
  netAmount: {
    type: Number,
    required: true,
    min: 0
  },
  walletAddress: {
    type: String,
    required: true,
    trim: true
  },
  network: {
    type: String,
    required: true,
    enum: ['Bitcoin', 'Ethereum', 'Tron', 'BSC', 'Solana', 'Polygon']
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'approved', 'rejected', 'completed', 'failed'],
    default: 'pending',
    index: true
  },
  adminNotes: {
    type: String,
    trim: true
  },
  txHash: {
    type: String,
    trim: true,
    sparse: true
  },
  approvedAt: {
    type: Date
  },
  completedAt: {
    type: Date
  },
  metadata: {
    lockValidation: {
      isLocked: { type: Boolean, default: false },
      lockExpiryDate: Date,
      daysRemaining: Number,
      firstInvestmentDate: Date
    },
    feeCalculation: {
      baseFee: { type: Number, default: 0 },
      networkFee: { type: Number, default: 0 },
      totalFee: { type: Number, default: 0 },
      feePercentage: { type: Number, default: 0 }
    },
    adminActions: [{
      action: { type: String, required: true },
      adminId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
      timestamp: { type: Date, default: Date.now },
      notes: String,
      previousStatus: String,
      newStatus: String
    }],
    validationResults: {
      balanceCheck: { type: Boolean, default: false },
      minimumAmountCheck: { type: Boolean, default: false },
      lockCheck: { type: Boolean, default: false },
      addressValidation: { type: Boolean, default: false },
      networkValidation: { type: Boolean, default: false }
    },
    conversionRate: {
      rate: Number,
      timestamp: Date,
      source: String
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
withdrawalSchema.index({ userId: 1, status: 1 });
withdrawalSchema.index({ cryptocurrency: 1, status: 1 });
withdrawalSchema.index({ createdAt: -1 });
withdrawalSchema.index({ status: 1, createdAt: -1 });

// Virtual for user information
withdrawalSchema.virtual('user', {
  ref: 'User',
  localField: 'userId',
  foreignField: '_id',
  justOne: true
});

// Virtual for wallet information
withdrawalSchema.virtual('wallet', {
  ref: 'Wallet',
  localField: 'walletId',
  foreignField: '_id',
  justOne: true
});

// Virtual for investment package information
withdrawalSchema.virtual('investmentPackage', {
  ref: 'InvestmentPackage',
  localField: 'investmentPackageId',
  foreignField: '_id',
  justOne: true
});

// Pre-save middleware to calculate net amount and validate investment package
withdrawalSchema.pre('save', async function(next) {
  // Calculate net amount
  if (this.isModified('amount') || this.isModified('networkFee')) {
    this.netAmount = this.amount - (this.networkFee || 0);
  }

  // Validate investment package for balance withdrawals
  if (this.withdrawalType === 'balance' && this.investmentPackageId && this.isNew) {
    try {
      const InvestmentPackage = mongoose.model('InvestmentPackage');
      const investmentPackage = await InvestmentPackage.findById(this.investmentPackageId);

      if (!investmentPackage) {
        return next(new Error('Investment package not found'));
      }

      if (!investmentPackage.userId.equals(this.userId)) {
        return next(new Error('Investment package does not belong to the user'));
      }

      if (investmentPackage.status !== 'active') {
        return next(new Error('Investment package is not active'));
      }

      if (investmentPackage.amount < this.amount) {
        return next(new Error('Insufficient balance in investment package'));
      }

      if (investmentPackage.currency !== this.cryptocurrency) {
        return next(new Error('Investment package currency does not match withdrawal cryptocurrency'));
      }
    } catch (error) {
      return next(error);
    }
  }

  next();
});

// Static method to get withdrawal statistics
withdrawalSchema.statics.getStatistics = async function(userId?: string) {
  const match: any = {};
  if (userId) match.userId = new mongoose.Types.ObjectId(userId);

  return await this.aggregate([
    { $match: match },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        totalUsdValue: { $sum: '$usdValue' }
      }
    }
  ]);
};

// Static method to process balance withdrawal approval with investment package update
withdrawalSchema.statics.approveBalanceWithdrawal = async function(withdrawalId: string, adminId: string, adminNotes?: string) {
  const session = await mongoose.startSession();

  try {
    return await session.withTransaction(async () => {
      const withdrawal = await this.findById(withdrawalId).session(session);
      if (!withdrawal) {
        throw new Error('Withdrawal not found');
      }

      if (withdrawal.status !== 'pending') {
        throw new Error('Withdrawal is not in pending status');
      }

      if (withdrawal.withdrawalType === 'balance' && withdrawal.investmentPackageId) {
        const InvestmentPackage = mongoose.model('InvestmentPackage');
        const investmentPackage = await InvestmentPackage.findById(withdrawal.investmentPackageId).session(session);

        if (!investmentPackage) {
          throw new Error('Investment package not found');
        }

        // Note: Funds were already deducted from wallet when withdrawal was created
        // We only need to update investment package for tracking purposes

        // Deduct amount from investment package for tracking
        investmentPackage.amount -= withdrawal.amount;

        // Update investment package status if fully withdrawn
        if (investmentPackage.amount <= 0) {
          investmentPackage.status = 'withdrawn';
          investmentPackage.withdrawnAt = new Date();
        }

        await investmentPackage.save({ session });
      }

      // Update withdrawal status
      withdrawal.status = 'approved';
      withdrawal.approvedAt = new Date();
      withdrawal.adminNotes = adminNotes;

      // Add admin action to metadata
      if (!withdrawal.metadata.adminActions) {
        withdrawal.metadata.adminActions = [];
      }
      withdrawal.metadata.adminActions.push({
        action: 'approve',
        adminId: new mongoose.Types.ObjectId(adminId),
        timestamp: new Date(),
        notes: adminNotes,
        previousStatus: 'pending',
        newStatus: 'approved'
      });

      await withdrawal.save({ session });

      return withdrawal;
    });
  } finally {
    await session.endSession();
  }
};

// Static method to reject withdrawal and restore funds if needed
withdrawalSchema.statics.rejectWithdrawal = async function(withdrawalId: string, adminId: string, rejectionReason: string) {
  const session = await mongoose.startSession();

  try {
    return await session.withTransaction(async () => {
      const withdrawal = await this.findById(withdrawalId).session(session);
      if (!withdrawal) {
        throw new Error('Withdrawal not found');
      }

      if (withdrawal.status !== 'pending') {
        throw new Error('Withdrawal is not in pending status');
      }

      // Restore funds to wallet since they were deducted immediately upon withdrawal request
      const Wallet = mongoose.model('Wallet');
      const wallet = await Wallet.findById(withdrawal.walletId).session(session);
      if (!wallet) {
        throw new Error('Wallet not found for fund restoration');
      }

      const assetIndex = wallet.assets.findIndex((a: any) => a.symbol === withdrawal.cryptocurrency);
      if (assetIndex === -1) {
        throw new Error(`Asset ${withdrawal.cryptocurrency} not found in wallet`);
      }

      // Restore funds to appropriate balance based on withdrawal type
      switch (withdrawal.withdrawalType) {
        case 'balance':
          wallet.assets[assetIndex].balance = (wallet.assets[assetIndex].balance || 0) + withdrawal.amount;
          break;
        case 'interest':
          wallet.assets[assetIndex].interestBalance = (wallet.assets[assetIndex].interestBalance || 0) + withdrawal.amount;
          break;
        case 'commission':
          wallet.assets[assetIndex].commissionBalance = (wallet.assets[assetIndex].commissionBalance || 0) + withdrawal.amount;
          break;
      }

      await wallet.save({ session });

      // Update withdrawal status
      withdrawal.status = 'rejected';
      withdrawal.adminNotes = rejectionReason;

      // Add admin action to metadata
      if (!withdrawal.metadata.adminActions) {
        withdrawal.metadata.adminActions = [];
      }
      withdrawal.metadata.adminActions.push({
        action: 'reject',
        adminId: new mongoose.Types.ObjectId(adminId),
        timestamp: new Date(),
        notes: rejectionReason,
        previousStatus: 'pending',
        newStatus: 'rejected'
      });

      await withdrawal.save({ session });

      return withdrawal;
    });
  } finally {
    await session.endSession();
  }
};

// Static method to find withdrawals with populated investment package data
withdrawalSchema.statics.findWithInvestmentPackage = function(query: any = {}) {
  return this.find(query)
    .populate('userId', 'email firstName lastName phoneNumber country')
    .populate('walletId', 'assets')
    .populate('investmentPackageId', 'amount currency status activeDays totalEarned createdAt activatedAt')
    .sort({ createdAt: -1 });
};

// Instance method to get investment package details
withdrawalSchema.methods.getInvestmentPackageDetails = async function() {
  if (this.withdrawalType !== 'balance' || !this.investmentPackageId) {
    return null;
  }

  const InvestmentPackage = mongoose.model('InvestmentPackage');
  return await InvestmentPackage.findById(this.investmentPackageId);
};

// Instance method to validate withdrawal against investment package
withdrawalSchema.methods.validateAgainstInvestmentPackage = async function() {
  if (this.withdrawalType !== 'balance' || !this.investmentPackageId) {
    return { valid: true, message: 'No investment package validation required' };
  }

  const InvestmentPackage = mongoose.model('InvestmentPackage');
  const investmentPackage = await InvestmentPackage.findById(this.investmentPackageId);

  if (!investmentPackage) {
    return { valid: false, message: 'Investment package not found' };
  }

  if (!investmentPackage.userId.equals(this.userId)) {
    return { valid: false, message: 'Investment package does not belong to the user' };
  }

  if (investmentPackage.status !== 'active') {
    return { valid: false, message: 'Investment package is not active' };
  }

  if (investmentPackage.amount < this.amount) {
    return { valid: false, message: 'Insufficient balance in investment package' };
  }

  if (investmentPackage.currency !== this.cryptocurrency) {
    return { valid: false, message: 'Investment package currency mismatch' };
  }

  return { valid: true, message: 'Validation successful', investmentPackage };
};

// Static methods interface
interface IWithdrawalModel extends mongoose.Model<IWithdrawal> {
  getStatistics(userId?: string): Promise<any[]>;
  approveBalanceWithdrawal(withdrawalId: string, adminId: string, adminNotes?: string): Promise<IWithdrawal>;
  rejectWithdrawal(withdrawalId: string, adminId: string, rejectionReason: string): Promise<IWithdrawal>;
  findWithInvestmentPackage(query?: any): mongoose.Query<IWithdrawal[], IWithdrawal>;
}

const Withdrawal = mongoose.model<IWithdrawal, IWithdrawalModel>('Withdrawal', withdrawalSchema);

export default Withdrawal;
