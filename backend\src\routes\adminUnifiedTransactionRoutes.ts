import express from 'express';
import {
  getAdminTransactions,
  getAdminDeposits,
  getAdminWithdrawals,
  approveTransaction,
  rejectTransaction,
  updateTransactionAmount,
  getTransactionStats
} from '../controllers/adminUnifiedTransactionController';
import { protect, admin } from '../middleware/authMiddleware';
import { wrapController } from '../utils/routeWrapper';
import { rateLimit } from 'express-rate-limit';

const router = express.Router();

// Rate limiting for admin operations
const adminRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 admin requests per windowMs
  message: {
    success: false,
    message: 'Too many admin requests, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply protection and admin middleware to all routes
router.use(protect, admin, adminRateLimit);

// ===========================================
// ADMIN TRANSACTION OVERVIEW ROUTES
// ===========================================

/**
 * @route   GET /api/admin/transactions
 * @desc    Get all transactions for admin panel
 * @access  Admin
 */
router.get('/', wrapController(getAdminTransactions));

/**
 * @route   GET /api/admin/transactions/stats
 * @desc    Get transaction statistics for admin dashboard
 * @access  Admin
 */
router.get('/stats', wrapController(getTransactionStats));

// ===========================================
// ADMIN DEPOSIT MANAGEMENT ROUTES
// ===========================================

/**
 * @route   GET /api/admin/transactions/deposits
 * @desc    Get all deposits for admin panel
 * @access  Admin
 */
router.get('/deposits', wrapController(getAdminDeposits));

// ===========================================
// ADMIN WITHDRAWAL MANAGEMENT ROUTES
// ===========================================

/**
 * @route   GET /api/admin/transactions/withdrawals
 * @desc    Get all withdrawals for admin panel
 * @access  Admin
 */
router.get('/withdrawals', wrapController(getAdminWithdrawals));

// ===========================================
// ADMIN TRANSACTION ACTION ROUTES
// ===========================================

/**
 * @route   PUT /api/admin/transactions/:transactionId/approve
 * @desc    Approve a transaction (deposit or withdrawal)
 * @access  Admin
 */
router.put('/:transactionId/approve', wrapController(approveTransaction));

/**
 * @route   PUT /api/admin/transactions/:transactionId/reject
 * @desc    Reject a transaction (deposit or withdrawal)
 * @access  Admin
 */
router.put('/:transactionId/reject', wrapController(rejectTransaction));

/**
 * @route   PUT /api/admin/transactions/:transactionId/amount
 * @desc    Update transaction amount
 * @access  Admin
 */
router.put('/:transactionId/amount', wrapController(updateTransactionAmount));

export default router;
