import express from 'express';
import {
  createDeposit,
  createWithdrawal,
  getTransactionHistory,
  getTransactionDetails,
  getWalletBalances,
  getWithdrawableBalance,
  syncWalletData,
  validateWithdrawal
} from '../controllers/unifiedTransactionController';
import { protect } from '../middleware/authMiddleware';
import { wrapController } from '../utils/routeWrapper';
import { rateLimit } from 'express-rate-limit';

const router = express.Router();

// Rate limiting for transaction operations
const transactionRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 transaction requests per windowMs
  message: {
    success: false,
    message: 'Too many transaction requests, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const withdrawalRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 withdrawal requests per windowMs
  message: {
    success: false,
    message: 'Too many withdrawal requests, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const validationRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 20, // Limit each IP to 20 validation requests per windowMs
  message: {
    success: false,
    message: 'Too many validation requests, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply protection middleware to all routes
router.use(protect);

// ===========================================
// DEPOSIT ROUTES
// ===========================================

/**
 * @route   POST /api/transactions/deposit
 * @desc    Create a new deposit transaction
 * @access  Private
 */
router.post('/deposit', transactionRateLimit, wrapController(createDeposit));

// ===========================================
// WITHDRAWAL ROUTES
// ===========================================

/**
 * @route   POST /api/transactions/withdrawal/validate
 * @desc    Validate withdrawal request
 * @access  Private
 */
router.post('/withdrawal/validate', validationRateLimit, wrapController(validateWithdrawal));

/**
 * @route   POST /api/transactions/withdrawal
 * @desc    Create a new withdrawal transaction
 * @access  Private
 */
router.post('/withdrawal', withdrawalRateLimit, wrapController(createWithdrawal));

// ===========================================
// TRANSACTION HISTORY ROUTES
// ===========================================

/**
 * @route   GET /api/transactions/history
 * @desc    Get user transaction history
 * @access  Private
 */
router.get('/history', wrapController(getTransactionHistory));

/**
 * @route   GET /api/transactions/:transactionId
 * @desc    Get specific transaction details
 * @access  Private
 */
router.get('/:transactionId', wrapController(getTransactionDetails));

// ===========================================
// WALLET ROUTES
// ===========================================

/**
 * @route   GET /api/transactions/wallet/balances
 * @desc    Get user wallet balances
 * @access  Private
 */
router.get('/wallet/balances', wrapController(getWalletBalances));

/**
 * @route   GET /api/transactions/wallet/withdrawable/:cryptocurrency
 * @desc    Get withdrawable balance for specific cryptocurrency
 * @access  Private
 */
router.get('/wallet/withdrawable/:cryptocurrency', wrapController(getWithdrawableBalance));

/**
 * @route   POST /api/transactions/wallet/sync
 * @desc    Sync wallet data across all components
 * @access  Private
 */
router.post('/wallet/sync', wrapController(syncWalletData));

export default router;
