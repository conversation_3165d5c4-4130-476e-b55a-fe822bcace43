import express from 'express';
import {
  register,
  login,
  logout,
  refreshToken,
  getProfile,
  updateProfile,
  enableTwoFactor,
  verifyKyc,
  getReferrals
} from '../controllers/userController';
import { getWebSocketToken } from '../controllers/websocketController'; // Sử dụng cho Socket.IO
import {
  getReferralInfo,
  applyReferralCode
} from '../controllers/referralController';
import { protect } from '../middleware/authMiddleware';
import { cacheMiddleware, clearCache } from '../middleware/cacheMiddleware';
import {
  authRateLimit,
  loginRateLimit,
  registerRateLimit,
  sensitiveOperationRateLimit
} from '../middleware/rateLimitMiddleware';
import {
  validateUserRegistration,
  validateUserLogin,
  validateProfileUpdate
} from '../middleware/validationMiddleware';
import { wrapController } from '../utils/routeWrapper';

// Import email verification routes
import emailVerificationRoutes from './emailVerificationRoutes';

const router = express.Router();

// Test route
router.get('/test', (req, res) => {
  console.log('Test route hit');
  res.json({ message: 'Test route working' });
});

// Public routes with rate limiting and validation for security
router.post('/signup', registerRateLimit, validateUserRegistration, wrapController(register));
router.post('/register', registerRateLimit, validateUserRegistration, wrapController(register)); // Keep for backward compatibility
router.post('/login', wrapController(login));
router.post('/logout', authRateLimit, wrapController(logout));
router.post('/refresh-token', authRateLimit, wrapController(refreshToken)); // Refresh token endpoint
router.get('/ws-token', protect, authRateLimit, wrapController(getWebSocketToken));

// Protected routes with appropriate rate limiting and validation
router.get('/profile', protect, cacheMiddleware({ keyPrefix: 'api:user:profile:' }), wrapController(getProfile));
router.put('/profile', protect, sensitiveOperationRateLimit, validateProfileUpdate, clearCache('user:profile:'), wrapController(updateProfile));
router.post('/enable-2fa', protect, sensitiveOperationRateLimit, clearCache('user:profile:'), wrapController(enableTwoFactor));
router.post('/verify-kyc', protect, sensitiveOperationRateLimit, clearCache('user:profile:'), wrapController(verifyKyc));

// Referral routes
router.get('/referrals', protect, cacheMiddleware({ keyPrefix: 'api:user:referrals:' }), wrapController(getReferrals));
router.get('/referral-info', protect, wrapController(getReferralInfo));
router.post('/apply-referral', protect, wrapController(applyReferralCode));

// Email verification routes
router.use('/email-verification', emailVerificationRoutes);

export default router;
