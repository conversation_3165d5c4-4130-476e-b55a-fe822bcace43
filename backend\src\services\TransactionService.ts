import Transaction from '../models/transactionModel';
import Wallet from '../models/walletModel';
import { logger } from '../utils/logger';
import realTimeDataService from './realTimeDataService';

/**
 * Service for handling transaction operations
 */
class TransactionService {
  /**
   * Create a new deposit transaction
   * @param userId User ID
   * @param data Deposit data
   * @returns Created transaction
   */
  async createDeposit(userId: string, data: any): Promise<any> {
    try {
      const { asset, amount, walletAddress, network, txHash, receipt } = data;
      
      // Create transaction
      const transaction = await Transaction.create({
        userId,
        type: 'deposit',
        asset: asset.toUpperCase(),
        amount,
        status: 'pending',
        walletAddress,
        blockchainNetwork: network,
        txHash,
        userName: data.userName,
        userEmail: data.userEmail,
        metadata: {
          receiptUrl: receipt,
          source: 'direct_deposit'
        }
      });
      
      logger.info(`New deposit transaction created: ${transaction._id} for user: ${userId}`);
      
      // Send real-time notification
      await realTimeDataService.notifyNewDeposit(transaction);
      
      return transaction;
    } catch (error) {
      logger.error('Error creating deposit transaction:', error);
      throw error;
    }
  }
  
  /**
   * Create a new withdrawal transaction
   * @param userId User ID
   * @param data Withdrawal data
   * @returns Created transaction
   */
  async createWithdrawal(userId: string, data: any): Promise<any> {
    try {
      const { asset, amount, walletAddress, network, memo } = data;
      
      // Find user wallet
      const wallet = await Wallet.findOne({ userId });
      if (!wallet) {
        throw new Error('Wallet not found');
      }
      
      // Check if user has enough balance
      const assetIndex = wallet.assets.findIndex(a => a.symbol === asset);
      if (assetIndex === -1 || wallet.assets[assetIndex].balance < amount) {
        throw new Error('Insufficient balance');
      }
      
      // Deduct amount from wallet
      wallet.assets[assetIndex].balance -= amount;
      await wallet.save();
      
      // Create transaction
      const transaction = await Transaction.create({
        userId,
        type: 'withdrawal',
        asset: asset.toUpperCase(),
        amount,
        status: 'pending',
        walletAddress,
        blockchainNetwork: network,
        description: memo,
        userName: data.userName,
        userEmail: data.userEmail,
        metadata: {
          source: 'direct_withdrawal'
        }
      });
      
      logger.info(`New withdrawal transaction created: ${transaction._id} for user: ${userId}`);
      
      // Send real-time notification
      await realTimeDataService.notifyNewWithdrawal(transaction);
      
      return transaction;
    } catch (error) {
      logger.error('Error creating withdrawal transaction:', error);
      throw error;
    }
  }
  
  /**
   * Approve a deposit transaction
   * @param transactionId Transaction ID
   * @param adminId Admin user ID
   * @param adminNote Optional admin note
   * @returns Updated transaction
   */
  async approveDeposit(transactionId: string, adminId: string, adminNote?: string): Promise<any> {
    try {
      // Find the transaction
      const transaction = await Transaction.findById(transactionId);
      if (!transaction) {
        throw new Error('Transaction not found');
      }
      
      // Check if it's a deposit
      if (transaction.type !== 'deposit') {
        throw new Error('Transaction is not a deposit');
      }
      
      // Check if already approved
      if (transaction.status === 'approved') {
        return transaction;
      }
      
      // Store previous status for notification
      const previousStatus = transaction.status;
      
      // Update transaction status
      transaction.status = 'approved';
      transaction.adminId = adminId;
      if (adminNote) {
        if (!transaction.metadata) transaction.metadata = {};
        transaction.metadata.adminNote = adminNote;
      }
      
      await transaction.save();
      
      logger.info(`Deposit transaction ${transactionId} approved by admin ${adminId}`);
      
      // Update user wallet
      await this.updateUserWallet(
        transaction.userId.toString(), 
        transaction.asset, 
        transaction.amount
      );
      
      // Send real-time notification
      await realTimeDataService.notifyDepositStatusUpdate(transaction);
      
      return transaction;
    } catch (error) {
      logger.error(`Error approving deposit transaction ${transactionId}:`, error);
      throw error;
    }
  }
  
  /**
   * Reject a deposit transaction
   * @param transactionId Transaction ID
   * @param adminId Admin user ID
   * @param adminNote Optional admin note
   * @returns Updated transaction
   */
  async rejectDeposit(transactionId: string, adminId: string, adminNote?: string): Promise<any> {
    try {
      // Find the transaction
      const transaction = await Transaction.findById(transactionId);
      if (!transaction) {
        throw new Error('Transaction not found');
      }
      
      // Check if it's a deposit
      if (transaction.type !== 'deposit') {
        throw new Error('Transaction is not a deposit');
      }
      
      // Check if already rejected
      if (transaction.status === 'rejected') {
        return transaction;
      }
      
      // Store previous status for notification
      const previousStatus = transaction.status;
      
      // Update transaction status
      transaction.status = 'rejected';
      transaction.adminId = adminId;
      if (adminNote) {
        if (!transaction.metadata) transaction.metadata = {};
        transaction.metadata.adminNote = adminNote;
      }
      
      await transaction.save();
      
      logger.info(`Deposit transaction ${transactionId} rejected by admin ${adminId}`);
      
      // Send real-time notification
      await realTimeDataService.notifyDepositStatusUpdate(transaction);
      
      return transaction;
    } catch (error) {
      logger.error(`Error rejecting deposit transaction ${transactionId}:`, error);
      throw error;
    }
  }
  
  /**
   * Process a withdrawal transaction
   * @param transactionId Transaction ID
   * @param adminId Admin user ID
   * @param status New status (completed or rejected)
   * @param txHash Optional transaction hash
   * @param adminNote Optional admin note
   * @returns Updated transaction
   */
  async processWithdrawal(
    transactionId: string, 
    adminId: string, 
    status: 'completed' | 'rejected',
    txHash?: string,
    adminNote?: string
  ): Promise<any> {
    try {
      // Find the transaction
      const transaction = await Transaction.findById(transactionId);
      if (!transaction) {
        throw new Error('Transaction not found');
      }
      
      // Check if it's a withdrawal
      if (transaction.type !== 'withdrawal') {
        throw new Error('Transaction is not a withdrawal');
      }
      
      // Check if already processed
      if (transaction.status === status) {
        return transaction;
      }
      
      // Store previous status for notification
      const previousStatus = transaction.status;
      
      // Update transaction status
      transaction.status = status;
      transaction.adminId = adminId;
      if (txHash) transaction.txHash = txHash;
      if (adminNote) {
        if (!transaction.metadata) transaction.metadata = {};
        transaction.metadata.adminNote = adminNote;
      }
      
      await transaction.save();
      
      logger.info(`Withdrawal transaction ${transactionId} ${status} by admin ${adminId}`);
      
      // If rejected, refund the amount to user's wallet
      if (status === 'rejected') {
        await this.refundWithdrawal(
          transaction.userId.toString(),
          transaction.asset,
          transaction.amount
        );
      }
      
      // Send real-time notification
      await realTimeDataService.notifyWithdrawalStatusUpdate(transaction, previousStatus);
      
      return transaction;
    } catch (error) {
      logger.error(`Error processing withdrawal transaction ${transactionId}:`, error);
      throw error;
    }
  }
  
  /**
   * Update user wallet balance after deposit approval
   * @param userId User ID
   * @param asset Asset symbol
   * @param amount Amount to add
   */
  private async updateUserWallet(userId: string, asset: string, amount: number): Promise<void> {
    try {
      // Find or create wallet
      let wallet = await Wallet.findOne({ userId });
      
      if (!wallet) {
        wallet = await Wallet.create({
          userId,
          assets: [
            {
              symbol: asset,
              balance: amount,
              commissionBalance: 0,
              interestBalance: 0
            }
          ]
        });
        
        logger.info(`Created new wallet for user ${userId} with initial balance ${amount} ${asset}`);
        
        // Send real-time notification
        await realTimeDataService.notifyWalletUpdate(userId, asset, amount);
        return;
      }
      
      // Find asset in wallet
      const assetIndex = wallet.assets.findIndex(a => a.symbol === asset);
      
      if (assetIndex >= 0) {
        // Update existing asset
        wallet.assets[assetIndex].balance += amount;
        logger.info(`Updated wallet balance for user ${userId}: +${amount} ${asset}`);
      } else {
        // Add new asset
        wallet.assets.push({
          symbol: asset,
          balance: amount,
          commissionBalance: 0,
          interestBalance: 0
        });
        logger.info(`Added new asset ${asset} to wallet of user ${userId} with balance ${amount}`);
      }
      
      await wallet.save();
      
      // Send real-time notification
      const newBalance = assetIndex >= 0 ? wallet.assets[assetIndex].balance : amount;
      await realTimeDataService.notifyWalletUpdate(userId, asset, newBalance);
      
    } catch (error) {
      logger.error(`Error updating wallet for user ${userId}:`, error);
      throw error;
    }
  }
  
  /**
   * Refund a withdrawal amount to user wallet
   * @param userId User ID
   * @param asset Asset symbol
   * @param amount Amount to refund
   */
  private async refundWithdrawal(userId: string, asset: string, amount: number): Promise<void> {
    try {
      // Find wallet
      const wallet = await Wallet.findOne({ userId });
      if (!wallet) {
        logger.error(`Cannot refund withdrawal: wallet not found for user ${userId}`);
        return;
      }
      
      // Find asset in wallet
      const assetIndex = wallet.assets.findIndex(a => a.symbol === asset);
      
      if (assetIndex >= 0) {
        // Update existing asset
        wallet.assets[assetIndex].balance += amount;
        logger.info(`Refunded withdrawal to user ${userId}: +${amount} ${asset}`);
      } else {
        // Add new asset
        wallet.assets.push({
          symbol: asset,
          balance: amount,
          commissionBalance: 0,
          interestBalance: 0
        });
        logger.info(`Added new asset ${asset} to wallet of user ${userId} with refunded amount ${amount}`);
      }
      
      await wallet.save();
      
      // Send real-time notification
      const newBalance = assetIndex >= 0 ? wallet.assets[assetIndex].balance : amount;
      await realTimeDataService.notifyWalletUpdate(userId, asset, newBalance);
      
    } catch (error) {
      logger.error(`Error refunding withdrawal for user ${userId}:`, error);
      throw error;
    }
  }
}

export const transactionService = new TransactionService();
export default transactionService;