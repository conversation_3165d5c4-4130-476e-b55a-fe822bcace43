import * as cron from 'node-cron';
import InvestmentPackage from '../models/investmentPackageModel';
import AuditTrail from '../models/auditTrailModel';
import autoInvestmentService from './autoInvestmentService';
import depositMonitorService from './depositMonitorService';
import timeService from './timeService';
import withdrawalAuditService from './withdrawalAuditService';
import interestCalculationService from './interestCalculationService';
import { cryptoDataCollectionService } from './cryptoDataCollectionService';
import { logger } from '../utils/logger';

class CronService {
  private jobs: Map<string, cron.ScheduledTask> = new Map();

  /**
   * Initialize all cron jobs
   */
  initialize(): void {
    // Skip cron jobs in mock database mode to prevent timeout errors
    if (process.env.USE_MOCK_DATABASE === 'true') {
      console.log('🕐 Cron service skipped in MOCK DATABASE mode');
      return;
    }

    this.setupDailyInterestCalculation();
    this.setupPackageActivation();
    this.setupSystemMaintenance();
    this.setupAutoInvestmentProcessing();
    this.setupDepositMonitoring();

    console.log('🕐 Cron service initialized with all jobs');
  }

  /**
   * Enhanced Daily interest calculation at 03:00 Turkey time
   * Cron: "0 3 * * *" (every day at 03:00)
   *
   * Features:
   * - Atomic database transactions with rollback support
   * - Wallet interest balance updates
   * - Transaction record creation
   * - Payment history logging
   * - Comprehensive error handling and retry logic
   */
  private setupDailyInterestCalculation(): void {
    const job = cron.schedule('0 3 * * *', async () => {
      try {
        logger.info('🔄 Starting enhanced daily interest calculation with full transaction safety...');

        // Use the enhanced interest calculation service
        const summary = await interestCalculationService.processAllActivePackages();

        // Log comprehensive summary
        logger.info('✅ Enhanced daily interest calculation completed', {
          totalPackages: summary.totalPackages,
          successfulCalculations: summary.successfulCalculations,
          failedCalculations: summary.failedCalculations,
          totalInterestPaid: summary.totalInterestPaid.toFixed(6),
          errorCount: summary.errors.length,
          duration: `${summary.duration}ms`,
          timestamp: summary.timestamp.toISOString(),
          turkeyTime: timeService.formatTurkeyTime(summary.timestamp)
        });

        // Log individual errors if any
        if (summary.errors.length > 0) {
          logger.error('❌ Interest calculation errors encountered:', {
            errorCount: summary.errors.length,
            errors: summary.errors
          });
        }

        // Send system notification for monitoring
        try {
          const notificationService = require('./notificationService').default;
          await notificationService.notifySystemEvent('DAILY_INTEREST_COMPLETED', {
            summary,
            timestamp: summary.timestamp
          });
        } catch (notificationError) {
          logger.warn('Failed to send system notification for interest calculation', {
            error: notificationError.message
          });
        }

      } catch (error: any) {
        logger.error('❌ Enhanced daily interest calculation process failed', {
          error: error.message,
          stack: error.stack,
          timestamp: timeService.getTurkeyTime().toISOString()
        });

        // Send error notification
        try {
          const notificationService = require('./notificationService').default;
          await notificationService.notifySystemEvent('DAILY_INTEREST_FAILED', {
            error: error.message,
            timestamp: timeService.getTurkeyTime()
          });
        } catch (notificationError) {
          logger.error('Failed to send error notification', {
            originalError: error.message,
            notificationError: notificationError.message
          });
        }
      }
    }, {
      timezone: 'Europe/Istanbul'
    });

    this.jobs.set('dailyInterestCalculation', job);
    logger.info('📅 Enhanced daily interest calculation job scheduled for 03:00 Turkey time');
  }

  /**
   * Package activation check (every 5 minutes)
   * Activates packages that should be activated
   */
  private setupPackageActivation(): void {
    const job = cron.schedule('*/5 * * * *', async () => {
      try {
        const turkeyTime = timeService.getTurkeyTime();

        // Find packages that should be activated
        const pendingPackages = await InvestmentPackage.find({
          status: 'pending',
          activatedAt: { $lte: turkeyTime }
        });

        if (pendingPackages.length === 0) {
          return; // No packages to activate
        }

        console.info(`🔄 Activating ${pendingPackages.length} pending packages...`);

        let activated = 0;
        for (const pkg of pendingPackages) {
          try {
            pkg.status = 'active';
            pkg.lastCalculatedAt = turkeyTime;
            await pkg.save();

            // Create audit trail
            await AuditTrail.createAuditLog({
              userId: pkg.userId,
              action: 'INVESTMENT_ACTIVATED',
              packageId: pkg._id as any,
              amount: pkg.amount,
              currency: pkg.currency,
              details: {
                activatedAt: pkg.activatedAt,
                interestRate: pkg.interestRate,
                compoundEnabled: pkg.compoundEnabled
              }
            });

            activated++;
            console.debug(`✅ Package ${pkg._id} activated`);
          } catch (error: any) {
            console.error(`❌ Error activating package ${pkg._id}:`, error);
          }
        }

        if (activated > 0) {
          console.info(`✅ Successfully activated ${activated} packages`);
        }

      } catch (error: any) {
        console.error('❌ Package activation check failed:', error);
      }
    }, {
      timezone: 'Europe/Istanbul'
    });

    this.jobs.set('packageActivation', job);
    console.info('📅 Package activation job scheduled (every 5 minutes)');
  }

  /**
   * System maintenance tasks (daily at 02:00)
   */
  private setupSystemMaintenance(): void {
    const job = cron.schedule('0 2 * * *', async () => {
      try {
        console.info('🔧 Starting system maintenance tasks...');

        const startTime = Date.now();

        // Clean old audit trails (keep last 90 days)
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - 90);

        const deletedAudits = await AuditTrail.deleteMany({
          timestamp: { $lt: cutoffDate }
        });

        // Update package statistics
        const packageStats = await InvestmentPackage.aggregate([
          {
            $group: {
              _id: '$status',
              count: { $sum: 1 },
              totalAmount: { $sum: '$amount' },
              totalEarned: { $sum: '$totalEarned' }
            }
          }
        ]);

        // Clear crypto API cache
        const cryptoApiService = require('./cryptoApiService').default;
        cryptoApiService.clearCache();

        // Clean old crypto price data (keep last 90 days)
        const deletedPriceRecords = await cryptoDataCollectionService.cleanOldPriceData(90);

        const duration = Date.now() - startTime;

        console.info(`✅ System maintenance completed:`, {
          deletedAuditTrails: deletedAudits.deletedCount,
          deletedPriceRecords,
          packageStats,
          duration: `${duration}ms`
        });

      } catch (error: any) {
        console.error('❌ System maintenance failed:', error);
      }
    }, {
      timezone: 'Europe/Istanbul'
    });

    this.jobs.set('systemMaintenance', job);
    console.info('📅 System maintenance job scheduled for 02:00 Turkey time');
  }

  /**
   * Auto-investment processing every 5 minutes
   * Cron: every 5 minutes
   */
  private setupAutoInvestmentProcessing(): void {
    const job = cron.schedule('*/5 * * * *', async () => {
      try {
        console.info('🤖 Starting auto-investment processing...');
        const startTime = Date.now();

        await autoInvestmentService.processConfirmedDeposits();

        const duration = Date.now() - startTime;
        console.info(`✅ Auto-investment processing completed in ${duration}ms`);

      } catch (error: any) {
        console.error('❌ Auto-investment processing failed:', error);
      }
    }, {
      timezone: 'Europe/Istanbul'
    });

    this.jobs.set('autoInvestmentProcessing', job);
    console.info('📅 Auto-investment processing job scheduled for every 5 minutes');
  }

  /**
   * Deposit monitoring startup
   * Starts monitoring when cron service initializes
   */
  private setupDepositMonitoring(): void {
    // Start deposit monitoring immediately
    setTimeout(async () => {
      try {
        console.info('🔍 Starting deposit monitoring service...');
        await depositMonitorService.startMonitoring();
        console.info('✅ Deposit monitoring service started');
      } catch (error: any) {
        console.error('❌ Failed to start deposit monitoring:', error);
      }
    }, 5000); // Wait 5 seconds after startup

    console.info('📅 Deposit monitoring startup scheduled');
  }

  /**
   * Enhanced manual interest calculation (for testing/admin use)
   * Uses the same enhanced service as the automated cron job
   */
  async calculateInterestManually(packageId?: string): Promise<{
    success: boolean;
    processed: number;
    totalInterest: number;
    errors: any[];
  }> {
    try {
      logger.info('🔄 Manual enhanced interest calculation started...', {
        packageId: packageId || 'all_active',
        timestamp: timeService.getTurkeyTime().toISOString()
      });

      if (packageId) {
        // Calculate for specific package
        const pkg = await InvestmentPackage.findOne({ _id: packageId, status: 'active' });

        if (!pkg) {
          return {
            success: false,
            processed: 0,
            totalInterest: 0,
            errors: [{ error: `Package not found or not active: ${packageId}` }]
          };
        }

        // Use enhanced calculation service for single package with transaction
        const { executeTransaction } = require('../utils/transactionManager');
        const result = await executeTransaction(async (session: any) => {
          return await interestCalculationService.calculatePackageInterest(pkg, session);
        });

        return {
          success: result.success,
          processed: result.success ? 1 : 0,
          totalInterest: result.success ? result.dailyInterest : 0,
          errors: result.success ? [] : [{ packageId, error: result.error }]
        };
      } else {
        // Calculate for all active packages using enhanced service
        const summary = await interestCalculationService.processAllActivePackages();

        logger.info('✅ Manual enhanced interest calculation completed', {
          totalPackages: summary.totalPackages,
          successful: summary.successfulCalculations,
          failed: summary.failedCalculations,
          totalInterest: summary.totalInterestPaid.toFixed(6),
          duration: `${summary.duration}ms`
        });

        return {
          success: summary.failedCalculations === 0,
          processed: summary.successfulCalculations,
          totalInterest: summary.totalInterestPaid,
          errors: summary.errors
        };
      }

    } catch (error: any) {
      logger.error('❌ Manual enhanced interest calculation failed:', {
        error: error.message,
        stack: error.stack,
        packageId: packageId || 'all_active'
      });

      return {
        success: false,
        processed: 0,
        totalInterest: 0,
        errors: [{ error: error.message }]
      };
    }
  }

  /**
   * Get job status
   */
  getJobStatus(): { [jobName: string]: { running: boolean; nextRun?: Date } } {
    const status: { [jobName: string]: { running: boolean; nextRun?: Date } } = {};

    this.jobs.forEach((job, name) => {
      status[name] = {
        running: (job as any).running || false,
        nextRun: (job as any).nextDate ? (job as any).nextDate().toDate() : undefined
      };
    });

    return status;
  }

  /**
   * Stop all cron jobs
   */
  stopAll(): void {
    this.jobs.forEach((job, name) => {
      job.stop();
      console.info(`🛑 Stopped cron job: ${name}`);
    });

    this.jobs.clear();
    console.info('🛑 All cron jobs stopped');
  }

  /**
   * Start all cron jobs
   */
  startAll(): void {
    this.jobs.forEach((job, name) => {
      job.start();
      console.info(`▶️ Started cron job: ${name}`);
    });

    console.info('▶️ All cron jobs started');
  }
}

export default new CronService();
