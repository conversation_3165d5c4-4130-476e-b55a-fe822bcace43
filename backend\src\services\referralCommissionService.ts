import mongoose from 'mongoose';
import ReferralCommission from '../models/referralCommission';
import ReferralCommissionConfig from '../models/referralCommissionConfig';
import User from '../models/User';
import Investment from '../models/investmentModel';
import logger from '../utils/logger';
import { socketService } from '../index';

/**
 * T<PERSON>h và tạo hoa hồng cho người giới thiệu khi có khoản đầu tư mới được phê duyệt
 */
export const calculateAndCreateCommission = async (investmentId: string): Promise<boolean> => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    // Lấy thông tin khoản đầu tư
    const investment = await Investment.findById(investmentId).session(session);
    if (!investment) {
      logger.error(`Investment not found: ${investmentId}`);
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // Ki<PERSON>m tra trạng thái khoản đầu tư
    if (investment.status !== 'approved') {
      logger.info(`Investment ${investmentId} is not approved, skipping commission calculation`);
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // Lấy thông tin người đầu tư
    const investor = await User.findById(investment.userId).session(session);
    if (!investor) {
      logger.error(`Investor not found for investment: ${investmentId}`);
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // Kiểm tra xem người đầu tư có người giới thiệu không
    if (!investor.referrerId) {
      logger.info(`Investor ${investor._id} has no referrer, skipping commission calculation`);
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // Lấy thông tin người giới thiệu
    const referrer = await User.findById(investor.referrerId).session(session);
    if (!referrer) {
      logger.error(`Referrer not found for investor: ${investor._id}`);
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // Lấy cấu hình hoa hồng dựa trên level của người giới thiệu
    const referrerLevel = referrer.level || 1;
    const commissionConfig = await ReferralCommissionConfig.findOne({
      level: referrerLevel,
      isActive: true,
    }).session(session);

    if (!commissionConfig) {
      logger.error(`No active commission config found for level: ${referrerLevel}`);
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // Kiểm tra số tiền đầu tư có đủ điều kiện nhận hoa hồng không
    if (investment.amount < commissionConfig.minInvestmentAmount) {
      logger.info(
        `Investment amount ${investment.amount} is less than minimum required ${commissionConfig.minInvestmentAmount}, skipping commission`
      );
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // Tính số tiền hoa hồng
    const commissionAmount = (investment.amount * commissionConfig.commissionRate) / 100;

    // Kiểm tra xem đã có hoa hồng cho khoản đầu tư này chưa
    const existingCommission = await ReferralCommission.findOne({
      investmentId: investment._id,
    }).session(session);

    if (existingCommission) {
      logger.info(`Commission already exists for investment: ${investmentId}`);
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // Tạo bản ghi hoa hồng mới
    const newCommission = new ReferralCommission({
      referrerId: referrer._id,
      referredId: investor._id,
      investmentId: investment._id,
      amount: commissionAmount,
      commissionRate: commissionConfig.commissionRate,
      level: referrerLevel,
      status: 'approved', // Tự động phê duyệt hoa hồng
      currency: investment.currency,
      description: `Referral commission for investment ${investment._id}`,
    });

    await newCommission.save({ session });

    // Cập nhật tổng hoa hồng cho người giới thiệu
    referrer.totalCommission = (referrer.totalCommission || 0) + commissionAmount;
    
    // Cập nhật cả withdrawable balance để người dùng có thể rút tiền hoa hồng
    if (!referrer.withdrawableBalance) referrer.withdrawableBalance = {};
    if (!referrer.withdrawableBalance[investment.currency]) {
      referrer.withdrawableBalance[investment.currency] = commissionAmount;
    } else {
      referrer.withdrawableBalance[investment.currency] += commissionAmount;
    }
    
    await referrer.save({ session });
    
    // Gửi thông báo thời gian thực cho người giới thiệu
    try {
      socketService.emitToUser(referrer._id.toString(), 'commission:new', {
        amount: commissionAmount,
        currency: investment.currency,
        from: `${investor.firstName} ${investor.lastName}`,
        timestamp: new Date()
      });
    } catch (socketError) {
      logger.warn('Failed to emit socket notification:', socketError);
    }

    await session.commitTransaction();
    session.endSession();

    logger.info(
      `Successfully created commission of ${commissionAmount} ${investment.currency} for referrer ${referrer._id}`
    );
    return true;
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    logger.error('Error calculating commission:', error);
    return false;
  }
};

/**
 * Lấy danh sách hoa hồng của một người dùng
 */
export const getUserCommissions = async (userId: string) => {
  try {
    return await ReferralCommission.find({ referrerId: userId })
      .populate('referredId', 'name email')
      .populate('investmentId', 'amount currency status')
      .sort({ createdAt: -1 });
  } catch (error) {
    logger.error(`Error getting commissions for user ${userId}:`, error);
    throw error;
  }
};

/**
 * Lấy tổng hoa hồng của một người dùng
 */
export const getUserTotalCommission = async (userId: string) => {
  try {
    const result = await ReferralCommission.aggregate([
      { $match: { referrerId: new mongoose.Types.ObjectId(userId), status: 'approved' } },
      { $group: { _id: '$currency', total: { $sum: '$amount' } } },
    ]);
    return result;
  } catch (error) {
    logger.error(`Error getting total commission for user ${userId}:`, error);
    throw error;
  }
};

/**
 * Yatırım onaylandığında referral komisyonlarını hesapla ve dağıt
 */
export const processInvestmentApproval = async (investmentId: string): Promise<void> => {
  try {
    // Yatırımı bul
    const investment = await Investment.findById(investmentId);
    if (!investment) {
      logger.error(`Investment not found: ${investmentId}`);
      return;
    }

    // Yatırım onaylandıysa komisyonları hesapla
    if (investment.status === 'approved') {
      const success = await calculateAndCreateCommission(investmentId);
      if (success) {
        logger.info(`Successfully processed referral commissions for investment: ${investmentId}`);
      } else {
        logger.warn(`Failed to process referral commissions for investment: ${investmentId}`);
      }
    }
  } catch (error) {
    logger.error(`Error processing investment approval: ${error}`);
  }
};

/**
 * Komisyon çekildiğinde kullanıcının bakiyesini güncelle
 */
export const processCommissionWithdrawal = async (userId: string, amount: number, currency: string): Promise<boolean> => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    // Kullanıcıyı bul
    const user = await User.findById(userId).session(session);
    if (!user) {
      logger.error(`User not found: ${userId}`);
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // Kullanıcının çekilebilir bakiyesini kontrol et
    if (!user.withdrawableBalance || !user.withdrawableBalance[currency] || user.withdrawableBalance[currency] < amount) {
      logger.error(`Insufficient withdrawable balance for user ${userId}`);
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // Bakiyeyi güncelle
    user.withdrawableBalance[currency] -= amount;
    user.totalCommission = (user.totalCommission || 0) - amount;
    
    await user.save({ session });
    
    // İşlemi kaydet
    await session.commitTransaction();
    session.endSession();
    
    // Gerçek zamanlı bildirim gönder
    try {
      socketService.emitToUser(userId, 'commission:withdrawal', {
        amount,
        currency,
        timestamp: new Date(),
        remainingBalance: user.withdrawableBalance[currency]
      });
    } catch (socketError) {
      logger.warn('Failed to emit socket notification:', socketError);
    }
    
    return true;
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    logger.error(`Error processing commission withdrawal: ${error}`);
    return false;
  }
};

export default {
  calculateAndCreateCommission,
  getUserCommissions,
  getUserTotalCommission,
  processInvestmentApproval,
  processCommissionWithdrawal
};
