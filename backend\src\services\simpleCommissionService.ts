import mongoose from 'mongoose';
import User from '../models/User';
import Wallet from '../models/walletModel';
import Transaction from '../models/transactionModel';
import logger from '../utils/logger';

/**
 * Basit Komisyon Sistemi
 * - Platform Komisyonu: %1 (tü<PERSON>)
 * - Referans Komisyonu: %3 (sadece referans kazançları)
 */

// Sabit komisyon oranları
export const COMMISSION_RATES = {
  PLATFORM: 0.01,  // %1 platform komisyonu
  REFERRAL: 0.03   // %3 referans komisyonu
} as const;

export type CommissionType = 'platform' | 'referral';

interface CommissionCalculation {
  originalAmount: number;
  commissionAmount: number;
  netAmount: number;
  rate: number;
  type: CommissionType;
}

/**
 * Platform komisyonu hesapla (%1)
 */
export const calculatePlatformCommission = (amount: number): CommissionCalculation => {
  const commissionAmount = amount * COMMISSION_RATES.PLATFORM;
  const netAmount = amount - commissionAmount;

  return {
    originalAmount: amount,
    commissionAmount,
    netAmount,
    rate: COMMISSION_RATES.PLATFORM,
    type: 'platform'
  };
};

/**
 * Referans komisyonu hesapla (%3)
 * NOT: Referans komisyonu platform tarafından ödenir, yatırımcının parasından düşülmez
 */
export const calculateReferralCommission = (amount: number): CommissionCalculation => {
  const commissionAmount = amount * COMMISSION_RATES.REFERRAL;

  return {
    originalAmount: amount,
    commissionAmount,
    netAmount: amount, // Referans komisyonu platform tarafından ödenir
    rate: COMMISSION_RATES.REFERRAL,
    type: 'referral'
  };
};

/**
 * Platform komisyonu uygula ve kaydet
 */
export const applyPlatformCommission = async (
  userId: mongoose.Types.ObjectId,
  amount: number,
  currency: string,
  description: string,
  session?: mongoose.ClientSession
): Promise<CommissionCalculation> => {
  const calculation = calculatePlatformCommission(amount);

  try {
    // Get user's wallet for walletId
    let wallet = await Wallet.findOne({ userId }).session(session || undefined);
    if (!wallet) {
      wallet = new Wallet({
        userId,
        assets: [],
        totalCommissionEarned: 0,
        totalInterestEarned: 0
      });
      if (session) {
        await wallet.save({ session });
      } else {
        await wallet.save();
      }
    }

    // Debug logging for wallet
    logger.info('💳 Platform commission wallet info', {
      walletId: wallet._id?.toString(),
      walletExists: !!wallet._id,
      userId: userId.toString(),
      assetsCount: wallet.assets?.length || 0
    });

    // Platform komisyonu transaction'ı oluştur
    const commissionTransaction = new Transaction({
      userId,
      walletId: wallet._id, // Add required walletId
      type: 'commission',
      asset: currency,
      amount: calculation.commissionAmount,
      status: 'completed',
      description: `Platform commission (1%): ${description}`,
      metadata: {
        commissionType: 'platform',
        originalAmount: calculation.originalAmount,
        commissionRate: calculation.rate,
        relatedDescription: description
      }
    });

    if (session) {
      await commissionTransaction.save({ session });
    } else {
      await commissionTransaction.save();
    }

    logger.info('Platform commission applied', {
      userId: userId.toString(),
      amount: calculation.commissionAmount,
      currency,
      originalAmount: amount
    });

    return calculation;
  } catch (error) {
    logger.error('Error applying platform commission:', error);
    throw error;
  }
};

/**
 * Referans komisyonu uygula ve kaydet
 */
export const applyReferralCommission = async (
  referrerId: mongoose.Types.ObjectId,
  referredId: mongoose.Types.ObjectId,
  amount: number,
  currency: string,
  description: string,
  session?: mongoose.ClientSession,
  depositId?: mongoose.Types.ObjectId // Add optional depositId parameter
): Promise<CommissionCalculation | null> => {
  try {
    logger.info('🔍 STARTING applyReferralCommission', {
      referrerId: referrerId.toString(),
      referredId: referredId.toString(),
      amount,
      currency,
      description,
      hasSession: !!session
    });

    // Referrer'ı kontrol et
    const referrer = await User.findById(referrerId).session(session || undefined);
    logger.info('👤 Referrer lookup result', {
      referrerId: referrerId.toString(),
      referrerFound: !!referrer,
      referrerName: referrer ? `${referrer.firstName} ${referrer.lastName}` : 'N/A'
    });

    if (!referrer) {
      logger.warn('❌ Referrer not found for commission', { referrerId: referrerId.toString() });
      return null;
    }

    // Check if referral commission already exists for this user (first deposit only)
    const ReferralCommission = (await import('../models/referralCommission')).default;
    const existingCommission = await ReferralCommission.findOne({
      referrerId,
      referredId
    }).session(session || undefined);

    logger.info('🔍 Checking existing commission', {
      referrerId: referrerId.toString(),
      referredId: referredId.toString(),
      existingCommissionFound: !!existingCommission,
      existingCommissionId: existingCommission?._id?.toString(),
      existingCommissionAmount: existingCommission?.amount,
      existingCommissionStatus: existingCommission?.status
    });

    if (existingCommission) {
      logger.warn('❌ Referral commission already exists for this user', {
        referrerId: referrerId.toString(),
        referredId: referredId.toString(),
        existingAmount: existingCommission.amount,
        existingCurrency: existingCommission.currency,
        existingStatus: existingCommission.status,
        createdAt: existingCommission.createdAt
      });
      return null; // Commission already paid
    }

    logger.info('✅ No existing commission found - proceeding with creation', {
      referrerId: referrerId.toString(),
      referredId: referredId.toString()
    });

    const calculation = calculateReferralCommission(amount);

    // Referrer'ın wallet'ını bul veya oluştur
    let wallet = await Wallet.findOne({ userId: referrerId }).session(session || undefined);
    if (!wallet) {
      wallet = new Wallet({
        userId: referrerId,
        assets: [],
        totalCommissionEarned: 0,
        totalInterestEarned: 0
      });
      // Save wallet first to get _id
      if (session) {
        await wallet.save({ session });
      } else {
        await wallet.save();
      }
    }

    // Asset'i bul veya oluştur - Use index-based approach
    let assetIndex = wallet.assets.findIndex(a => a.symbol.toUpperCase() === currency.toUpperCase());
    if (assetIndex === -1) {
      // Create new asset
      const newAsset = {
        symbol: currency.toUpperCase(),
        balance: 0,
        interestBalance: 0,
        commissionBalance: 0,
        mode: 'commission' as const
      };
      wallet.assets.push(newAsset);
      assetIndex = wallet.assets.length - 1;

      logger.info('🆕 Created new asset for referrer wallet', {
        referrerId: referrerId.toString(),
        symbol: currency.toUpperCase(),
        assetIndex
      });
    }

    // Komisyon bakiyesini güncelle - Direct array access
    wallet.assets[assetIndex].commissionBalance = (wallet.assets[assetIndex].commissionBalance || 0) + calculation.commissionAmount;
    wallet.totalCommissionEarned = (wallet.totalCommissionEarned || 0) + calculation.commissionAmount;

    logger.info('💰 Updated referrer commission balance', {
      referrerId: referrerId.toString(),
      symbol: currency.toUpperCase(),
      commissionAmount: calculation.commissionAmount,
      newCommissionBalance: wallet.assets[assetIndex].commissionBalance,
      totalCommissionEarned: wallet.totalCommissionEarned
    });
    
    // Save wallet with detailed logging
    logger.info('💾 Saving referrer wallet with commission update', {
      referrerId: referrerId.toString(),
      walletId: wallet._id?.toString(),
      assetsCount: wallet.assets.length,
      totalCommissionEarned: wallet.totalCommissionEarned,
      hasSession: !!session
    });

    if (session) {
      await wallet.save({ session });
    } else {
      await wallet.save();
    }

    logger.info('✅ Referrer wallet saved successfully', {
      referrerId: referrerId.toString(),
      walletId: wallet._id?.toString()
    });

    // Debug logging for wallet
    logger.info('💳 Wallet info before creating transaction', {
      walletId: wallet._id?.toString(),
      walletExists: !!wallet._id,
      userId: referrerId.toString(),
      assetsCount: wallet.assets.length
    });

    // Referans komisyonu transaction'ı oluştur
    const commissionTransaction = new Transaction({
      userId: referrerId,
      walletId: wallet._id, // Add required walletId
      type: 'commission',
      asset: currency,
      amount: calculation.commissionAmount,
      status: 'completed',
      description: `Referral commission (3%): ${description}`,
      metadata: {
        commissionType: 'referral',
        referredUserId: referredId.toString(),
        originalAmount: calculation.originalAmount,
        commissionRate: calculation.rate,
        relatedDescription: description
      }
    });

    if (session) {
      await commissionTransaction.save({ session });
    } else {
      await commissionTransaction.save();
    }

    // Create referral commission record
    const referralCommissionRecord = new ReferralCommission({
      referrerId,
      referredId,
      investmentId: depositId || referredId, // Use depositId if available, otherwise use referredId as fallback
      amount: calculation.commissionAmount,
      commissionRate: calculation.rate * 100, // Store as percentage
      level: 1, // Simple system - only level 1
      status: 'approved',
      currency: currency.toUpperCase(),
      description: `Simple referral commission (3%): ${description}`
    });

    logger.info('📋 Creating referral commission record', {
      referrerId: referrerId.toString(),
      referredId: referredId.toString(),
      investmentId: (depositId || referredId).toString(),
      amount: calculation.commissionAmount,
      currency: currency.toUpperCase()
    });

    if (session) {
      await referralCommissionRecord.save({ session });
    } else {
      await referralCommissionRecord.save();
    }

    // User'ın toplam komisyonunu güncelle
    referrer.totalCommission = (referrer.totalCommission || 0) + calculation.commissionAmount;
    if (session) {
      await referrer.save({ session });
    } else {
      await referrer.save();
    }

    logger.info('Referral commission applied', {
      referrerId: referrerId.toString(),
      referredId: referredId.toString(),
      amount: calculation.commissionAmount,
      currency,
      originalAmount: amount
    });

    return calculation;
  } catch (error) {
    logger.error('Error applying referral commission:', error);
    throw error;
  }
};

/**
 * Yatırım için komisyonları uygula (platform + referans)
 */
export const applyInvestmentCommissions = async (
  userId: mongoose.Types.ObjectId,
  amount: number,
  currency: string,
  session?: mongoose.ClientSession,
  depositId?: mongoose.Types.ObjectId // Add optional depositId parameter
): Promise<{
  platformCommission: CommissionCalculation;
  referralCommission: CommissionCalculation | null;
  finalAmount: number;
}> => {
  try {
    logger.info('🔍 STARTING applyInvestmentCommissions', {
      userId: userId.toString(),
      amount,
      currency,
      hasSession: !!session
    });

    // Platform komisyonu uygula
    const platformCommission = await applyPlatformCommission(
      userId,
      amount,
      currency,
      'Investment deposit',
      session
    );

    logger.info('✅ Platform commission applied', {
      userId: userId.toString(),
      platformCommissionAmount: platformCommission.commissionAmount
    });

    // Referans komisyonu kontrol et ve uygula
    const user = await User.findById(userId).session(session || undefined);
    logger.info('👤 User lookup result', {
      userId: userId.toString(),
      userFound: !!user,
      userFirstName: user?.firstName,
      userLastName: user?.lastName,
      hasReferrerId: !!user?.referrerId,
      referrerId: user?.referrerId?.toString()
    });

    let referralCommission: CommissionCalculation | null = null;

    if (user?.referrerId) {
      logger.info('🎯 User has referrer - attempting referral commission', {
        userId: userId.toString(),
        referrerId: user.referrerId.toString(),
        amount,
        currency
      });

      referralCommission = await applyReferralCommission(
        user.referrerId,
        userId,
        amount,
        currency,
        `Investment from ${user.firstName} ${user.lastName}`,
        session,
        depositId // Pass depositId to applyReferralCommission
      );

      if (referralCommission) {
        logger.info('✅ Referral commission created successfully', {
          referrerId: user.referrerId.toString(),
          referredId: userId.toString(),
          commissionAmount: referralCommission.commissionAmount,
          currency
        });
      } else {
        logger.warn('❌ Referral commission NOT created', {
          referrerId: user.referrerId.toString(),
          referredId: userId.toString(),
          reason: 'applyReferralCommission returned null'
        });
      }
    } else {
      logger.info('ℹ️ User has no referrer - skipping referral commission', {
        userId: userId.toString(),
        userFound: !!user,
        referrerId: user?.referrerId
      });
    }

    // Final amount = original - platform commission
    const finalAmount = platformCommission.netAmount;

    logger.info('🎉 Investment commissions completed', {
      userId: userId.toString(),
      originalAmount: amount,
      platformCommission: platformCommission.commissionAmount,
      referralCommission: referralCommission?.commissionAmount || 0,
      finalAmount,
      referralCommissionCreated: !!referralCommission
    });

    return {
      platformCommission,
      referralCommission,
      finalAmount
    };
  } catch (error) {
    logger.error('❌ Error applying investment commissions:', error);
    throw error;
  }
};

/**
 * Çekim için platform komisyonu uygula
 */
export const applyWithdrawalCommission = async (
  userId: mongoose.Types.ObjectId,
  amount: number,
  currency: string,
  session?: mongoose.ClientSession
): Promise<CommissionCalculation> => {
  return await applyPlatformCommission(
    userId,
    amount,
    currency,
    'Withdrawal',
    session
  );
};

/**
 * Komisyon oranlarını getir
 */
export const getCommissionRates = () => {
  return {
    platform: {
      rate: COMMISSION_RATES.PLATFORM,
      percentage: `${(COMMISSION_RATES.PLATFORM * 100).toFixed(1)}%`,
      description: 'Platform commission applied to all transactions'
    },
    referral: {
      rate: COMMISSION_RATES.REFERRAL,
      percentage: `${(COMMISSION_RATES.REFERRAL * 100).toFixed(1)}%`,
      description: 'Referral commission for bringing new users'
    }
  };
};

/**
 * Kullanıcının toplam komisyon kazancını getir
 */
export const getUserCommissionEarnings = async (userId: mongoose.Types.ObjectId) => {
  try {
    const wallet = await Wallet.findOne({ userId });
    if (!wallet) {
      return { totalCommission: 0, assets: [] };
    }

    const commissionAssets = wallet.assets
      .filter(asset => (asset.commissionBalance || 0) > 0)
      .map(asset => ({
        symbol: asset.symbol,
        commissionBalance: asset.commissionBalance || 0
      }));

    return {
      totalCommission: wallet.totalCommissionEarned || 0,
      assets: commissionAssets
    };
  } catch (error) {
    logger.error('Error getting user commission earnings:', error);
    throw error;
  }
};

export default {
  calculatePlatformCommission,
  calculateReferralCommission,
  applyPlatformCommission,
  applyReferralCommission,
  applyInvestmentCommissions,
  applyWithdrawalCommission,
  getCommissionRates,
  getUserCommissionEarnings,
  COMMISSION_RATES
};
