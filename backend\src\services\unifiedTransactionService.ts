import mongoose from 'mongoose';
import { UnifiedTransaction, IUnifiedTransaction } from '../models/unifiedTransactionModel';
import { getEnhancedSocketService } from './enhancedSocketService';
import logger from '../utils/logger';
// import { WalletService } from './walletService';
// import { InvestmentService } from './investmentService';

/**
 * Unified Transaction Service
 * Handles all deposit and withdrawal operations with real-time synchronization
 */
export class UnifiedTransactionService {
  // private walletService: WalletService;
  // private investmentService: InvestmentService;

  constructor() {
    // this.walletService = new WalletService();
    // this.investmentService = new InvestmentService();
  }

  /**
   * Create a new deposit transaction
   */
  async createDeposit(depositData: {
    userId: string;
    walletId: string;
    cryptocurrency: string;
    network: string;
    amount: number;
    walletAddress: string;
    txHash?: string;
    autoInvestmentEnabled?: boolean;
  }): Promise<IUnifiedTransaction> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      // Calculate USD value (integrate with price service)
      const usdValue = await this.calculateUSDValue(depositData.cryptocurrency, depositData.amount);

      // Create deposit transaction
      const transaction = new UnifiedTransaction({
        userId: new mongoose.Types.ObjectId(depositData.userId),
        walletId: new mongoose.Types.ObjectId(depositData.walletId),
        type: 'deposit',
        subType: 'investment',
        cryptocurrency: depositData.cryptocurrency.toUpperCase(),
        network: depositData.network,
        amount: depositData.amount,
        usdValue,
        networkFee: 0, // Deposits typically don't have network fees
        netAmount: depositData.amount,
        walletAddress: depositData.walletAddress,
        txHash: depositData.txHash,
        confirmations: 0,
        requiredConfirmations: this.getRequiredConfirmations(depositData.cryptocurrency),
        status: 'pending',
        adminStatus: 'pending_review',
        autoInvestmentEnabled: depositData.autoInvestmentEnabled ?? true,
        metadata: {
          validationResults: {
            balanceCheck: true,
            lockPeriodCheck: true,
            minimumAmountCheck: depositData.amount >= this.getMinimumAmount(depositData.cryptocurrency),
            addressValidation: true
          },
          syncStatus: {
            walletUpdated: false,
            balanceUpdated: false,
            investmentCreated: false,
            notificationSent: false,
            lastSyncAt: new Date()
          }
        }
      });

      await transaction.save({ session });
      await session.commitTransaction();

      // Send real-time notification
      const socketService = getEnhancedSocketService();
      socketService.notifyDepositCreated(depositData.userId, transaction);

      logger.info(`Deposit transaction created: ${transaction.transactionId}`, {
        userId: depositData.userId,
        amount: depositData.amount,
        cryptocurrency: depositData.cryptocurrency
      });

      return transaction;
    } catch (error) {
      await session.abortTransaction();
      logger.error('Error creating deposit transaction:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Create a new withdrawal transaction
   */
  async createWithdrawal(withdrawalData: {
    userId: string;
    walletId: string;
    cryptocurrency: string;
    network: string;
    amount: number;
    walletAddress: string;
    withdrawalType: 'balance' | 'interest' | 'commission';
    investmentPackageId?: string;
  }): Promise<IUnifiedTransaction> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      // Validate withdrawal eligibility
      const validation = await this.validateWithdrawal(withdrawalData);
      if (!validation.isValid) {
        throw new Error(`Withdrawal validation failed: ${validation.errors.join(', ')}`);
      }

      // Calculate fees and USD value
      const networkFee = await this.calculateNetworkFee(withdrawalData.cryptocurrency, withdrawalData.network);
      const usdValue = await this.calculateUSDValue(withdrawalData.cryptocurrency, withdrawalData.amount);

      // Create withdrawal transaction
      const transaction = new UnifiedTransaction({
        userId: new mongoose.Types.ObjectId(withdrawalData.userId),
        walletId: new mongoose.Types.ObjectId(withdrawalData.walletId),
        type: 'withdrawal',
        subType: withdrawalData.withdrawalType,
        cryptocurrency: withdrawalData.cryptocurrency.toUpperCase(),
        network: withdrawalData.network,
        amount: withdrawalData.amount,
        usdValue,
        networkFee,
        netAmount: withdrawalData.amount - networkFee,
        walletAddress: withdrawalData.walletAddress,
        confirmations: 0,
        requiredConfirmations: 1, // Withdrawals need admin approval
        status: 'pending',
        adminStatus: 'pending_review',
        autoInvestmentEnabled: false,
        investmentPackageId: withdrawalData.investmentPackageId ? 
          new mongoose.Types.ObjectId(withdrawalData.investmentPackageId) : undefined,
        metadata: {
          lockValidation: validation.lockInfo,
          feeCalculation: {
            baseFee: 0,
            networkFee,
            totalFee: networkFee,
            feePercentage: (networkFee / withdrawalData.amount) * 100
          },
          validationResults: validation.validationResults,
          syncStatus: {
            walletUpdated: false,
            balanceUpdated: false,
            investmentCreated: false,
            notificationSent: false,
            lastSyncAt: new Date()
          }
        }
      });

      await transaction.save({ session });
      await session.commitTransaction();

      // Send real-time notification
      const socketService = getEnhancedSocketService();
      socketService.notifyWithdrawalCreated(withdrawalData.userId, transaction);

      logger.info(`Withdrawal transaction created: ${transaction.transactionId}`, {
        userId: withdrawalData.userId,
        amount: withdrawalData.amount,
        cryptocurrency: withdrawalData.cryptocurrency,
        withdrawalType: withdrawalData.withdrawalType
      });

      return transaction;
    } catch (error) {
      await session.abortTransaction();
      logger.error('Error creating withdrawal transaction:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Approve a transaction (admin action)
   */
  async approveTransaction(
    transactionId: string, 
    adminId: string, 
    notes?: string
  ): Promise<IUnifiedTransaction> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const transaction = await UnifiedTransaction.findOne({ transactionId }).session(session);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      // Update transaction status
      await transaction.updateAdminStatus('approved', adminId, notes);
      
      // Process based on transaction type
      if (transaction.type === 'deposit') {
        await this.processApprovedDeposit(transaction, session);
      } else if (transaction.type === 'withdrawal') {
        await this.processApprovedWithdrawal(transaction, session);
      }

      await session.commitTransaction();

      // Send real-time notifications
      const socketService = getEnhancedSocketService();
      if (transaction.type === 'deposit') {
        socketService.notifyDepositApproved(transaction.userId.toString(), transaction);
      } else {
        socketService.notifyWithdrawalApproved(transaction.userId.toString(), transaction);
      }

      logger.info(`Transaction approved: ${transactionId}`, {
        type: transaction.type,
        amount: transaction.amount,
        adminId
      });

      return transaction;
    } catch (error) {
      await session.abortTransaction();
      logger.error('Error approving transaction:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Update transaction amount (admin action)
   */
  async updateTransactionAmount(
    transactionId: string,
    newAmount: number,
    adminId: string,
    notes?: string
  ): Promise<IUnifiedTransaction> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const transaction = await UnifiedTransaction.findOne({ transactionId }).session(session);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      const previousAmount = transaction.amount;
      
      // Update amount and recalculate values
      transaction.amount = newAmount;
      transaction.usdValue = await this.calculateUSDValue(transaction.cryptocurrency, newAmount);
      
      if (transaction.type === 'withdrawal') {
        transaction.netAmount = newAmount - transaction.networkFee;
      } else {
        transaction.netAmount = newAmount;
      }

      // Add admin action to metadata
      if (!transaction.metadata.adminActions) {
        transaction.metadata.adminActions = [];
      }
      
      transaction.metadata.adminActions.push({
        action: 'amount_update',
        adminId: new mongoose.Types.ObjectId(adminId),
        timestamp: new Date(),
        notes: notes || `Amount updated from ${previousAmount} to ${newAmount}`,
        previousAmount,
        newAmount
      });

      await transaction.save({ session });
      await session.commitTransaction();

      // Send real-time notifications for wallet sync
      const socketService = getEnhancedSocketService();
      socketService.notifyTransactionAmountUpdated(
        transaction.userId.toString(), 
        transaction, 
        previousAmount
      );

      logger.info(`Transaction amount updated: ${transactionId}`, {
        previousAmount,
        newAmount,
        adminId
      });

      return transaction;
    } catch (error) {
      await session.abortTransaction();
      logger.error('Error updating transaction amount:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Get transactions for admin panel
   */
  async getAdminTransactions(filters: {
    type?: 'deposit' | 'withdrawal';
    status?: string;
    cryptocurrency?: string;
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<{
    transactions: IUnifiedTransaction[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }> {
    const page = filters.page || 1;
    const limit = filters.limit || 20;
    const skip = (page - 1) * limit;

    // Build query
    const query: any = {};
    
    if (filters.type) query.type = filters.type;
    if (filters.status) query.status = filters.status;
    if (filters.cryptocurrency) query.cryptocurrency = filters.cryptocurrency.toUpperCase();
    
    if (filters.search) {
      query.$or = [
        { transactionId: { $regex: filters.search, $options: 'i' } },
        { txHash: { $regex: filters.search, $options: 'i' } },
        { walletAddress: { $regex: filters.search, $options: 'i' } }
      ];
    }

    // Execute queries
    const [transactions, total] = await Promise.all([
      UnifiedTransaction.find(query)
        .populate('userId', 'name email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      UnifiedTransaction.countDocuments(query)
    ]);

    return {
      transactions: transactions as IUnifiedTransaction[],
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Get user transaction history
   */
  async getUserTransactions(
    userId: string,
    filters: {
      type?: 'deposit' | 'withdrawal';
      cryptocurrency?: string;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{
    transactions: IUnifiedTransaction[];
    pagination: any;
  }> {
    const page = filters.page || 1;
    const limit = filters.limit || 20;
    const skip = (page - 1) * limit;

    const query: any = { userId: new mongoose.Types.ObjectId(userId) };
    
    if (filters.type) query.type = filters.type;
    if (filters.cryptocurrency) query.cryptocurrency = filters.cryptocurrency.toUpperCase();

    const [transactions, total] = await Promise.all([
      UnifiedTransaction.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      UnifiedTransaction.countDocuments(query)
    ]);

    return {
      transactions: transactions as IUnifiedTransaction[],
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  // ===========================================
  // PRIVATE HELPER METHODS
  // ===========================================

  private async processApprovedDeposit(transaction: IUnifiedTransaction, session: any): Promise<void> {
    // Import enhanced wallet service
    const { default: EnhancedWalletService } = await import('./enhancedWalletService');
    const walletService = new EnhancedWalletService();

    // Update wallet balance
    await walletService.updateBalance(
      transaction.userId.toString(),
      transaction.cryptocurrency,
      transaction.amount,
      'credit',
      session
    );

    // Create investment package if auto-investment is enabled
    if (transaction.autoInvestmentEnabled) {
      try {
        // Mock investment package creation for now
        const mockInvestment = {
          _id: `inv_${Date.now()}`,
          userId: transaction.userId.toString(),
          amount: transaction.amount,
          cryptocurrency: transaction.cryptocurrency,
          transactionId: transaction.transactionId,
          status: 'active',
          createdAt: new Date()
        };

        transaction.investmentPackageId = mockInvestment._id as any;
        await transaction.save({ session });

        // Notify about investment creation
        const socketService = getEnhancedSocketService();
        socketService.notifyInvestmentCreated(transaction.userId.toString(), mockInvestment);
      } catch (investmentError) {
        logger.error('Error creating investment package:', investmentError);
        // Continue without failing the deposit
      }
    }

    // Update sync status
    transaction.metadata.syncStatus = {
      walletUpdated: true,
      balanceUpdated: true,
      investmentCreated: transaction.autoInvestmentEnabled,
      notificationSent: true,
      lastSyncAt: new Date()
    };

    await transaction.save({ session });
  }

  private async processApprovedWithdrawal(transaction: IUnifiedTransaction, session: any): Promise<void> {
    // Import enhanced wallet service
    const { default: EnhancedWalletService } = await import('./enhancedWalletService');
    const walletService = new EnhancedWalletService();

    // Deduct from wallet balance
    await walletService.updateBalance(
      transaction.userId.toString(),
      transaction.cryptocurrency,
      transaction.amount,
      'debit',
      session
    );

    // Update investment package if applicable
    if (transaction.investmentPackageId && transaction.subType === 'balance') {
      try {
        // Mock investment package processing for now
        logger.info(`Processing withdrawal for investment package: ${transaction.investmentPackageId}`);
      } catch (investmentError) {
        logger.error('Error processing investment withdrawal:', investmentError);
        // Continue without failing the withdrawal
      }
    }

    // Update sync status
    transaction.metadata.syncStatus = {
      walletUpdated: true,
      balanceUpdated: true,
      investmentCreated: false,
      notificationSent: true,
      lastSyncAt: new Date()
    };

    await transaction.save({ session });
  }

  private async validateWithdrawal(withdrawalData: any): Promise<{
    isValid: boolean;
    errors: string[];
    lockInfo?: any;
    validationResults: any;
  }> {
    const errors: string[] = [];
    const validationResults = {
      balanceCheck: false,
      lockPeriodCheck: false,
      minimumAmountCheck: false,
      addressValidation: false
    };

    // Check minimum amount
    const minAmount = this.getMinimumAmount(withdrawalData.cryptocurrency);
    if (withdrawalData.amount < minAmount) {
      errors.push(`Minimum withdrawal amount is ${minAmount} ${withdrawalData.cryptocurrency}`);
    } else {
      validationResults.minimumAmountCheck = true;
    }

    // Check wallet balance
    const { default: EnhancedWalletService } = await import('./enhancedWalletService');
    const walletService = new EnhancedWalletService();

    const hasBalance = await walletService.checkBalance(
      withdrawalData.userId,
      withdrawalData.cryptocurrency,
      withdrawalData.amount
    );
    
    if (!hasBalance) {
      errors.push('Insufficient balance');
    } else {
      validationResults.balanceCheck = true;
    }

    // Check lock period for balance withdrawals
    let lockInfo = null;
    if (withdrawalData.withdrawalType === 'balance' && withdrawalData.investmentPackageId) {
      lockInfo = await this.investmentService.checkLockPeriod(withdrawalData.investmentPackageId);
      if (lockInfo.isLocked) {
        errors.push(`Investment is locked for ${lockInfo.daysRemaining} more days`);
      } else {
        validationResults.lockPeriodCheck = true;
      }
    } else {
      validationResults.lockPeriodCheck = true;
    }

    // Validate wallet address (basic validation)
    if (this.isValidWalletAddress(withdrawalData.walletAddress, withdrawalData.cryptocurrency)) {
      validationResults.addressValidation = true;
    } else {
      errors.push('Invalid wallet address');
    }

    return {
      isValid: errors.length === 0,
      errors,
      lockInfo,
      validationResults
    };
  }

  private async calculateUSDValue(cryptocurrency: string, amount: number): Promise<number> {
    // Integration with price service would go here
    // For now, return a mock value
    const mockPrices: { [key: string]: number } = {
      'BTC': 50000,
      'ETH': 3000,
      'USDT': 1,
      'BNB': 300,
      'SOL': 100,
      'DOGE': 0.1,
      'TRX': 0.1
    };

    return amount * (mockPrices[cryptocurrency] || 1);
  }

  private async calculateNetworkFee(cryptocurrency: string, network: string): Promise<number> {
    // Network fee calculation logic
    const fees: { [key: string]: number } = {
      'BTC': 0.0005,
      'ETH': 0.002,
      'USDT': 1,
      'BNB': 0.001,
      'SOL': 0.00025,
      'DOGE': 1,
      'TRX': 1
    };

    return fees[cryptocurrency] || 0;
  }

  private getRequiredConfirmations(cryptocurrency: string): number {
    const confirmations: { [key: string]: number } = {
      'BTC': 6,
      'ETH': 12,
      'USDT': 12,
      'BNB': 15,
      'SOL': 32,
      'DOGE': 6,
      'TRX': 19
    };

    return confirmations[cryptocurrency] || 3;
  }

  private getMinimumAmount(cryptocurrency: string): number {
    const minimums: { [key: string]: number } = {
      'BTC': 0.001,
      'ETH': 0.01,
      'USDT': 10,
      'BNB': 0.1,
      'SOL': 0.1,
      'DOGE': 100,
      'TRX': 100
    };

    return minimums[cryptocurrency] || 0.001;
  }

  private isValidWalletAddress(address: string, cryptocurrency: string): boolean {
    // Basic wallet address validation
    if (!address || address.length < 10) return false;
    
    // Add more sophisticated validation based on cryptocurrency
    return true;
  }
}

export default UnifiedTransactionService;
