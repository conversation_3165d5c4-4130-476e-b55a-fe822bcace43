import { cacheService } from '../services/cacheService';
import { logger } from './logger';

/**
 * Clear all wallet-related cache for a specific user
 * @param userId - The user ID to clear cache for
 * @param reason - Reason for cache clearing (for logging)
 */
export const clearWalletCache = (userId: string, reason: string = 'wallet update'): void => {
  try {
    // Define all possible wallet cache keys for the user
    const walletCacheKeys = [
      `wallet:info:${userId}`,
      `api:wallet:info:${userId}`,
      `wallet:transactions:${userId}`,
      `api:wallet:transactions:${userId}`,
      `wallet:balance:${userId}`, // Legacy cache key
      `api:wallet:balance:${userId}`, // Legacy cache key
      `wallet:earned:${userId}`,
      `api:wallet:earned:${userId}`,
      `wallet:withdrawable:${userId}`,
      `api:wallet:withdrawable:${userId}`
    ];

    // Delete specific cache keys
    let deletedCount = 0;
    walletCacheKeys.forEach(cacheKey => {
      if (cacheService.delete(cacheKey)) {
        deletedCount++;
      }
    });

    // Clear cache by prefix for comprehensive cleanup
    const prefixDeletedCount1 = cacheService.invalidateByPrefix(`wallet:${userId}`);
    const prefixDeletedCount2 = cacheService.invalidateByPrefix(`api:wallet:${userId}`);

    const totalDeleted = deletedCount + prefixDeletedCount1 + prefixDeletedCount2;

    logger.info(`Cleared wallet cache for user ${userId}`, {
      reason,
      specificKeysDeleted: deletedCount,
      prefixKeysDeleted: prefixDeletedCount1 + prefixDeletedCount2,
      totalDeleted
    });

  } catch (error) {
    logger.error(`Error clearing wallet cache for user ${userId}:`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      reason
    });
  }
};

/**
 * Clear transaction-related cache for a specific user
 * @param userId - The user ID to clear cache for
 * @param reason - Reason for cache clearing (for logging)
 */
export const clearTransactionCache = (userId: string, reason: string = 'transaction update'): void => {
  try {
    // Define transaction cache keys
    const transactionCacheKeys = [
      `wallet:transactions:${userId}`,
      `api:wallet:transactions:${userId}`,
      `transactions:${userId}`,
      `api:transactions:${userId}`
    ];

    // Delete specific cache keys
    let deletedCount = 0;
    transactionCacheKeys.forEach(cacheKey => {
      if (cacheService.delete(cacheKey)) {
        deletedCount++;
      }
    });

    // Clear cache by prefix
    const prefixDeletedCount = cacheService.invalidateByPrefix(`transactions:${userId}`);

    const totalDeleted = deletedCount + prefixDeletedCount;

    logger.info(`Cleared transaction cache for user ${userId}`, {
      reason,
      specificKeysDeleted: deletedCount,
      prefixKeysDeleted: prefixDeletedCount,
      totalDeleted
    });

  } catch (error) {
    logger.error(`Error clearing transaction cache for user ${userId}:`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      reason
    });
  }
};

/**
 * Clear all user-related cache (wallet + transactions + other data)
 * @param userId - The user ID to clear cache for
 * @param reason - Reason for cache clearing (for logging)
 */
export const clearUserCache = (userId: string, reason: string = 'user data update'): void => {
  try {
    // Clear wallet cache
    clearWalletCache(userId, reason);
    
    // Clear transaction cache
    clearTransactionCache(userId, reason);

    // Clear other user-related cache
    const userCacheKeys = [
      `user:${userId}`,
      `api:user:${userId}`,
      `user:profile:${userId}`,
      `api:user:profile:${userId}`,
      `user:stats:${userId}`,
      `api:user:stats:${userId}`
    ];

    let deletedCount = 0;
    userCacheKeys.forEach(cacheKey => {
      if (cacheService.delete(cacheKey)) {
        deletedCount++;
      }
    });

    // Clear cache by prefix
    const prefixDeletedCount = cacheService.invalidateByPrefix(`user:${userId}`);

    logger.info(`Cleared all user cache for user ${userId}`, {
      reason,
      userKeysDeleted: deletedCount,
      prefixKeysDeleted: prefixDeletedCount
    });

  } catch (error) {
    logger.error(`Error clearing user cache for user ${userId}:`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      reason
    });
  }
};

/**
 * Clear referral-related cache for a user
 * @param userId - The user ID to clear cache for
 * @param reason - Reason for cache clearing (for logging)
 */
export const clearReferralCache = (userId: string, reason: string = 'referral update'): void => {
  try {
    const referralCacheKeys = [
      `referral:stats:${userId}`,
      `api:referral:stats:${userId}`,
      `referral:tree:${userId}`,
      `api:referral:tree:${userId}`,
      `referral:commissions:${userId}`,
      `api:referral:commissions:${userId}`
    ];

    let deletedCount = 0;
    referralCacheKeys.forEach(cacheKey => {
      if (cacheService.delete(cacheKey)) {
        deletedCount++;
      }
    });

    // Clear cache by prefix
    const prefixDeletedCount = cacheService.invalidateByPrefix(`referral:${userId}`);

    logger.info(`Cleared referral cache for user ${userId}`, {
      reason,
      specificKeysDeleted: deletedCount,
      prefixKeysDeleted: prefixDeletedCount,
      totalDeleted: deletedCount + prefixDeletedCount
    });

  } catch (error) {
    logger.error(`Error clearing referral cache for user ${userId}:`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      reason
    });
  }
};

/**
 * Clear cache for multiple users (useful for bulk operations)
 * @param userIds - Array of user IDs to clear cache for
 * @param reason - Reason for cache clearing (for logging)
 */
export const clearMultipleUserCache = (userIds: string[], reason: string = 'bulk update'): void => {
  try {
    userIds.forEach(userId => {
      clearUserCache(userId, reason);
    });

    logger.info(`Cleared cache for ${userIds.length} users`, {
      reason,
      userIds: userIds.slice(0, 10) // Log first 10 user IDs to avoid log spam
    });

  } catch (error) {
    logger.error(`Error clearing cache for multiple users:`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      reason,
      userCount: userIds.length
    });
  }
};

/**
 * Clear admin-related cache
 * @param reason - Reason for cache clearing (for logging)
 */
export const clearAdminCache = (reason: string = 'admin data update'): void => {
  try {
    // Clear admin dashboard cache
    const adminCacheKeys = [
      'admin:dashboard',
      'api:admin:dashboard',
      'admin:stats',
      'api:admin:stats',
      'admin:users',
      'api:admin:users',
      'admin:transactions',
      'api:admin:transactions',
      'admin:referrals',
      'api:admin:referrals'
    ];

    let deletedCount = 0;
    adminCacheKeys.forEach(cacheKey => {
      if (cacheService.delete(cacheKey)) {
        deletedCount++;
      }
    });

    // Clear cache by prefix
    const prefixDeletedCount1 = cacheService.invalidateByPrefix('admin:');
    const prefixDeletedCount2 = cacheService.invalidateByPrefix('api:admin:');

    logger.info(`Cleared admin cache`, {
      reason,
      specificKeysDeleted: deletedCount,
      prefixKeysDeleted: prefixDeletedCount1 + prefixDeletedCount2,
      totalDeleted: deletedCount + prefixDeletedCount1 + prefixDeletedCount2
    });

  } catch (error) {
    logger.error(`Error clearing admin cache:`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      reason
    });
  }
};

/**
 * Clear system-wide cache (use with caution)
 * @param reason - Reason for cache clearing (for logging)
 */
export const clearAllCache = (reason: string = 'system maintenance'): void => {
  try {
    cacheService.clear();
    
    logger.warn(`Cleared ALL system cache`, {
      reason,
      warning: 'This clears all cached data system-wide'
    });

  } catch (error) {
    logger.error(`Error clearing all cache:`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      reason
    });
  }
};
