import React, { useState, useEffect } from 'react';
import {
  Box,
  Flex,
  Text,
  Button,
  Alert,
  AlertIcon,
  AlertDescription,
  useToast,
  HStack,
  Icon,
  IconButton,
  Collapse,
  useDisclosure
} from '@chakra-ui/react';
import { FaUserShield, FaSignOutAlt, FaTimes, FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { adminApiService } from '../services/adminApi';
import useAuth from '../hooks/useAuth';
import { useImpersonation } from '../hooks/useImpersonation';

interface ImpersonationBannerProps {
  isVisible?: boolean;
}

const ImpersonationBanner: React.FC<ImpersonationBannerProps> = ({ isVisible = true }) => {
  const [isReturning, setIsReturning] = useState(false);
  const { isOpen, onToggle } = useDisclosure({ defaultIsOpen: true });
  const { isImpersonating, isHidden, adminInfo, refreshStatus } = useImpersonation();
  const toast = useToast();
  const navigate = useNavigate();
  const { login } = useAuth();

  // Refresh status when component mounts (only once)
  useEffect(() => {
    refreshStatus();
  }, []); // Empty dependency array to run only once

  const handleReturnToAdmin = async () => {
    setIsReturning(true);
    
    try {
      const response = await adminApiService.returnToAdmin();
      
      if (response.data && response.data.data) {
        const adminData = response.data.data;
        
        // Update auth context with admin data
        await login(adminData.email, '', adminData);
        
        // Clear impersonation state
        localStorage.removeItem('impersonationAdminInfo');
        localStorage.removeItem('impersonationBannerHidden');
        refreshStatus();
        
        toast({
          title: 'Success',
          description: response.data.message || 'Successfully returned to admin panel',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });

        // Redirect to admin dashboard
        navigate('/admin');
      }
    } catch (error: any) {
      console.error('Return to admin error:', error);
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to return to admin panel',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsReturning(false);
    }
  };

  const handleHideBanner = () => {
    localStorage.setItem('impersonationBannerHidden', 'true');
    refreshStatus();
  };

  const handleShowBanner = () => {
    localStorage.removeItem('impersonationBannerHidden');
    refreshStatus();
  };

  const handleToggleCollapse = () => {
    onToggle();
  };

  if (!isVisible || !isImpersonating) {
    return null;
  }

  // If banner is hidden, show a small toggle button
  if (isHidden) {
    return (
      <Box
        position="fixed"
        top={4}
        right={4}
        zIndex={9999}
      >
        <Button
          size="sm"
          colorScheme="orange"
          leftIcon={<FaUserShield />}
          onClick={handleShowBanner}
          boxShadow="lg"
          borderRadius="full"
        >
          Admin Mode
        </Button>
      </Box>
    );
  }

  return (
    <Box
      position="fixed"
      top={0}
      left={0}
      right={0}
      zIndex={9999}
      bg="orange.500"
      color="white"
      boxShadow="md"
    >
      {/* Collapsed header bar */}
      <Flex
        align="center"
        justify="space-between"
        px={4}
        py={2}
        cursor="pointer"
        onClick={handleToggleCollapse}
        _hover={{ bg: "orange.600" }}
        transition="background-color 0.2s"
      >
        <HStack spacing={3}>
          <Icon as={FaUserShield} boxSize={4} />
          <Text fontWeight="bold" fontSize="sm">
            Admin Impersonation Mode
          </Text>
          <Icon as={isOpen ? FaChevronUp : FaChevronDown} boxSize={3} />
        </HStack>

        <HStack spacing={2}>
          <IconButton
            aria-label="Hide banner"
            icon={<FaTimes />}
            size="xs"
            variant="ghost"
            colorScheme="whiteAlpha"
            onClick={(e) => {
              e.stopPropagation();
              handleHideBanner();
            }}
            _hover={{
              bg: "rgba(255,255,255,0.2)"
            }}
          />
        </HStack>
      </Flex>

      {/* Collapsible content */}
      <Collapse in={isOpen} animateOpacity>
        <Box px={4} pb={3} pt={1}>
          <Flex
            maxW="1200px"
            mx="auto"
            align="center"
            justify="space-between"
            flexDir={{ base: "column", md: "row" }}
            gap={{ base: 2, md: 0 }}
          >
            <HStack spacing={3} flex={1}>
              {adminInfo && (
                <Text fontSize="sm" opacity={0.9}>
                  Logged in as user by admin: {adminInfo.adminName || adminInfo.adminEmail}
                </Text>
              )}
            </HStack>

            <Button
              size="sm"
              variant="outline"
              colorScheme="whiteAlpha"
              leftIcon={<FaSignOutAlt />}
              onClick={handleReturnToAdmin}
              isLoading={isReturning}
              loadingText="Returning..."
              _hover={{
                bg: "rgba(255,255,255,0.2)",
                borderColor: "white"
              }}
            >
              Return to Admin
            </Button>
          </Flex>
        </Box>
      </Collapse>
    </Box>
  );
};

export default React.memo(ImpersonationBanner);
