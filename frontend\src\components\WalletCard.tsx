import React from 'react';
import ModernWalletCard from './ModernWalletCard';

// BACKUP: Original WalletCard.tsx backed up as WalletCard.tsx.backup

interface WalletAsset {
  symbol: string;
  balance: number;
  interestBalance: number;
  commissionBalance: number;
}

interface WalletCardProps {
  asset: WalletAsset;
  onDeposit?: (currency: string, walletData?: any) => void;
  onWithdraw?: (currency: string, walletData?: any) => void;
}

const WalletCard: React.FC<WalletCardProps> = ({
  asset,
  onDeposit,
  onWithdraw,
}) => {
  // Use the new modern wallet card design
  return (
    <ModernWalletCard
      asset={asset}
      onDeposit={onDeposit}
      onWithdraw={onWithdraw}
    />
  );
};

export default WalletCard;
