import { useState, useEffect, useCallback } from 'react';
import { fetchCryptoPrices, getUSDRateSync } from '../utils/exchangeRates';

interface CryptoPricesState {
  prices: { [symbol: string]: number };
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

interface UseCryptoPricesReturn extends CryptoPricesState {
  refreshPrices: () => Promise<void>;
  getPrice: (symbol: string) => number;
}

/**
 * Custom hook for managing cryptocurrency prices
 * Fetches prices from API and provides caching and error handling
 */
export const useCryptoPrices = (): UseCryptoPricesReturn => {
  const [state, setState] = useState<CryptoPricesState>({
    prices: {},
    isLoading: true,
    error: null,
    lastUpdated: null,
  });

  /**
   * Fetch prices from API
   */
  const refreshPrices = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const prices = await fetchCryptoPrices();
      setState({
        prices,
        isLoading: false,
        error: null,
        lastUpdated: new Date(),
      });
    } catch (error) {
      console.error('Error fetching crypto prices:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch prices',
      }));
    }
  }, []);

  /**
   * Get price for a specific cryptocurrency
   */
  const getPrice = useCallback((symbol: string): number => {
    const apiPrice = state.prices[symbol.toUpperCase()];
    if (apiPrice !== undefined) {
      return apiPrice;
    }
    
    // Fallback to sync rate if not found in API prices
    return getUSDRateSync(symbol);
  }, [state.prices]);

  /**
   * Initial load and periodic refresh
   */
  useEffect(() => {
    refreshPrices();
    
    // Refresh prices every 5 minutes
    const interval = setInterval(refreshPrices, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, [refreshPrices]);

  return {
    ...state,
    refreshPrices,
    getPrice,
  };
};

/**
 * Hook for getting a single cryptocurrency price
 * Optimized for components that only need one price
 */
export const useCryptoPrice = (symbol: string) => {
  const [price, setPrice] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadPrice = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const prices = await fetchCryptoPrices();
        const symbolPrice = prices[symbol.toUpperCase()];
        
        if (symbolPrice !== undefined) {
          setPrice(symbolPrice);
        } else {
          // Fallback to sync rate
          setPrice(getUSDRateSync(symbol));
        }
      } catch (err) {
        console.error(`Error fetching price for ${symbol}:`, err);
        setError(err instanceof Error ? err.message : 'Failed to fetch price');
        // Use fallback price
        setPrice(getUSDRateSync(symbol));
      } finally {
        setIsLoading(false);
      }
    };

    loadPrice();
  }, [symbol]);

  return { price, isLoading, error };
};

export default useCryptoPrices;
