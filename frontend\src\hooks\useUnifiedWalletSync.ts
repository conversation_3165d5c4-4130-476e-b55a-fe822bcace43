import { useState, useEffect, useCallback } from 'react';
import { walletService } from '../services/walletService';
import { enhancedWalletService } from '../services/enhancedWalletService';
import { socketService } from '../services/socket-service';

/**
 * Unified Wallet Sync Hook
 * Integrates with the new unified transaction system and real-time WebSocket updates
 */

interface WalletSyncState {
  balances: any;
  stats: any;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  syncStatus: 'idle' | 'syncing' | 'synced' | 'error';
}

export const useUnifiedWalletSync = () => {
  const [state, setState] = useState<WalletSyncState>({
    balances: null,
    stats: null,
    loading: false,
    error: null,
    lastUpdated: null,
    syncStatus: 'idle'
  });

  const updateState = useCallback((updates: Partial<WalletSyncState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  /**
   * Fetch wallet data from unified transaction system
   */
  const fetchWalletData = useCallback(async (forceRefresh: boolean = false) => {
    try {
      updateState({ loading: true, syncStatus: 'syncing', error: null });

      console.log(`🔄 useUnifiedWalletSync: Fetching wallet data (force: ${forceRefresh})`);

      // Use enhanced wallet service
      const response = await walletService.getEnhancedWalletBalances();
      
      if (response && response.balances) {
        updateState({
          balances: response.balances,
          stats: response.stats,
          loading: false,
          syncStatus: 'synced',
          error: null,
          lastUpdated: new Date()
        });

        console.log('✅ useUnifiedWalletSync: Wallet data fetched successfully:', {
          balancesCount: Object.keys(response.balances).length,
          totalUSD: response.stats?.totalUSDValue || 0
        });
      } else {
        throw new Error('Invalid response format');
      }

    } catch (error: any) {
      console.error('❌ useUnifiedWalletSync: Error fetching wallet data:', error);
      updateState({
        loading: false,
        syncStatus: 'error',
        error: error.message || 'Failed to fetch wallet data'
      });
    }
  }, [updateState]);

  /**
   * Force sync wallet data
   */
  const forceSyncWallet = useCallback(async () => {
    try {
      updateState({ loading: true, syncStatus: 'syncing' });

      console.log('🔄 useUnifiedWalletSync: Force syncing wallet data');

      // Trigger backend sync
      await walletService.syncWalletData();
      
      // Fetch fresh data
      await fetchWalletData(true);

      console.log('✅ useUnifiedWalletSync: Wallet force sync completed');

    } catch (error: any) {
      console.error('❌ useUnifiedWalletSync: Error force syncing wallet:', error);
      updateState({
        loading: false,
        syncStatus: 'error',
        error: error.message || 'Failed to sync wallet data'
      });
    }
  }, [fetchWalletData, updateState]);

  /**
   * Get withdrawable balance for specific cryptocurrency
   */
  const getWithdrawableBalance = useCallback(async (
    cryptocurrency: string, 
    type: 'balance' | 'interest' | 'commission' = 'balance'
  ) => {
    try {
      console.log(`🔍 useUnifiedWalletSync: Getting withdrawable balance for ${cryptocurrency} (${type})`);
      
      const response = await walletService.getWithdrawableBalance(cryptocurrency, type);
      
      console.log(`✅ useUnifiedWalletSync: Withdrawable balance received:`, response);
      
      return response.withdrawableBalance || 0;
    } catch (error: any) {
      console.error('❌ useUnifiedWalletSync: Error getting withdrawable balance:', error);
      return 0;
    }
  }, []);

  /**
   * Setup real-time WebSocket listeners
   */
  useEffect(() => {
    console.log('🔌 useUnifiedWalletSync: Setting up WebSocket listeners');

    // Listen for wallet balance updates
    const handleWalletBalanceUpdate = (data: any) => {
      console.log('📡 useUnifiedWalletSync: Wallet balance update received:', data);
      
      // Refresh wallet data when balance updates
      fetchWalletData(true);
    };

    // Listen for transaction amount updates
    const handleTransactionAmountUpdate = (data: any) => {
      console.log('📡 useUnifiedWalletSync: Transaction amount update received:', data);
      
      // Refresh wallet data when admin updates transaction amounts
      fetchWalletData(true);
    };

    // Listen for deposit approvals
    const handleDepositApproved = (data: any) => {
      console.log('📡 useUnifiedWalletSync: Deposit approved:', data);
      
      // Refresh wallet data when deposits are approved
      fetchWalletData(true);
    };

    // Listen for withdrawal approvals
    const handleWithdrawalApproved = (data: any) => {
      console.log('📡 useUnifiedWalletSync: Withdrawal approved:', data);
      
      // Refresh wallet data when withdrawals are approved
      fetchWalletData(true);
    };

    // Listen for wallet sync requirements
    const handleWalletSyncRequired = (data: any) => {
      console.log('📡 useUnifiedWalletSync: Wallet sync required:', data);
      
      // Force sync when explicitly requested
      forceSyncWallet();
    };

    // Setup socket listeners
    if (socketService.socket) {
      socketService.socket.on('wallet_balance_updated', handleWalletBalanceUpdate);
      socketService.socket.on('transaction_amount_updated', handleTransactionAmountUpdate);
      socketService.socket.on('deposit_approved', handleDepositApproved);
      socketService.socket.on('withdrawal_approved', handleWithdrawalApproved);
      socketService.socket.on('wallet_sync_required', handleWalletSyncRequired);
    }

    // Cleanup listeners on unmount
    return () => {
      if (socketService.socket) {
        socketService.socket.off('wallet_balance_updated', handleWalletBalanceUpdate);
        socketService.socket.off('transaction_amount_updated', handleTransactionAmountUpdate);
        socketService.socket.off('deposit_approved', handleDepositApproved);
        socketService.socket.off('withdrawal_approved', handleWithdrawalApproved);
        socketService.socket.off('wallet_sync_required', handleWalletSyncRequired);
      }
    };
  }, [fetchWalletData, forceSyncWallet]);

  /**
   * Initial data fetch
   */
  useEffect(() => {
    fetchWalletData();
  }, [fetchWalletData]);

  /**
   * Auto-refresh every 30 seconds
   */
  useEffect(() => {
    const interval = setInterval(() => {
      if (state.syncStatus !== 'syncing') {
        fetchWalletData();
      }
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [fetchWalletData, state.syncStatus]);

  return {
    // State
    balances: state.balances,
    stats: state.stats,
    loading: state.loading,
    error: state.error,
    lastUpdated: state.lastUpdated,
    syncStatus: state.syncStatus,
    
    // Actions
    refreshWalletData: fetchWalletData,
    forceSyncWallet,
    getWithdrawableBalance,
    
    // Utilities
    clearError: () => updateState({ error: null }),
    isDataFresh: () => {
      if (!state.lastUpdated) return false;
      return (Date.now() - state.lastUpdated.getTime()) < 30000; // 30 seconds
    }
  };
};

export default useUnifiedWalletSync;
