import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { ChakraProvider } from '@chakra-ui/react'
import { theme } from './App'
import { BrowserRouter } from 'react-router-dom'
import './index.css'
import App from './App.tsx'
// Import i18n configuration
import i18n from './i18n'
import ErrorBoundary from './components/ErrorBoundary'
import { AuthProvider } from './context/AuthContext'
import { WalletProvider } from './context/WalletContext'
import { I18nextProvider } from 'react-i18next'
// Production-ready main entry point

// Performance monitoring
const reportWebVitals = (metric: any) => {
  // Send to analytics services
  console.log(metric)
}

// Get root element for better hydration performance
const rootElement = document.getElementById('root')
if (!rootElement) throw new Error('Failed to find the root element')
const root = createRoot(rootElement)

// Render application with enhanced error handling
const renderApp = () => {
  try {
    console.log('Rendering app with theme:', theme);
    root.render(
      <StrictMode>
        <ErrorBoundary>
          <I18nextProvider i18n={i18n}>
            <ChakraProvider theme={theme}>
              <BrowserRouter>
                <AuthProvider>
                  <WalletProvider>
                    <App />
                  </WalletProvider>
                </AuthProvider>
              </BrowserRouter>
            </ChakraProvider>
          </I18nextProvider>
        </ErrorBoundary>
      </StrictMode>,
    )
  } catch (error) {
    console.error('Rendering error:', error)

    // Fallback rendering without providers if there's an error
    try {
      console.log('Attempting fallback rendering');
      root.render(
        <StrictMode>
          <ChakraProvider theme={theme}>
            <BrowserRouter>
              <App />
            </BrowserRouter>
          </ChakraProvider>
        </StrictMode>,
      )
    } catch (fallbackError) {
      console.error('Fallback rendering error:', fallbackError)

      // Display error message in the DOM if all else fails
      rootElement.innerHTML = `
        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100vh; background-color: #0B0E11; color: #F0B90B; padding: 20px; text-align: center;">
          <h1 style="margin-bottom: 20px;">Shipping Finance</h1>
          <p style="margin-bottom: 20px;">We're experiencing technical difficulties. Please try refreshing the page.</p>
          <button onclick="window.location.reload()" style="background-color: #F0B90B; color: #0B0E11; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
            Refresh Page
          </button>
        </div>
      `;
    }
  }
}

// Render app immediately since i18n is already initialized in i18n.ts
console.log('i18n initialization status:', i18n.isInitialized ? 'initialized' : 'not initialized');
renderApp();

// Web vitals'ı ölçmek için
import('web-vitals').then((webVitals) => {
  // Modern web-vitals API'sini kullan
  webVitals.onCLS(reportWebVitals); // Cumulative Layout Shift
  webVitals.onFID(reportWebVitals); // First Input Delay
  webVitals.onFCP(reportWebVitals); // First Contentful Paint
  webVitals.onLCP(reportWebVitals); // Largest Contentful Paint
  webVitals.onTTFB(reportWebVitals); // Time to First Byte
})

// Service Worker kaydı
if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/service-worker.js')
      .then(registration => {
        console.log('ServiceWorker registration successful with scope: ', registration.scope);
      })
      .catch(error => {
        console.log('ServiceWorker registration failed: ', error);
      });
  });
}
