import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Grid,
  GridItem,
  Text,
  Button,
  Flex,
  Icon,
  useToast,
  Badge,
  VStack,
  HStack,
  Container,
  useClipboard,
  Tooltip,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { FaBitcoin, FaEthereum, FaCopy, FaCheckCircle, FaArrowDown, FaArrowUp } from 'react-icons/fa';
import { SiTether, SiBinance, SiDogecoin } from 'react-icons/si';
import { FaCoins } from 'react-icons/fa';
import axios from 'axios';
import { API_URL } from '../config';

// Define crypto address interface
interface CryptoAddress {
  currency: string;
  addresses: string[];
  currentIndex: number;
  enabled: boolean;
  network?: string;
}

// Define system config interface
interface SystemConfig {
  cryptoAddresses: CryptoAddress[];
  supportedCurrencies: string[];
}

// Crypto currency icons mapping
const getCryptoIcon = (currency: string) => {
  switch (currency.toUpperCase()) {
    case 'BTC':
      return FaBitcoin;
    case 'ETH':
      return FaEthereum;
    case 'USDT':
      return SiTether;
    case 'BNB':
      return SiBinance;
    case 'DOGE':
      return SiDogecoin;
    case 'TRX':
      return FaCoins;
    default:
      return FaBitcoin;
  }
};

// Crypto currency colors mapping
const getCryptoColor = (currency: string) => {
  switch (currency.toUpperCase()) {
    case 'BTC':
      return '#F7931A';
    case 'ETH':
      return '#627EEA';
    case 'USDT':
      return '#26A17B';
    case 'BNB':
      return '#F3BA2F';
    case 'DOGE':
      return '#C2A633';
    case 'TRX':
      return '#FF060A';
    default:
      return '#F0B90B';
  }
};

// Crypto currency full names mapping
const getCryptoName = (currency: string) => {
  switch (currency.toUpperCase()) {
    case 'BTC':
      return 'Bitcoin';
    case 'ETH':
      return 'Ethereum';
    case 'USDT':
      return 'Tether';
    case 'BNB':
      return 'Binance Coin';
    case 'DOGE':
      return 'Dogecoin';
    case 'TRX':
      return 'TRON';
    default:
      return currency;
  }
};

const CryptoAddresses = () => {
  const { t } = useTranslation();
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [systemConfig, setSystemConfig] = useState<SystemConfig>({
    cryptoAddresses: [],
    supportedCurrencies: ['BTC', 'ETH', 'USDT', 'BNB', 'DOGE', 'TRX'],
  });

  // Fetch system configuration
  useEffect(() => {
    const fetchSystemConfig = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await axios.get(`${API_URL}/system/crypto-addresses`);

        if (response.data.success && response.data.data) {
          const config = response.data.data;
          setSystemConfig({
            cryptoAddresses: config.cryptoAddresses || [],
            supportedCurrencies: config.supportedCurrencies || ['BTC', 'ETH', 'USDT', 'BNB', 'DOGE', 'TRX'],
          });
        }
      } catch (err: any) {
        console.error('Error fetching crypto addresses:', err);
        setError(t('cryptoAddresses.errorFetching', 'Failed to load cryptocurrency addresses'));

        // Fallback to mock data for demonstration
        setSystemConfig({
          cryptoAddresses: [
            {
              currency: 'BTC',
              addresses: ['**********************************'],
              currentIndex: 0,
              enabled: true,
            },
            {
              currency: 'ETH',
              addresses: ['******************************************'],
              currentIndex: 0,
              enabled: true,
            },
            {
              currency: 'USDT',
              addresses: ['TKWLzPKNdgzVwYbSYnFVBcL1uEE9CfTQbX'],
              currentIndex: 0,
              enabled: true,
            },
            {
              currency: 'TRX',
              addresses: ['TLPuNinqS5qHuVMHWadqA7RZ2LcxdjCWzb'],
              currentIndex: 0,
              enabled: true,
            },
            {
              currency: 'DOGE',
              addresses: ['DH5yaieqoZN36fDVciNyRueRGvGLR3mr7L'],
              currentIndex: 0,
              enabled: true,
            },
          ],
          supportedCurrencies: ['BTC', 'ETH', 'USDT', 'BNB', 'DOGE', 'TRX'],
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchSystemConfig();
  }, [t]);

  // Address copy component with modern card design
  const AddressCopyCard = ({ currency, address }: { currency: string; address: string }) => {
    const { hasCopied, onCopy } = useClipboard(address);
    const cryptoColor = getCryptoColor(currency);
    const CryptoIcon = getCryptoIcon(currency);

    const handleCopy = () => {
      onCopy();
      toast({
        title: t('cryptoAddresses.copied', 'Address Copied'),
        description: t('cryptoAddresses.copiedDesc', `${currency} address copied to clipboard`),
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    };

    return (
      <Box
        bg="#1E2329"
        borderRadius="xl"
        p={6}
        borderWidth="1px"
        borderColor="#2B3139"
        _hover={{
          transform: 'translateY(-4px)',
          shadow: '0 8px 25px rgba(0, 0, 0, 0.3)',
          borderColor: cryptoColor
        }}
        transition="all 0.3s ease"
        position="relative"
        overflow="hidden"
      >
        {/* Header with crypto info */}
        <HStack spacing={4} mb={4}>
          <Box
            p={3}
            borderRadius="full"
            bg={`${cryptoColor}20`}
            border="2px solid"
            borderColor={`${cryptoColor}40`}
          >
            <Icon as={CryptoIcon} boxSize={8} color={cryptoColor} />
          </Box>
          <VStack align="start" spacing={0} flex={1}>
            <Heading size="lg" color="#EAECEF" fontWeight="bold">
              {currency}
            </Heading>
            <Text fontSize="sm" color="#848E9C">
              {getCryptoName(currency)}
            </Text>
          </VStack>
          <Badge
            colorScheme="green"
            variant="solid"
            px={3}
            py={1}
            borderRadius="full"
            fontSize="xs"
            fontWeight="bold"
          >
            {t('cryptoAddresses.active', 'Active')}
          </Badge>
        </HStack>

        {/* Address section */}
        <VStack spacing={4} align="stretch">
          <Box>
            <Text fontSize="sm" color="#848E9C" mb={3} fontWeight="medium">
              {t('cryptoAddresses.depositAddress', 'Address')}:
            </Text>
            <Box
              p={4}
              bg="#0B0E11"
              borderRadius="lg"
              border="1px solid"
              borderColor="#2B3139"
              position="relative"
              _hover={{ borderColor: "#848E9C" }}
              transition="border-color 0.2s"
            >
              <HStack spacing={3} align="center">
                <Text
                  fontSize="sm"
                  color="#EAECEF"
                  wordBreak="break-all"
                  fontFamily="mono"
                  flex={1}
                  lineHeight="1.4"
                >
                  {address}
                </Text>
                <Tooltip label={hasCopied ? t('common.copied', 'Copied!') : t('common.copy', 'Copy')}>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={handleCopy}
                    color={hasCopied ? "#02C076" : "#848E9C"}
                    _hover={{
                      bg: hasCopied ? "#02C07620" : "#848E9C20",
                      color: hasCopied ? "#02C076" : "#EAECEF"
                    }}
                    minW="auto"
                    p={2}
                  >
                    <Icon as={hasCopied ? FaCheckCircle : FaCopy} boxSize={4} />
                  </Button>
                </Tooltip>
              </HStack>
            </Box>
          </Box>

          {/* Action buttons */}
          <HStack spacing={3}>
            <Button
              flex={1}
              bg={cryptoColor}
              color="#0B0E11"
              _hover={{
                bg: `${cryptoColor}E6`,
                transform: "translateY(-1px)"
              }}
              _active={{ transform: "translateY(0px)" }}
              fontWeight="bold"
              borderRadius="lg"
              leftIcon={<Icon as={FaArrowDown} />}
              transition="all 0.2s"
            >
              {t('cryptoAddresses.deposit', 'Deposit')}
            </Button>
            <Button
              flex={1}
              variant="outline"
              borderColor="#2B3139"
              color="#EAECEF"
              _hover={{
                borderColor: cryptoColor,
                color: cryptoColor,
                transform: "translateY(-1px)"
              }}
              _active={{ transform: "translateY(0px)" }}
              fontWeight="bold"
              borderRadius="lg"
              leftIcon={<Icon as={FaArrowUp} />}
              transition="all 0.2s"
            >
              {t('cryptoAddresses.withdraw', 'Withdraw')}
            </Button>
          </HStack>
        </VStack>
      </Box>
    );
  };

  if (isLoading) {
    return (
      <Box bg="#0B0E11" minH="100vh" py={8}>
        <Container maxW="container.xl">
          <Box display="flex" justifyContent="center" alignItems="center" minH="400px">
            <VStack spacing={4}>
              <Spinner size="xl" color="#F0B90B" />
              <Text color="#EAECEF">{t('cryptoAddresses.loading', 'Loading cryptocurrency addresses...')}</Text>
            </VStack>
          </Box>
        </Container>
      </Box>
    );
  }

  // Get enabled addresses only
  const enabledAddresses = systemConfig.cryptoAddresses.filter(addr => addr.enabled && addr.addresses.length > 0);

  return (
    <Box bg="#0B0E11" minH="100vh" py={8}>
      <Container maxW="container.xl">
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <Box textAlign="center">
            <Heading size="xl" color="#F0B90B" mb={4}>
              {t('cryptoAddresses.title', 'Cryptocurrency Addresses')}
            </Heading>
            <Text fontSize="lg" color="#848E9C" maxW="2xl" mx="auto">
              {t('cryptoAddresses.description', 'Use these addresses to deposit cryptocurrencies to our platform. Make sure to send only the specified cryptocurrency to its corresponding address.')}
            </Text>
          </Box>

          {/* Error Alert */}
          {error && (
            <Alert status="warning" borderRadius="md" bg="#2B3139" borderColor="#F0B90B">
              <AlertIcon color="#F0B90B" />
              <Box>
                <AlertTitle color="#EAECEF">{t('common.warning', 'Warning')}</AlertTitle>
                <AlertDescription color="#848E9C">{error}</AlertDescription>
              </Box>
            </Alert>
          )}

          {/* Addresses Grid */}
          {enabledAddresses.length > 0 ? (
            <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(3, 1fr)" }} gap={6}>
              {enabledAddresses.map((cryptoAddr) => {
                const currentAddress = cryptoAddr.addresses[cryptoAddr.currentIndex] || cryptoAddr.addresses[0];
                return (
                  <GridItem key={cryptoAddr.currency}>
                    <AddressCopyCard
                      currency={cryptoAddr.currency}
                      address={currentAddress}
                    />
                  </GridItem>
                );
              })}
            </Grid>
          ) : (
            <Alert status="info" borderRadius="md" bg="#2B3139" borderColor="#627EEA">
              <AlertIcon color="#627EEA" />
              <Box>
                <AlertTitle color="#EAECEF">{t('cryptoAddresses.noAddresses', 'No Addresses Available')}</AlertTitle>
                <AlertDescription color="#848E9C">
                  {t('cryptoAddresses.noAddressesDesc', 'No cryptocurrency addresses are currently configured. Please contact support for assistance.')}
                </AlertDescription>
              </Box>
            </Alert>
          )}
        </VStack>
      </Container>
    </Box>
  );
};

export default CryptoAddresses;
