import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Button,
  Container,
  Heading,
  Text,
  SimpleGrid,
  FormControl,
  FormLabel,
  Input,
  Stack,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Switch,
  Badge,
  Flex,
  Avatar,
  Divider,
  HStack,
  VStack,
  Icon,
  Progress,
  InputGroup,
  InputRightElement,
  InputRightAddon,
  Select,
  Grid,
  GridItem,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Textarea,
  useDisclosure,
  useToast,
  Spinner
} from '@chakra-ui/react';

import { ViewIcon, ViewOffIcon } from '@chakra-ui/icons';
import {
  FaKey,
  FaShieldAlt,
  FaUserCheck,
  FaWallet,
  FaEthereum,
  FaDollarSign,
  FaMoneyBillWave,
  FaCopy,
  FaUserPlus,
  FaChartLine,
  FaCoins,
  FaUserFriends,
  FaArrowUp,
  FaArrowRight,
  FaGoogle,
  FaStar,
  FaHeadset,
  FaLock,
  FaUserCog,
  FaBell,
  FaUserShield,
  FaDownload,
  FaSave,
  FaTrashAlt,
  FaSearch,
  FaFileExport,
  FaEye,
  FaArrowDown,
  FaArrowLeft,
  FaSpinner,
  FaExternalLinkAlt,
  FaPrint,
  FaSort,
  FaExchangeAlt,
  FaSync,
  FaExpand,
  FaCompress,
  FaPlus
} from 'react-icons/fa';
import { SiDogecoin } from 'react-icons/si';
import useAuth from '../hooks/useAuth';
import useWallet from '../hooks/useWallet';
import { useTranslation } from 'react-i18next';
import EmailVerificationBanner from '../components/EmailVerificationBanner';

// Comprehensive countries list with flags for better UX
const COUNTRIES = [
  { code: 'AF', name: 'Afghanistan', flag: '🇦🇫' },
  { code: 'AL', name: 'Albania', flag: '🇦🇱' },
  { code: 'DZ', name: 'Algeria', flag: '🇩🇿' },
  { code: 'AD', name: 'Andorra', flag: '🇦🇩' },
  { code: 'AO', name: 'Angola', flag: '🇦🇴' },
  { code: 'AR', name: 'Argentina', flag: '🇦🇷' },
  { code: 'AM', name: 'Armenia', flag: '🇦🇲' },
  { code: 'AU', name: 'Australia', flag: '🇦🇺' },
  { code: 'AT', name: 'Austria', flag: '🇦🇹' },
  { code: 'AZ', name: 'Azerbaijan', flag: '🇦🇿' },
  { code: 'BH', name: 'Bahrain', flag: '🇧🇭' },
  { code: 'BD', name: 'Bangladesh', flag: '🇧🇩' },
  { code: 'BY', name: 'Belarus', flag: '🇧🇾' },
  { code: 'BE', name: 'Belgium', flag: '🇧🇪' },
  { code: 'BZ', name: 'Belize', flag: '🇧🇿' },
  { code: 'BJ', name: 'Benin', flag: '🇧🇯' },
  { code: 'BT', name: 'Bhutan', flag: '🇧🇹' },
  { code: 'BO', name: 'Bolivia', flag: '🇧🇴' },
  { code: 'BA', name: 'Bosnia and Herzegovina', flag: '🇧🇦' },
  { code: 'BW', name: 'Botswana', flag: '🇧🇼' },
  { code: 'BR', name: 'Brazil', flag: '🇧🇷' },
  { code: 'BN', name: 'Brunei', flag: '🇧🇳' },
  { code: 'BG', name: 'Bulgaria', flag: '🇧🇬' },
  { code: 'BF', name: 'Burkina Faso', flag: '🇧🇫' },
  { code: 'BI', name: 'Burundi', flag: '🇧🇮' },
  { code: 'KH', name: 'Cambodia', flag: '🇰🇭' },
  { code: 'CM', name: 'Cameroon', flag: '🇨🇲' },
  { code: 'CA', name: 'Canada', flag: '🇨🇦' },
  { code: 'CV', name: 'Cape Verde', flag: '🇨🇻' },
  { code: 'CF', name: 'Central African Republic', flag: '🇨🇫' },
  { code: 'TD', name: 'Chad', flag: '🇹🇩' },
  { code: 'CL', name: 'Chile', flag: '🇨🇱' },
  { code: 'CN', name: 'China', flag: '🇨🇳' },
  { code: 'CO', name: 'Colombia', flag: '🇨🇴' },
  { code: 'KM', name: 'Comoros', flag: '🇰🇲' },
  { code: 'CG', name: 'Congo', flag: '🇨🇬' },
  { code: 'CR', name: 'Costa Rica', flag: '🇨🇷' },
  { code: 'CI', name: 'Côte d\'Ivoire', flag: '🇨🇮' },
  { code: 'HR', name: 'Croatia', flag: '🇭🇷' },
  { code: 'CU', name: 'Cuba', flag: '🇨🇺' },
  { code: 'CY', name: 'Cyprus', flag: '🇨🇾' },
  { code: 'CZ', name: 'Czech Republic', flag: '🇨🇿' },
  { code: 'DK', name: 'Denmark', flag: '🇩🇰' },
  { code: 'DJ', name: 'Djibouti', flag: '🇩🇯' },
  { code: 'DM', name: 'Dominica', flag: '🇩🇲' },
  { code: 'DO', name: 'Dominican Republic', flag: '🇩🇴' },
  { code: 'EC', name: 'Ecuador', flag: '🇪🇨' },
  { code: 'EG', name: 'Egypt', flag: '🇪🇬' },
  { code: 'SV', name: 'El Salvador', flag: '🇸🇻' },
  { code: 'GQ', name: 'Equatorial Guinea', flag: '🇬🇶' },
  { code: 'ER', name: 'Eritrea', flag: '🇪🇷' },
  { code: 'EE', name: 'Estonia', flag: '🇪🇪' },
  { code: 'SZ', name: 'Eswatini', flag: '🇸🇿' },
  { code: 'ET', name: 'Ethiopia', flag: '🇪🇹' },
  { code: 'FJ', name: 'Fiji', flag: '🇫🇯' },
  { code: 'FI', name: 'Finland', flag: '🇫🇮' },
  { code: 'FR', name: 'France', flag: '🇫🇷' },
  { code: 'GA', name: 'Gabon', flag: '🇬🇦' },
  { code: 'GM', name: 'Gambia', flag: '🇬🇲' },
  { code: 'GE', name: 'Georgia', flag: '🇬🇪' },
  { code: 'DE', name: 'Germany', flag: '🇩🇪' },
  { code: 'GH', name: 'Ghana', flag: '🇬🇭' },
  { code: 'GR', name: 'Greece', flag: '🇬🇷' },
  { code: 'GD', name: 'Grenada', flag: '🇬🇩' },
  { code: 'GT', name: 'Guatemala', flag: '🇬🇹' },
  { code: 'GN', name: 'Guinea', flag: '🇬🇳' },
  { code: 'GW', name: 'Guinea-Bissau', flag: '🇬🇼' },
  { code: 'GY', name: 'Guyana', flag: '🇬🇾' },
  { code: 'HT', name: 'Haiti', flag: '🇭🇹' },
  { code: 'HN', name: 'Honduras', flag: '🇭🇳' },
  { code: 'HU', name: 'Hungary', flag: '🇭🇺' },
  { code: 'IS', name: 'Iceland', flag: '🇮🇸' },
  { code: 'IN', name: 'India', flag: '🇮🇳' },
  { code: 'ID', name: 'Indonesia', flag: '🇮🇩' },
  { code: 'IR', name: 'Iran', flag: '🇮🇷' },
  { code: 'IQ', name: 'Iraq', flag: '🇮🇶' },
  { code: 'IE', name: 'Ireland', flag: '🇮🇪' },
  { code: 'IL', name: 'Israel', flag: '🇮🇱' },
  { code: 'IT', name: 'Italy', flag: '🇮🇹' },
  { code: 'JM', name: 'Jamaica', flag: '🇯🇲' },
  { code: 'JP', name: 'Japan', flag: '🇯🇵' },
  { code: 'JO', name: 'Jordan', flag: '🇯🇴' },
  { code: 'KZ', name: 'Kazakhstan', flag: '🇰🇿' },
  { code: 'KE', name: 'Kenya', flag: '🇰🇪' },
  { code: 'KI', name: 'Kiribati', flag: '🇰🇮' },
  { code: 'KP', name: 'North Korea', flag: '🇰🇵' },
  { code: 'KR', name: 'South Korea', flag: '🇰🇷' },
  { code: 'KW', name: 'Kuwait', flag: '🇰🇼' },
  { code: 'KG', name: 'Kyrgyzstan', flag: '🇰🇬' },
  { code: 'LA', name: 'Laos', flag: '🇱🇦' },
  { code: 'LV', name: 'Latvia', flag: '🇱🇻' },
  { code: 'LB', name: 'Lebanon', flag: '🇱🇧' },
  { code: 'LS', name: 'Lesotho', flag: '🇱🇸' },
  { code: 'LR', name: 'Liberia', flag: '🇱🇷' },
  { code: 'LY', name: 'Libya', flag: '🇱🇾' },
  { code: 'LI', name: 'Liechtenstein', flag: '🇱🇮' },
  { code: 'LT', name: 'Lithuania', flag: '🇱🇹' },
  { code: 'LU', name: 'Luxembourg', flag: '🇱🇺' },
  { code: 'MG', name: 'Madagascar', flag: '🇲🇬' },
  { code: 'MW', name: 'Malawi', flag: '🇲🇼' },
  { code: 'MY', name: 'Malaysia', flag: '🇲🇾' },
  { code: 'MV', name: 'Maldives', flag: '🇲🇻' },
  { code: 'ML', name: 'Mali', flag: '🇲🇱' },
  { code: 'MT', name: 'Malta', flag: '🇲🇹' },
  { code: 'MH', name: 'Marshall Islands', flag: '🇲🇭' },
  { code: 'MR', name: 'Mauritania', flag: '🇲🇷' },
  { code: 'MU', name: 'Mauritius', flag: '🇲🇺' },
  { code: 'MX', name: 'Mexico', flag: '🇲🇽' },
  { code: 'FM', name: 'Micronesia', flag: '🇫🇲' },
  { code: 'MD', name: 'Moldova', flag: '🇲🇩' },
  { code: 'MC', name: 'Monaco', flag: '🇲🇨' },
  { code: 'MN', name: 'Mongolia', flag: '🇲🇳' },
  { code: 'ME', name: 'Montenegro', flag: '🇲🇪' },
  { code: 'MA', name: 'Morocco', flag: '🇲🇦' },
  { code: 'MZ', name: 'Mozambique', flag: '🇲🇿' },
  { code: 'MM', name: 'Myanmar', flag: '🇲🇲' },
  { code: 'NA', name: 'Namibia', flag: '🇳🇦' },
  { code: 'NR', name: 'Nauru', flag: '🇳🇷' },
  { code: 'NP', name: 'Nepal', flag: '🇳🇵' },
  { code: 'NL', name: 'Netherlands', flag: '🇳🇱' },
  { code: 'NZ', name: 'New Zealand', flag: '🇳🇿' },
  { code: 'NI', name: 'Nicaragua', flag: '🇳🇮' },
  { code: 'NE', name: 'Niger', flag: '🇳🇪' },
  { code: 'NG', name: 'Nigeria', flag: '🇳🇬' },
  { code: 'MK', name: 'North Macedonia', flag: '🇲🇰' },
  { code: 'NO', name: 'Norway', flag: '🇳🇴' },
  { code: 'OM', name: 'Oman', flag: '🇴🇲' },
  { code: 'PK', name: 'Pakistan', flag: '🇵🇰' },
  { code: 'PW', name: 'Palau', flag: '🇵🇼' },
  { code: 'PS', name: 'Palestine', flag: '🇵🇸' },
  { code: 'PA', name: 'Panama', flag: '🇵🇦' },
  { code: 'PG', name: 'Papua New Guinea', flag: '🇵🇬' },
  { code: 'PY', name: 'Paraguay', flag: '🇵🇾' },
  { code: 'PE', name: 'Peru', flag: '🇵🇪' },
  { code: 'PH', name: 'Philippines', flag: '🇵🇭' },
  { code: 'PL', name: 'Poland', flag: '🇵🇱' },
  { code: 'PT', name: 'Portugal', flag: '🇵🇹' },
  { code: 'QA', name: 'Qatar', flag: '🇶🇦' },
  { code: 'RO', name: 'Romania', flag: '🇷🇴' },
  { code: 'RU', name: 'Russia', flag: '🇷🇺' },
  { code: 'RW', name: 'Rwanda', flag: '🇷🇼' },
  { code: 'WS', name: 'Samoa', flag: '🇼🇸' },
  { code: 'SM', name: 'San Marino', flag: '🇸🇲' },
  { code: 'ST', name: 'São Tomé and Príncipe', flag: '🇸🇹' },
  { code: 'SA', name: 'Saudi Arabia', flag: '🇸🇦' },
  { code: 'SN', name: 'Senegal', flag: '🇸🇳' },
  { code: 'RS', name: 'Serbia', flag: '🇷🇸' },
  { code: 'SC', name: 'Seychelles', flag: '🇸🇨' },
  { code: 'SL', name: 'Sierra Leone', flag: '🇸🇱' },
  { code: 'SG', name: 'Singapore', flag: '🇸🇬' },
  { code: 'SK', name: 'Slovakia', flag: '🇸🇰' },
  { code: 'SI', name: 'Slovenia', flag: '🇸🇮' },
  { code: 'SB', name: 'Solomon Islands', flag: '🇸🇧' },
  { code: 'SO', name: 'Somalia', flag: '🇸🇴' },
  { code: 'ZA', name: 'South Africa', flag: '🇿🇦' },
  { code: 'SS', name: 'South Sudan', flag: '🇸🇸' },
  { code: 'ES', name: 'Spain', flag: '🇪🇸' },
  { code: 'LK', name: 'Sri Lanka', flag: '🇱🇰' },
  { code: 'SD', name: 'Sudan', flag: '🇸🇩' },
  { code: 'SR', name: 'Suriname', flag: '🇸🇷' },
  { code: 'SE', name: 'Sweden', flag: '🇸🇪' },
  { code: 'CH', name: 'Switzerland', flag: '🇨🇭' },
  { code: 'SY', name: 'Syria', flag: '🇸🇾' },
  { code: 'TW', name: 'Taiwan', flag: '🇹🇼' },
  { code: 'TJ', name: 'Tajikistan', flag: '🇹🇯' },
  { code: 'TZ', name: 'Tanzania', flag: '🇹🇿' },
  { code: 'TH', name: 'Thailand', flag: '🇹🇭' },
  { code: 'TL', name: 'Timor-Leste', flag: '🇹🇱' },
  { code: 'TG', name: 'Togo', flag: '🇹🇬' },
  { code: 'TO', name: 'Tonga', flag: '🇹🇴' },
  { code: 'TT', name: 'Trinidad and Tobago', flag: '🇹🇹' },
  { code: 'TN', name: 'Tunisia', flag: '🇹🇳' },
  { code: 'TR', name: 'Turkey', flag: '🇹🇷' },
  { code: 'TM', name: 'Turkmenistan', flag: '🇹🇲' },
  { code: 'TV', name: 'Tuvalu', flag: '🇹🇻' },
  { code: 'UG', name: 'Uganda', flag: '🇺🇬' },
  { code: 'UA', name: 'Ukraine', flag: '🇺🇦' },
  { code: 'AE', name: 'United Arab Emirates', flag: '🇦🇪' },
  { code: 'GB', name: 'United Kingdom', flag: '🇬🇧' },
  { code: 'US', name: 'United States', flag: '🇺🇸' },
  { code: 'UY', name: 'Uruguay', flag: '🇺🇾' },
  { code: 'UZ', name: 'Uzbekistan', flag: '🇺🇿' },
  { code: 'VU', name: 'Vanuatu', flag: '🇻🇺' },
  { code: 'VA', name: 'Vatican City', flag: '🇻🇦' },
  { code: 'VE', name: 'Venezuela', flag: '🇻🇪' },
  { code: 'VN', name: 'Vietnam', flag: '🇻🇳' },
  { code: 'YE', name: 'Yemen', flag: '🇾🇪' },
  { code: 'ZM', name: 'Zambia', flag: '🇿🇲' },
  { code: 'ZW', name: 'Zimbabwe', flag: '🇿🇼' }
];
import KYCVerification from '../components/KYCVerification';
// import AnalyticsDashboard from '../components/AnalyticsDashboard';
import UserInvestments from '../components/UserInvestments';
import InvestmentTab from '../components/profile/InvestmentTab';
import WalletCard from '../components/WalletCard';

// Import API services
import { transactionService, referralService, investmentPackageService } from '../services/api';
import { ReferralHistoryItem, ReferralStats } from '../services/referralService';
import axios from 'axios';

// Enhanced Transaction interfaces
interface EnhancedTransaction {
  id: string;
  type: 'Deposit' | 'Withdrawal' | 'Investment' | 'Earnings' | 'Referral Commission';
  cryptocurrency: string;
  amount: number;
  usdValue?: number;
  status: 'Confirmed' | 'Processing' | 'Pending' | 'Failed';
  date: string;
  fee: number;
  confirmations: number;
  requiredConfirmations: number;
  txHash: string;
  fromAddress: string;
  toAddress: string;
  description: string;
  direction: 'incoming' | 'outgoing' | 'internal';
  network?: string;
  gasPrice?: number;
  blockNumber?: number;
}

interface CryptocurrencyInfo {
  symbol: string;
  name: string;
  icon: string;
  decimals: number;
  color: string;
}

interface TransactionFilters {
  cryptocurrency: string;
  type: string;
  status: string;
  dateRange: {
    start: string;
    end: string;
  };
  searchQuery: string;
}

// Enhanced Investment interfaces for CryptoYield system
interface InvestmentPackage {
  id: string;
  packageId: string;
  userId: string;
  cryptocurrency: string;
  principalAmount: number;
  currentBalance: number;
  accumulatedInterest: number;
  dailyInterestRate: number;
  annualPercentageYield: number;
  startDate: string;
  maturityDate: string;
  lastInterestDistribution: string;
  nextInterestDistribution: string;
  status: 'active' | 'completed' | 'paused' | 'withdrawn';
  totalEarned: number;
  daysActive: number;
  totalDays: number;
  roi: number;
  interestTier: 'bronze' | 'silver' | 'gold' | 'platinum';
  withdrawalEligible: boolean;
  earlyWithdrawalPenalty: number;
  metadata: {
    createdFromDeposit: boolean;
    depositTransactionId?: string;
    autoReinvest: boolean;
    compoundingEnabled: boolean;
  };
}

interface InterestDistribution {
  id: string;
  packageId: string;
  amount: number;
  cryptocurrency: string;
  usdValue: number;
  distributionDate: string;
  transactionId: string;
  status: 'pending' | 'completed' | 'failed';
  type: 'daily' | 'bonus' | 'completion';
}

interface WithdrawalEligibility {
  eligible: boolean;
  currentBalance: number;
  minimumRequired: number;
  availableForWithdrawal: number;
  usdEquivalent: number;
  withdrawalFee: number;
  cooldownRemaining: number;
  status: 'eligible' | 'insufficient_balance' | 'cooldown' | 'maintenance';
  nextEligibleTime?: string;
  reasons: string[];
}

interface InterestRateTier {
  tier: 'bronze' | 'silver' | 'gold' | 'platinum';
  minAmount: number;
  maxAmount: number | null;
  dailyRate: number;
  annualRate: number;
  color: string;
  benefits: string[];
}



// Import custom modals
import DepositModal from '../components/modals/DepositModal';
import WithdrawModal from '../components/modals/WithdrawModal';
import LoginPrompt from '../components/LoginPrompt';



const Profile = () => {
  const { t } = useTranslation();
  const { user, updateProfile, loading } = useAuth();
  const { wallet, loading: walletLoading, fetchWallet } = useWallet();
  const toast = useToast();

  // Check authentication status early but don't return yet
  const isAuthenticated = user && user._id && user.email;



  // Modal states
  const {
    isOpen: isDepositOpen,
    onOpen: onDepositOpen,
    onClose: onDepositClose
  } = useDisclosure();

  const {
    isOpen,
    onClose
  } = useDisclosure();



  const {
    isOpen: isWithdrawOpen,
    onOpen: onWithdrawOpen,
    onClose: onWithdrawClose
  } = useDisclosure();

  // Yatırım modalı için state'ler
  const [selectedCrypto, setSelectedCrypto] = useState<string | null>('USDT');
  const [initialWithdrawalType, setInitialWithdrawalType] = useState<'principal' | 'interest' | 'commission'>('interest');
  const [commissionData, setCommissionData] = useState({
    amount: 0,
    commission: 0,
    btcAmount: 0,
    btcCommission: 0
  });

  // Yatırım butonuna tıklandığında
  const handleInvestClick = () => {
    // Kullanıcı giriş yapmış mı kontrol et
    if (user) {
      // Kullanıcı giriş yapmışsa modalı aç
      onDepositOpen();
    } else {
      // Kullanıcı giriş yapmamışsa uyarı göster
      toast({
        title: t('home.deposit.loginRequired', 'Giriş Yapmanız Gerekiyor'),
        description: t('home.deposit.loginRequiredDesc', 'Yatırım yapmak için lütfen giriş yapın veya hesap oluşturun.'),
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // Colors
  const bgColor = "#0B0E11";
  const cardBgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";

  // Wallet verilerini yükle
  useEffect(() => {
    if (user) {
      fetchWallet();
    }
  }, [user, fetchWallet]);



  const [formData, setFormData] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    phone: '+41 78 123 4567',
    country: 'Switzerland',
    city: 'Zurich',
    address: 'Bahnhofstrasse 42',
    postalCode: '8001',
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [activeTab, setActiveTab] = useState(0); // Default to Profile tab


  // Referral system state
  const [referralHistory, setReferralHistory] = useState<ReferralHistoryItem[]>([]);
  const [referralStats, setReferralStats] = useState<ReferralStats | null>(null);
  const [referralLoading, setReferralLoading] = useState(false);
  const [referralError, setReferralError] = useState<string | null>(null);
  const [referralPagination, setReferralPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });



  // Fetch real referral data
  const fetchReferralData = useCallback(async () => {
    if (!user) {
      console.log('🚫 REFERRAL: No user found, skipping referral data fetch');
      setReferralLoading(false);
      setReferralError('Please log in to view referral data');
      return;
    }

    // Check if user has proper authentication
    if (!user._id || !user.email) {
      console.log('🚫 REFERRAL: Invalid user data, skipping referral data fetch');
      setReferralLoading(false);
      setReferralError('Invalid user session. Please log in again.');
      return;
    }

    setReferralLoading(true);
    setReferralError(null);

    try {
      console.log('🚀 REFERRAL: Starting referral data fetch for user:', user.email);

      // Fetch referral stats and history in parallel with better error handling
      const [statsResponse, historyResponse] = await Promise.all([
        referralService.getReferralStats().catch(error => {
          console.warn('⚠️ REFERRAL: Stats fetch failed:', error);
          // Check if it's an authentication error
          if (error?.response?.status === 401 || error?.response?.status === 403) {
            throw error; // Re-throw auth errors to handle them properly
          }
          return { data: { totalReferrals: 0, totalCommissions: 0, activeReferrals: 0 } };
        }),
        referralService.getReferralHistory({
          page: referralPagination.page,
          limit: referralPagination.limit
        }).catch(error => {
          console.warn('⚠️ REFERRAL: History fetch failed:', error);
          // Check if it's an authentication error
          if (error?.response?.status === 401 || error?.response?.status === 403) {
            throw error; // Re-throw auth errors to handle them properly
          }
          return { data: { referrals: [], pagination: { page: 1, limit: 10, total: 0, pages: 0 } } };
        })
      ]);

      console.log('📥 REFERRAL: Raw API responses:', {
        statsResponse,
        historyResponse
      });

      // Handle different response structures with comprehensive fallbacks
      const statsData = statsResponse?.data?.data || statsResponse?.data || statsResponse || {
        totalReferrals: 0,
        totalCommissions: 0,
        activeReferrals: 0,
        totalEarnings: 0,
        pendingCommissions: 0,
        thisMonthReferrals: 0,
        conversionRate: 0
      };

      const historyData = historyResponse?.data?.data || historyResponse?.data || historyResponse || {
        referrals: [],
        pagination: { page: 1, limit: 10, total: 0, pages: 0 }
      };

      setReferralStats(statsData);
      setReferralHistory(historyData.referrals || []);
      setReferralPagination(prev => ({
        ...prev,
        ...(historyData.pagination || { page: 1, limit: 10, total: 0, pages: 0 })
      }));

      console.log('✅ REFERRAL: Data loaded successfully:', {
        stats: statsData,
        historyCount: historyData.referrals?.length || 0
      });
    } catch (error: any) {
      console.error('❌ REFERRAL: Error fetching referral data:', error);

      // Handle authentication errors specifically
      if (error?.response?.status === 401) {
        console.log('🔐 REFERRAL: Authentication error - user needs to log in');
        setReferralError('Authentication required. Please log in to view referral data.');

        toast({
          title: t('common.error', 'Authentication Error'),
          description: 'Please log in to access your referral data',
          status: 'warning',
          duration: 5000,
          isClosable: true,
        });
        return;
      }

      if (error?.response?.status === 403) {
        console.log('🚫 REFERRAL: Access forbidden - insufficient permissions');
        setReferralError('Access denied. You do not have permission to view referral data.');

        toast({
          title: t('common.error', 'Access Denied'),
          description: 'You do not have permission to access referral data',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
        return;
      }

      // Handle other errors
      const errorMessage = error?.response?.data?.message ||
                          error?.message ||
                          'Failed to load referral data';

      setReferralError(`Network Error: ${errorMessage}`);

      // Show error toast with more details
      toast({
        title: t('common.error', 'Error'),
        description: `${t('profile.referral.fetchError', 'Failed to load referral data')}: ${errorMessage}`,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setReferralLoading(false);
    }
  }, [user, referralPagination.page, referralPagination.limit, t, toast]);

  // Generate referral code if user doesn't have one
  const generateReferralCode = useCallback(async () => {
    if (!user?.token) return;

    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:5001'}/api/referrals/generate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user.token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.status === 'success') {
          toast({
            title: t('common:referral.codeGenerated', 'Referral Code Generated'),
            description: t('common:referral.codeGeneratedDesc', 'Your referral code has been created successfully'),
            status: 'success',
            duration: 3000,
            isClosable: true,
          });

          // Refresh referral data to get the new code
          fetchReferralData();
        }
      } else {
        throw new Error('Failed to generate referral code');
      }
    } catch (error) {
      console.error('Error generating referral code:', error);
      toast({
        title: t('common.error', 'Error'),
        description: t('common:referral.generateError', 'Failed to generate referral code'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  }, [user?.token, toast, t, fetchReferralData]);

  // Enhanced Transactions state management
  const [transactionSortBy, setTransactionSortBy] = useState<'date' | 'amount' | 'type' | 'status'>('date');
  const [transactionSortOrder, setTransactionSortOrder] = useState<'asc' | 'desc'>('desc');
  const [transactionPageSize, setTransactionPageSize] = useState(25);
  const [currentTransactionPage, setCurrentTransactionPage] = useState(1);
  const [showTransactionDetails, setShowTransactionDetails] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Enhanced filtering and search
  const [transactionFilters, setTransactionFilters] = useState<TransactionFilters>({
    cryptocurrency: 'all',
    type: 'all',
    status: 'all',
    dateRange: {
      start: '',
      end: ''
    },
    searchQuery: ''
  });

  // Enhanced transactions with real-time updates
  const [enhancedTransactions, setEnhancedTransactions] = useState<EnhancedTransaction[]>([]);
  const [selectedTransaction, setSelectedTransaction] = useState<EnhancedTransaction | null>(null);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  // Enhanced Investment state management
  const [investmentPackages, setInvestmentPackages] = useState<InvestmentPackage[]>([]);
  const [interestDistributions, setInterestDistributions] = useState<InterestDistribution[]>([]);
  const [withdrawalEligibility, setWithdrawalEligibility] = useState<WithdrawalEligibility | null>(null);
  const [selectedPackage, setSelectedPackage] = useState<InvestmentPackage | null>(null);
  const [nextDistributionCountdown, setNextDistributionCountdown] = useState<string>('');
  const [investmentSortBy, setInvestmentSortBy] = useState<'date' | 'amount' | 'performance' | 'roi'>('date');
  const [investmentSortOrder, setInvestmentSortOrder] = useState<'asc' | 'desc'>('desc');
  const [investmentFilters, setInvestmentFilters] = useState({
    cryptocurrency: 'all',
    status: 'all',
    tier: 'all'
  });
  const [showWithdrawalModal, setShowWithdrawalModal] = useState(false);
  const [pendingInterestAmount, setPendingInterestAmount] = useState(0);
  const [realTimeInterestAccrual, setRealTimeInterestAccrual] = useState<{ [packageId: string]: number }>({});

  // Real data state management
  const [realTransactions, setRealTransactions] = useState<any[]>([]);
  const [realInvestmentPackages, setRealInvestmentPackages] = useState<any[]>([]);
  const [realEarningsSummary, setRealEarningsSummary] = useState<any>(null);
  const [dataLoading, setDataLoading] = useState(false);
  const [dataError, setDataError] = useState<string | null>(null);





  // Transaction data will be fetched from unified transaction system

  // Transaction helper functions
  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'Deposit': return FaArrowDown;
      case 'Withdrawal': return FaArrowUp;
      case 'Investment': return FaCoins;
      case 'Earnings': return FaChartLine;
      case 'Referral Commission': return FaUserFriends;
      default: return FaMoneyBillWave;
    }
  };

  const getTransactionColor = (type: string, direction: string) => {
    if (direction === 'incoming') return '#02C076';
    if (direction === 'outgoing') return '#F84960';
    return '#F0B90B';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Confirmed': return '#02C076';
      case 'Processing': return '#F0B90B';
      case 'Pending': return '#848E9C';
      case 'Failed': return '#F84960';
      default: return '#848E9C';
    }
  };

  // Enhanced cryptocurrency information
  const cryptocurrencyInfo: { [key: string]: CryptocurrencyInfo } = {
    BTC: { symbol: 'BTC', name: 'Bitcoin', icon: '₿', decimals: 8, color: '#F7931A' },
    ETH: { symbol: 'ETH', name: 'Ethereum', icon: 'Ξ', decimals: 6, color: '#627EEA' },
    USDT: { symbol: 'USDT', name: 'Tether', icon: '₮', decimals: 2, color: '#26A17B' },
    BNB: { symbol: 'BNB', name: 'Binance Coin', icon: 'BNB', decimals: 4, color: '#F0B90B' },
    SOL: { symbol: 'SOL', name: 'Solana', icon: 'SOL', decimals: 4, color: '#9945FF' },
    DOGE: { symbol: 'DOGE', name: 'Dogecoin', icon: 'Ð', decimals: 4, color: '#C2A633' },
    TRX: { symbol: 'TRX', name: 'Tron', icon: 'TRX', decimals: 4, color: '#FF060A' },
    ADA: { symbol: 'ADA', name: 'Cardano', icon: 'ADA', decimals: 4, color: '#0033AD' },
    MATIC: { symbol: 'MATIC', name: 'Polygon', icon: 'MATIC', decimals: 4, color: '#8247E5' },
    LTC: { symbol: 'LTC', name: 'Litecoin', icon: 'Ł', decimals: 6, color: '#BFBBBB' }
  };

  // Enhanced transaction helper functions
  const formatCryptocurrencyAmount = (amount: number, cryptocurrency: string): string => {
    const info = cryptocurrencyInfo[cryptocurrency] || { decimals: 4 };
    return `${amount.toFixed(info.decimals)} ${cryptocurrency}`;
  };

  const formatUSDValue = (usdValue?: number): string => {
    if (!usdValue) return '';
    return `≈ $${usdValue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const getTransactionDirectionColor = (direction: string) => {
    switch (direction) {
      case 'incoming': return '#02C076';
      case 'outgoing': return '#F84960';
      case 'internal': return '#F0B90B';
      default: return '#848E9C';
    }
  };

  const getTransactionDirectionIcon = (direction: string) => {
    switch (direction) {
      case 'incoming': return FaArrowDown;
      case 'outgoing': return FaArrowUp;
      case 'internal': return FaExchangeAlt;
      default: return FaMoneyBillWave;
    }
  };

  const getConfirmationProgress = (confirmations: number, required: number): number => {
    return Math.min((confirmations / required) * 100, 100);
  };

  // Enhanced Investment helper functions and interest rate tiers
  const interestRateTiers: InterestRateTier[] = [
    {
      tier: 'bronze',
      minAmount: 50,
      maxAmount: 999,
      dailyRate: 0.1, // 0.1% daily
      annualRate: 36.5, // 36.5% APY
      color: '#CD7F32',
      benefits: ['Basic interest rate', 'Standard support', 'Monthly reports']
    },
    {
      tier: 'silver',
      minAmount: 1000,
      maxAmount: 4999,
      dailyRate: 0.15, // 0.15% daily
      annualRate: 54.75, // 54.75% APY
      color: '#C0C0C0',
      benefits: ['Enhanced interest rate', 'Priority support', 'Weekly reports', 'Early withdrawal option']
    },
    {
      tier: 'gold',
      minAmount: 5000,
      maxAmount: 19999,
      dailyRate: 0.2, // 0.2% daily
      annualRate: 73, // 73% APY
      color: '#FFD700',
      benefits: ['Premium interest rate', 'Priority support', 'Daily reports', 'Reduced withdrawal fees', 'Auto-reinvest option']
    },
    {
      tier: 'platinum',
      minAmount: 20000,
      maxAmount: null,
      dailyRate: 0.3, // 0.3% daily
      annualRate: 109.5, // 109.5% APY
      color: '#E5E4E2',
      benefits: ['Maximum interest rate', 'Dedicated account manager', 'Real-time reports', 'No withdrawal fees', 'Advanced features', 'Custom investment terms']
    }
  ];

  const getInterestTier = (amount: number, cryptocurrency: string): InterestRateTier => {
    // Convert to USD equivalent for tier calculation
    const usdAmount = amount; // Simplified - in real implementation, convert using exchange rates

    for (let i = interestRateTiers.length - 1; i >= 0; i--) {
      const tier = interestRateTiers[i];
      if (usdAmount >= tier.minAmount && (tier.maxAmount === null || usdAmount <= tier.maxAmount)) {
        return tier;
      }
    }
    return interestRateTiers[0]; // Default to bronze
  };

  const calculateDailyInterest = (principal: number, dailyRate: number): number => {
    return principal * (dailyRate / 100);
  };

  const calculateAccumulatedInterest = (principal: number, dailyRate: number, daysActive: number): number => {
    return principal * (dailyRate / 100) * daysActive;
  };

  const getWithdrawalEligibilityStatus = (balance: number, cryptocurrency: string): WithdrawalEligibility => {
    const minimumUSDT = 50;
    const usdEquivalent = balance; // Simplified - in real implementation, convert using exchange rates
    const withdrawalFee = balance * 0.001; // 0.1% withdrawal fee
    const availableForWithdrawal = Math.max(0, balance - withdrawalFee);

    const eligible = usdEquivalent >= minimumUSDT;
    const status = eligible ? 'eligible' : 'insufficient_balance';

    return {
      eligible,
      currentBalance: balance,
      minimumRequired: minimumUSDT,
      availableForWithdrawal,
      usdEquivalent,
      withdrawalFee,
      cooldownRemaining: 0,
      status,
      reasons: eligible ? [] : [`Minimum withdrawal amount is ${minimumUSDT} USDT equivalent`]
    };
  };

  const formatTimeUntilDistribution = (nextDistribution: string): string => {
    const now = new Date();
    const next = new Date(nextDistribution);
    const diff = next.getTime() - now.getTime();

    if (diff <= 0) return '00:00:00';

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const getNextDistributionTime = (): string => {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(3, 0, 0, 0); // 03:00 UTC+3

    // If it's already past 03:00 today, set for tomorrow
    if (now.getHours() >= 3) {
      return tomorrow.toISOString();
    } else {
      // Set for today at 03:00
      const today = new Date(now);
      today.setHours(3, 0, 0, 0);
      return today.toISOString();
    }
  };

  const getPackageProgress = (pkg: InvestmentPackage): number => {
    return Math.min((pkg.daysActive / pkg.totalDays) * 100, 100);
  };

  const getPackageStatusColor = (status: string): string => {
    switch (status) {
      case 'active': return '#02C076';
      case 'completed': return '#F0B90B';
      case 'paused': return '#848E9C';
      case 'withdrawn': return '#F84960';
      default: return '#848E9C';
    }
  };



  // Enhanced real data fetching functions
  const fetchRealTransactions = async () => {
    if (!user) return;

    try {
      setDataLoading(true);
      setIsRefreshing(true);

      const response = await transactionService.getAll({
        page: currentTransactionPage,
        limit: transactionPageSize,
        sort: `${transactionSortOrder === 'desc' ? '-' : ''}${transactionSortBy === 'date' ? 'createdAt' : transactionSortBy}`
      });

      if (response.data && response.data.transactions) {
        const formattedTransactions = response.data.transactions.map((tx: any) => ({
          id: tx._id,
          type: tx.type === 'commission' ? 'Referral Commission' :
                tx.type === 'interest' ? 'Earnings' :
                tx.type === 'deposit' ? 'Deposit' : 'Withdrawal',
          cryptocurrency: tx.asset?.toUpperCase() || 'UNKNOWN',
          amount: Number(tx.amount) || 0,
          usdValue: tx.usdValue ? Number(tx.usdValue) : null,
          status: tx.status === 'completed' ? 'Confirmed' :
                  tx.status === 'pending' ? 'Processing' :
                  tx.status === 'failed' ? 'Failed' : 'Pending',
          date: tx.createdAt,
          fee: Number(tx.fee) || 0,
          confirmations: Number(tx.confirmations) || 0,
          txHash: tx.txHash || `0x${Math.random().toString(16).substr(2, 16)}...`,
          fromAddress: tx.fromAddress || (tx.type === 'deposit' ? 'External Wallet' : user.walletAddress),
          toAddress: tx.toAddress || (tx.type === 'withdrawal' ? 'External Wallet' : user.walletAddress),
          description: tx.description || `${tx.type} transaction`,
          direction: tx.type === 'deposit' || tx.type === 'commission' || tx.type === 'interest' ? 'incoming' : 'outgoing'
        }));

        // Create enhanced transactions with additional properties
        const enhancedTxs: EnhancedTransaction[] = formattedTransactions.map((tx: any) => ({
          ...tx,
          requiredConfirmations: getRequiredConfirmations(tx.cryptocurrency),
          network: getNetworkName(tx.cryptocurrency),
          gasPrice: tx.gasPrice || 0,
          blockNumber: tx.blockNumber || 0
        }));

        setRealTransactions(formattedTransactions);
        setEnhancedTransactions(enhancedTxs);
        setLastRefresh(new Date());
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      setDataError('Failed to fetch transactions');
    } finally {
      setDataLoading(false);
      setIsRefreshing(false);
    }
  };

  // Helper functions for enhanced transactions
  const getRequiredConfirmations = (cryptocurrency: string): number => {
    const confirmationMap: { [key: string]: number } = {
      BTC: 6,
      ETH: 12,
      USDT: 12,
      BNB: 15,
      SOL: 32,
      DOGE: 6,
      TRX: 5,
      ADA: 15,
      MATIC: 128,
      LTC: 6
    };
    return confirmationMap[cryptocurrency] || 12;
  };

  const getNetworkName = (cryptocurrency: string): string => {
    const networkMap: { [key: string]: string } = {
      BTC: 'Bitcoin',
      ETH: 'Ethereum',
      USDT: 'Ethereum (ERC-20)',
      BNB: 'BNB Smart Chain',
      SOL: 'Solana',
      DOGE: 'Dogecoin',
      TRX: 'Tron Network',
      ADA: 'Cardano',
      MATIC: 'Polygon',
      LTC: 'Litecoin'
    };
    return networkMap[cryptocurrency] || 'Unknown';
  };

  const fetchRealInvestmentPackages = async () => {
    if (!user) {
      console.log('🚫 INVESTMENT: No user found, skipping investment data fetch');
      setDataLoading(false);
      setDataError('Please log in to view investment data');
      return;
    }

    // Check if user has proper authentication
    if (!user._id || !user.email) {
      console.log('🚫 INVESTMENT: Invalid user data, skipping investment data fetch');
      setDataLoading(false);
      setDataError('Invalid user session. Please log in again.');
      return;
    }

    try {
      setDataLoading(true);
      console.log('🚀 INVESTMENT: Starting investment packages fetch for user:', user.email);

      const response = await investmentPackageService.getUserPackages({
        page: 1,
        limit: 50,
        status: 'active'
      }).catch(error => {
        console.warn('⚠️ INVESTMENT: Packages fetch failed:', error);
        // Check if it's an authentication error
        if (error?.response?.status === 401 || error?.response?.status === 403) {
          throw error; // Re-throw auth errors to handle them properly
        }
        return { data: { data: { packages: [] } } };
      });

      console.log('📥 INVESTMENT: Raw API response:', response);

      if (response.data && response.data.data && response.data.data.packages) {
        const packages = response.data.data.packages;
        setRealInvestmentPackages(packages);

        // Transform to enhanced investment packages
        const enhancedPackages: InvestmentPackage[] = packages.map((pkg: any) => {
          const tier = getInterestTier(pkg.principalAmount || 0, pkg.cryptocurrency || 'USDT');
          const daysActive = Math.floor((new Date().getTime() - new Date(pkg.startDate).getTime()) / (1000 * 60 * 60 * 24));
          const totalDays = Math.floor((new Date(pkg.maturityDate).getTime() - new Date(pkg.startDate).getTime()) / (1000 * 60 * 60 * 24));
          const accumulatedInterest = calculateAccumulatedInterest(pkg.principalAmount || 0, tier.dailyRate, daysActive);

          return {
            id: pkg._id,
            packageId: pkg.packageId || pkg._id,
            userId: pkg.userId,
            cryptocurrency: pkg.cryptocurrency?.toUpperCase() || 'USDT',
            principalAmount: Number(pkg.principalAmount) || 0,
            currentBalance: Number(pkg.currentBalance) || Number(pkg.principalAmount) || 0,
            accumulatedInterest,
            dailyInterestRate: tier.dailyRate,
            annualPercentageYield: tier.annualRate,
            startDate: pkg.startDate,
            maturityDate: pkg.maturityDate,
            lastInterestDistribution: pkg.lastInterestDistribution || pkg.startDate,
            nextInterestDistribution: getNextDistributionTime(),
            status: pkg.status === 'active' ? 'active' : pkg.status === 'completed' ? 'completed' : 'paused',
            totalEarned: Number(pkg.totalEarned) || accumulatedInterest,
            daysActive,
            totalDays,
            roi: pkg.principalAmount ? ((accumulatedInterest / pkg.principalAmount) * 100) : 0,
            interestTier: tier.tier,
            withdrawalEligible: getWithdrawalEligibilityStatus(Number(pkg.currentBalance) || 0, pkg.cryptocurrency || 'USDT').eligible,
            earlyWithdrawalPenalty: 0.05, // 5% penalty
            metadata: {
              createdFromDeposit: pkg.createdFromDeposit || false,
              depositTransactionId: pkg.depositTransactionId,
              autoReinvest: pkg.autoReinvest || false,
              compoundingEnabled: pkg.compoundingEnabled || false
            }
          };
        });

        setInvestmentPackages(enhancedPackages);
        console.log('✅ INVESTMENT: Packages loaded successfully:', enhancedPackages.length);
      } else {
        console.log('⚠️ INVESTMENT: No packages data in response');
        setInvestmentPackages([]);
      }
    } catch (error: any) {
      console.error('❌ INVESTMENT: Error fetching investment packages:', error);

      // Handle authentication errors specifically
      if (error?.response?.status === 401) {
        console.log('🔐 INVESTMENT: Authentication error - user needs to log in');
        setDataError('Authentication required. Please log in to view investment data.');
        return;
      }

      if (error?.response?.status === 403) {
        console.log('🚫 INVESTMENT: Access forbidden - insufficient permissions');
        setDataError('Access denied. You do not have permission to view investment data.');
        return;
      }

      // Handle other errors
      const errorMessage = error?.response?.data?.message ||
                          error?.message ||
                          'Failed to fetch investments';

      setDataError(`Network Error: ${errorMessage}`);
    } finally {
      setDataLoading(false);
    }
  };

  const fetchInterestDistributions = async () => {
    if (!user) return;

    try {
      console.log('🔄 Profile: Fetching interest distributions from unified transaction system');

      // Connect to unified transaction system for real distribution data
      // This will be implemented when the investment system is ready
      console.log('⚠️ Profile: Interest distribution system not yet connected to unified transaction system');

      // Set empty array until real implementation
      setInterestDistributions([]);

      console.log('✅ Profile: Interest distributions fetch completed (empty state)');
    } catch (error) {
      console.error('❌ Profile: Error fetching interest distributions:', error);
      setInterestDistributions([]);
    }
  };

  const calculateWithdrawalEligibility = () => {
    if (investmentPackages.length === 0) return;

    const totalBalance = investmentPackages.reduce((sum, pkg) => {
      return sum + pkg.currentBalance + pkg.accumulatedInterest;
    }, 0);

    const eligibility = getWithdrawalEligibilityStatus(totalBalance, 'USDT');
    setWithdrawalEligibility(eligibility);
  };

  const fetchRealEarningsSummary = async () => {
    if (!user) return;

    try {
      const response = await investmentPackageService.getEarnings();

      if (response.data && response.data.data) {
        setRealEarningsSummary(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching earnings summary:', error);
      setDataError('Failed to fetch earnings summary');
    }
  };

  // Fetch real data when user is available
  useEffect(() => {
    if (user) {
      fetchRealTransactions();
      fetchRealInvestmentPackages();
      fetchRealEarningsSummary();
      fetchReferralData();
    }
  }, [user, fetchReferralData]);

  // Fetch transactions when pagination or sorting changes
  useEffect(() => {
    if (user) {
      fetchRealTransactions();
    }
  }, [
    currentTransactionPage,
    transactionPageSize,
    transactionSortBy,
    transactionSortOrder
  ]);

  // Enhanced filtering and search functions
  const getFilteredTransactions = (): EnhancedTransaction[] => {
    let filtered = enhancedTransactions;

    // Filter by cryptocurrency
    if (transactionFilters.cryptocurrency !== 'all') {
      filtered = filtered.filter(tx => tx.cryptocurrency === transactionFilters.cryptocurrency);
    }

    // Filter by type
    if (transactionFilters.type !== 'all') {
      filtered = filtered.filter(tx => tx.type === transactionFilters.type);
    }

    // Filter by status
    if (transactionFilters.status !== 'all') {
      filtered = filtered.filter(tx => tx.status === transactionFilters.status);
    }

    // Filter by search query
    if (transactionFilters.searchQuery) {
      const query = transactionFilters.searchQuery.toLowerCase();
      filtered = filtered.filter(tx =>
        tx.txHash.toLowerCase().includes(query) ||
        tx.cryptocurrency.toLowerCase().includes(query) ||
        tx.type.toLowerCase().includes(query) ||
        tx.description.toLowerCase().includes(query)
      );
    }

    // Filter by date range
    if (transactionFilters.dateRange.start && transactionFilters.dateRange.end) {
      const startDate = new Date(transactionFilters.dateRange.start);
      const endDate = new Date(transactionFilters.dateRange.end);
      filtered = filtered.filter(tx => {
        const txDate = new Date(tx.date);
        return txDate >= startDate && txDate <= endDate;
      });
    }

    return filtered;
  };

  const handleFilterChange = (filterType: keyof TransactionFilters, value: any) => {
    setTransactionFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const clearFilters = () => {
    setTransactionFilters({
      cryptocurrency: 'all',
      type: 'all',
      status: 'all',
      dateRange: { start: '', end: '' },
      searchQuery: ''
    });
  };

  const toggleRowExpansion = (transactionId: string) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev);
      if (newSet.has(transactionId)) {
        newSet.delete(transactionId);
      } else {
        newSet.add(transactionId);
      }
      return newSet;
    });
  };

  // Enhanced auto-refresh with investment data
  useEffect(() => {
    if (autoRefresh && user) {
      const interval = setInterval(() => {
        fetchRealTransactions();
        fetchRealInvestmentPackages();
        fetchRealEarningsSummary();
        fetchInterestDistributions();
        calculateWithdrawalEligibility();
      }, refreshInterval * 1000);

      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, user]);

  // Countdown timer for next interest distribution
  useEffect(() => {
    const updateCountdown = () => {
      const nextDistribution = getNextDistributionTime();
      const countdown = formatTimeUntilDistribution(nextDistribution);
      setNextDistributionCountdown(countdown);
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);
    return () => clearInterval(interval);
  }, []);

  // Real-time interest accrual simulation
  useEffect(() => {
    if (investmentPackages.length === 0) return;

    const interval = setInterval(() => {
      const newAccrual: { [packageId: string]: number } = {};

      investmentPackages.forEach(pkg => {
        if (pkg.status === 'active') {
          const secondsInDay = 24 * 60 * 60;
          const interestPerSecond = calculateDailyInterest(pkg.principalAmount, pkg.dailyInterestRate) / secondsInDay;
          const currentAccrual = realTimeInterestAccrual[pkg.id] || 0;
          newAccrual[pkg.id] = currentAccrual + interestPerSecond;
        }
      });

      setRealTimeInterestAccrual(newAccrual);
    }, 1000);

    return () => clearInterval(interval);
  }, [investmentPackages]);

  // Calculate withdrawal eligibility when packages change
  useEffect(() => {
    calculateWithdrawalEligibility();
  }, [investmentPackages]);

  // Fetch investment data on component mount
  useEffect(() => {
    if (user) {
      fetchInterestDistributions();
      fetchReferralData();
    }
  }, [user, fetchReferralData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.id]: e.target.value,
    });
  };

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    try {
      await updateProfile({
        firstName: formData.firstName,
        lastName: formData.lastName,
        country: formData.country,
        city: formData.city,
        address: formData.address,
        postalCode: formData.postalCode,
        phone: formData.phone,
      });
      setSuccess(t('profile.updateSuccess', 'Profile updated successfully'));
    } catch (err: any) {
      setError(err.response?.data?.message || t('profile.updateError', 'Failed to update profile'));
    }
  };

  const handlePasswordUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (formData.newPassword !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (formData.newPassword.length < 8) {
      setError('Password must be at least 8 characters');
      return;
    }

    try {
      await updateProfile({
        // Use a different property name that exists in the User type
        updatedPassword: formData.newPassword,
      } as any); // Use type assertion to bypass TypeScript check
      setSuccess('Password updated successfully');
      setFormData({
        ...formData,
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update password');
    }
  };

  const handleEnableTwoFactor = async () => {
    // In a real app, this would call your API to enable 2FA
    setSuccess('Two-factor authentication enabled');
  };

  const handleVerifyKYC = async () => {
    // In a real app, this would call your API to verify KYC
    setSuccess('KYC verification initiated');
  };

  const handleCopyToClipboard = (text: string, message: string = 'Copied to clipboard') => {
    navigator.clipboard.writeText(text);
    setSuccess(message);
    setTimeout(() => setSuccess(''), 2000);
  };







  // Render login prompt if user is not authenticated
  if (!isAuthenticated) {
    return (
      <Box bg="#0B0E11" minH="100vh" className="safe-area-all">
        <Container
          maxW={{ base: "100%", md: "100vw" }}
          px={{ base: 2, sm: 3, md: 4, lg: 6, xl: 8 }}
          py={{ base: 3, sm: 4, md: 6, lg: 8 }}
          overflow="hidden"
          className="mobile-full-width"
        >
          <Heading
            size={{ base: "md", md: "lg" }}
            color="#F0B90B"
            mb={{ base: 4, sm: 6, md: 8 }}
            textAlign={{ base: "center", md: "left" }}
            className="responsive-heading text-wrap"
            lineHeight={{ base: "1.2", md: "1.1" }}
            px={{ base: 2, md: 0 }}
          >
            WALLET
          </Heading>

          <Box
            maxW="500px"
            mx="auto"
            mt={{ base: 8, md: 12 }}
          >
            <LoginPrompt
              title="Access Your Wallet"
              message="Please log in to access your wallet, view your investments, and manage your referrals"
              feature="your wallet and investment data"
            />
          </Box>
        </Container>
      </Box>
    );
  }

  return (
    <Box bg="#0B0E11" minH="100vh" className="safe-area-all">
      {/* Custom Modals */}
      <DepositModal isOpen={isDepositOpen} onClose={onDepositClose} />
      <WithdrawModal
        isOpen={isWithdrawOpen}
        onClose={onWithdrawClose}
        initialCrypto={selectedCrypto?.toLowerCase() || 'usdt'}
        initialWithdrawalType={initialWithdrawalType}
        onSuccess={() => {
          fetchWallet();
          toast({
            title: 'Withdrawal Submitted',
            description: 'Your withdrawal request has been submitted and is pending approval.',
            status: 'success',
            duration: 5000,
            isClosable: true,
          });
        }}
      />

      <Container
        maxW={{ base: "100%", md: "100vw" }}
        px={{ base: 2, sm: 3, md: 4, lg: 6, xl: 8 }}
        py={{ base: 3, sm: 4, md: 6, lg: 8 }}
        overflow="hidden"
        className="mobile-full-width"
      >
        {/* Email Verification Banner */}
        <EmailVerificationBanner variant="banner" />

        {error && (
          <Alert status="error" mb={4} borderRadius="md" bg="#F6465D33" color="#F6465D" border="1px solid #F6465D">
            <AlertIcon color="#F6465D" />
            {error}
          </Alert>
        )}

        {success && (
          <Alert status="success" mb={4} borderRadius="md" bg="#0ECB8133" color="#0ECB81" border="1px solid #0ECB81">
            <AlertIcon color="#0ECB81" />
            {success}
          </Alert>
        )}

        <Grid
          templateColumns={{
            base: "1fr",
            sm: "1fr",
            md: "1fr",
            lg: "280px 1fr",
            xl: "320px 1fr",
            "2xl": "350px 1fr"
          }}
          gap={{ base: 3, sm: 4, md: 5, lg: 6, xl: 8 }}
          maxW={{ base: "100%", lg: "1400px", xl: "1600px", "2xl": "1800px" }}
          mx="auto"
          w="100%"
          overflow="hidden"
          className="profile-tabs mobile-full-width"
        >
          {/* Enhanced Left Sidebar */}
          <GridItem w="100%" overflow="hidden">
            <VStack spacing={{ base: 2, sm: 3, md: 4 }} align="stretch" w="100%">
              <Box
                bg="linear-gradient(135deg, rgba(30, 35, 41, 0.95) 0%, rgba(30, 35, 41, 0.85) 100%)"
                backdropFilter="blur(20px)"
                p={{ base: 3, sm: 4, md: 5, lg: 6 }}
                borderRadius={{ base: "lg", md: "xl" }}
                borderWidth="1px"
                borderColor="rgba(240, 185, 11, 0.2)"
                boxShadow="0 8px 32px rgba(0, 0, 0, 0.3)"
                textAlign="center"
                position="relative"
                overflow="hidden"
                w="100%"
                _before={{
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  height: "2px",
                  background: "linear-gradient(90deg, transparent, rgba(240, 185, 11, 0.6), transparent)",
                }}
              >
                <Avatar
                  size={{ base: "lg", md: "xl" }}
                  name={`${user?.firstName} ${user?.lastName}`}
                  bg="linear-gradient(135deg, #FCD535 0%, #F0B90B 100%)"
                  color="#0B0E11"
                  mb={{ base: 3, md: 4 }}
                  mx="auto"
                  borderWidth="3px"
                  borderColor="rgba(240, 185, 11, 0.3)"
                />
                <Heading
                  size={{ base: "sm", md: "md" }}
                  color="#EAECEF"
                  mb={2}
                  fontWeight="600"
                >
                  {user?.firstName || 'Admin'}
                </Heading>
                <Text
                  color="#848E9C"
                  mb={4}
                  fontSize={{ base: "xs", md: "sm" }}
                  fontFamily="mono"
                >
                  {t('profile.id', 'ID')}: {user?._id?.substring(0, 7) || 'Loading...'}
                </Text>


              </Box>

              <Box
                bg="linear-gradient(135deg, rgba(30, 35, 41, 0.95) 0%, rgba(30, 35, 41, 0.85) 100%)"
                backdropFilter="blur(15px)"
                p={{ base: 2, sm: 3, md: 4 }}
                borderRadius={{ base: "lg", md: "xl" }}
                borderWidth="1px"
                borderColor="rgba(43, 49, 57, 0.8)"
                boxShadow="0 4px 20px rgba(0, 0, 0, 0.2)"
                w="100%"
                overflow="hidden"
              >
                <VStack spacing={{ base: 1, sm: 1.5, md: 2 }} align="stretch" w="100%">
                  {/* BASIC PROFILE MANAGEMENT */}
                  <Button
                    variant="ghost"
                    justifyContent="flex-start"
                    color={activeTab === 0 ? "#FCD535" : "#EAECEF"}
                    bg={activeTab === 0 ? "rgba(240, 185, 11, 0.2)" : "transparent"}
                    borderRadius={{ base: "md", md: "lg" }}
                    borderWidth="1px"
                    borderColor={activeTab === 0 ? "rgba(240, 185, 11, 0.4)" : "transparent"}
                    fontWeight={activeTab === 0 ? "600" : "normal"}
                    onClick={() => setActiveTab(0)}
                    leftIcon={<Icon as={FaUserCheck} color={activeTab === 0 ? "#FCD535" : "#EAECEF"} boxSize={{ base: 3.5, md: 4 }} />}
                    size={{ base: "sm", md: "md", lg: "sm" }}
                    minH={{ base: "40px", sm: "42px", md: "44px", lg: "40px" }}
                    fontSize={{ base: "xs", sm: "sm", md: "sm" }}
                    px={{ base: 2, sm: 3, md: 4 }}
                    w="100%"
                    transition="all 0.3s ease"
                    _hover={{
                      bg: activeTab === 0 ? "rgba(240, 185, 11, 0.3)" : "rgba(43, 49, 57, 0.5)",
                      borderColor: "rgba(240, 185, 11, 0.6)",
                      transform: { base: "none", lg: "translateX(4px)" }
                    }}
                    _active={{
                      transform: "scale(0.98)"
                    }}
                  >
                    {t('profile.sidebar.profile', 'Profile')}
                  </Button>

                  <Button
                    variant="ghost"
                    justifyContent="flex-start"
                    color={activeTab === 8 ? "#F0B90B" : "#EAECEF"}
                    bg={activeTab === 8 ? "rgba(240, 185, 11, 0.1)" : "transparent"}
                    borderLeft={activeTab === 8 ? "4px solid #F0B90B" : "4px solid transparent"}
                    fontWeight={activeTab === 8 ? "bold" : "normal"}
                    _hover={{ bg: "#2B3139" }}
                    onClick={() => setActiveTab(8)}
                    leftIcon={<Icon as={FaMoneyBillWave} color={activeTab === 8 ? "#F0B90B" : "#EAECEF"} />}
                    size={{ base: "md", lg: "sm" }}
                    minH={{ base: "44px", lg: "auto" }}
                    fontSize={{ base: "sm", lg: "sm" }}
                  >
                    {t('profile.sidebar.transactions', 'Transactions')}
                  </Button>

                  <Button
                    variant="ghost"
                    justifyContent="flex-start"
                    color={activeTab === 13 ? "#F0B90B" : "#EAECEF"}
                    bg={activeTab === 13 ? "rgba(240, 185, 11, 0.1)" : "transparent"}
                    borderLeft={activeTab === 13 ? "4px solid #F0B90B" : "4px solid transparent"}
                    fontWeight={activeTab === 13 ? "bold" : "normal"}
                    _hover={{ bg: "#2B3139" }}
                    onClick={() => setActiveTab(13)}
                    leftIcon={<Icon as={FaCoins} color={activeTab === 13 ? "#F0B90B" : "#EAECEF"} />}
                    size={{ base: "md", lg: "sm" }}
                    minH={{ base: "44px", lg: "auto" }}
                    fontSize={{ base: "sm", lg: "sm" }}
                  >
                    {t('profile.sidebar.investments', 'Investments')}
                  </Button>

                  <Button
                    variant="ghost"
                    justifyContent="flex-start"
                    color={activeTab === 5 ? "#F0B90B" : "#EAECEF"}
                    bg={activeTab === 5 ? "rgba(240, 185, 11, 0.1)" : "transparent"}
                    borderLeft={activeTab === 5 ? "4px solid #F0B90B" : "4px solid transparent"}
                    fontWeight={activeTab === 5 ? "bold" : "normal"}
                    _hover={{ bg: "#2B3139" }}
                    onClick={() => setActiveTab(5)}
                    leftIcon={<Icon as={FaUserFriends} color={activeTab === 5 ? "#F0B90B" : "#EAECEF"} />}
                    size={{ base: "md", lg: "sm" }}
                    minH={{ base: "44px", lg: "auto" }}
                    fontSize={{ base: "sm", lg: "sm" }}
                  >
                    {t('profile.sidebar.referrals', 'Referrals')}
                  </Button>



                  {/* SECURITY & VERIFICATION */}
                  <Button
                    variant="ghost"
                    justifyContent="flex-start"
                    color={activeTab === 1 ? "#F0B90B" : "#EAECEF"}
                    bg={activeTab === 1 ? "rgba(240, 185, 11, 0.1)" : "transparent"}
                    borderLeft={activeTab === 1 ? "4px solid #F0B90B" : "4px solid transparent"}
                    fontWeight={activeTab === 1 ? "bold" : "normal"}
                    _hover={{ bg: "#2B3139" }}
                    onClick={() => setActiveTab(1)}
                    leftIcon={<Icon as={FaShieldAlt} color={activeTab === 1 ? "#F0B90B" : "#EAECEF"} />}
                    size="sm"
                  >
                    {t('profile.sidebar.privacy', 'Privacy')}
                  </Button>

                  <Button
                    variant="ghost"
                    justifyContent="flex-start"
                    color={activeTab === 6 ? "#F0B90B" : "#EAECEF"}
                    bg={activeTab === 6 ? "rgba(240, 185, 11, 0.1)" : "transparent"}
                    borderLeft={activeTab === 6 ? "4px solid #F0B90B" : "4px solid transparent"}
                    fontWeight={activeTab === 6 ? "bold" : "normal"}
                    _hover={{ bg: "#2B3139" }}
                    onClick={() => setActiveTab(6)}
                    leftIcon={<Icon as={FaUserCheck} color={activeTab === 6 ? "#F0B90B" : "#EAECEF"} />}
                    size="sm"
                  >
                    {t('profile.sidebar.verification', 'Verification')}
                  </Button>



                  {/* ADVANCED FEATURES */}
                  <Button
                    variant="ghost"
                    justifyContent="flex-start"
                    color={activeTab === 10 ? "#F0B90B" : "#EAECEF"}
                    bg={activeTab === 10 ? "rgba(240, 185, 11, 0.1)" : "transparent"}
                    borderLeft={activeTab === 10 ? "4px solid #F0B90B" : "4px solid transparent"}
                    fontWeight={activeTab === 10 ? "bold" : "normal"}
                    _hover={{ bg: "#2B3139" }}
                    onClick={() => setActiveTab(10)}
                    leftIcon={<Icon as={FaChartLine} color={activeTab === 10 ? "#F0B90B" : "#EAECEF"} />}
                    size="sm"
                  >
                    {t('profile.sidebar.analytics', 'Analytics')}
                  </Button>



                  {/* SETTINGS */}
                  <Button
                    variant="ghost"
                    justifyContent="flex-start"
                    color={activeTab === 7 ? "#F0B90B" : "#EAECEF"}
                    bg={activeTab === 7 ? "rgba(240, 185, 11, 0.1)" : "transparent"}
                    borderLeft={activeTab === 7 ? "4px solid #F0B90B" : "4px solid transparent"}
                    fontWeight={activeTab === 7 ? "bold" : "normal"}
                    _hover={{ bg: "#2B3139" }}
                    onClick={() => setActiveTab(7)}
                    leftIcon={<Icon as={FaWallet} color={activeTab === 7 ? "#F0B90B" : "#EAECEF"} />}
                    size="sm"
                  >
                    {t('common.settings', 'Settings')}
                  </Button>

                  {/* LOGOUT */}
                  <Button
                    variant="ghost"
                    justifyContent="flex-start"
                    color="red.400"
                    _hover={{ bg: "#2B3139" }}
                    leftIcon={<Icon as={FaKey} color="red.400" />}
                    size="sm"
                  >
                    {t('common.logout', 'Log Out')}
                  </Button>
                </VStack>
              </Box>
            </VStack>
          </GridItem>

          {/* Main Content */}
          <GridItem w="100%" overflow="hidden">
            {/* Enhanced Main Profile Content */}
            <Box
              bg="linear-gradient(135deg, rgba(30, 35, 41, 0.95) 0%, rgba(30, 35, 41, 0.85) 100%)"
              backdropFilter="blur(20px)"
              p={{ base: 3, sm: 4, md: 5, lg: 6, xl: 8 }}
              borderRadius={{ base: "lg", md: "xl", lg: "2xl" }}
              borderWidth="1px"
              borderColor="rgba(43, 49, 57, 0.8)"
              boxShadow="0 4px 20px rgba(0, 0, 0, 0.2)"
              position="relative"
              overflow="hidden"
              w="100%"
              _before={{
                content: '""',
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                height: "1px",
                background: "linear-gradient(90deg, transparent, rgba(43, 49, 57, 0.6), transparent)",
              }}
            >
              <Heading
                size={{ base: "sm", md: "md" }}
                color="#EAECEF"
                mb={{ base: 4, md: 6 }}
                fontWeight="600"
              >
                {activeTab === 13 ? 'Wallet' : 'Account Settings'}
              </Heading>

              {/* Enhanced Wallet Cards */}
              {activeTab === 13 && (
                <Box mb={{ base: 6, md: 8 }}>
                  {walletLoading ? (
                    <Flex justify="center" py={12}>
                      <VStack spacing={4}>
                        <Spinner size="xl" color="#F0B90B" thickness="4px" />
                        <Text color="#848E9C">Loading your wallets...</Text>
                      </VStack>
                    </Flex>
                  ) : wallet && wallet.assets.length > 0 ? (
                    <SimpleGrid columns={{ base: 1, lg: 2, xl: 3 }} spacing={6}>
                      {wallet.assets.map((asset, index) => (
                        <WalletCard
                          key={index}
                          asset={asset}
                          investments={[]} // Real investments from unified transaction system
                          onDeposit={() => {
                            setSelectedCrypto(asset.symbol);
                            onDepositOpen();
                            toast({
                              title: 'Deposit',
                              description: `Opening deposit modal for ${asset.symbol}`,
                              status: 'info',
                              duration: 2000,
                              isClosable: true,
                            });
                          }}
                          onWithdraw={() => {
                            setSelectedCrypto(asset.symbol);
                            setInitialWithdrawalType('interest');
                            onWithdrawOpen();
                            toast({
                              title: 'Withdraw',
                              description: `Opening withdraw modal for ${asset.symbol}`,
                              status: 'info',
                              duration: 2000,
                              isClosable: true,
                            });
                          }}
                          onViewDetails={() => {
                            toast({
                              title: 'View Details',
                              description: `Viewing details for ${asset.symbol}`,
                              status: 'info',
                              duration: 3000,
                              isClosable: true,
                            });
                          }}
                        />
                      ))}
                    </SimpleGrid>
                  ) : (
                    <Box
                      bg="#1E2329"
                      p={8}
                      textAlign="center"
                      borderRadius="lg"
                      border="1px solid #2B3139"
                    >
                      <VStack spacing={4}>
                        <Icon as={FaWallet} boxSize={12} color="#848E9C" />
                        <Heading size="md" color="#EAECEF">
                          No Investment Wallets Found
                        </Heading>
                        <Text color="#848E9C">
                          Start your crypto investment journey with CryptoYield
                        </Text>
                        <Button
                          bg="#F0B90B"
                          color="#0B0E11"
                          _hover={{ bg: "#F8D12F" }}
                          size="lg"
                          leftIcon={<Icon as={FaArrowDown} />}
                          onClick={() => onDepositOpen()}
                        >
                          Make First Investment
                        </Button>
                      </VStack>
                    </Box>
                  )}
                </Box>
              )}
              
            {activeTab === 0 && (
              <Box>
                <form onSubmit={handleProfileUpdate}>
                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6} mb={6}>
                    <FormControl id="firstName">
                      <FormLabel color="#848E9C" fontSize="sm">First Name</FormLabel>
                      <Input
                        type="text"
                        value={formData.firstName}
                        onChange={handleChange}
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                        _hover={{ borderColor: "#F0B90B" }}
                        _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                        minH={{ base: "44px", md: "auto" }}
                        fontSize={{ base: "16px", md: "14px" }}
                      />
                    </FormControl>
                    <FormControl id="lastName">
                      <FormLabel color="#848E9C" fontSize="sm">Last Name</FormLabel>
                      <Input
                        type="text"
                        value={formData.lastName}
                        onChange={handleChange}
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                        _hover={{ borderColor: "#F0B90B" }}
                        _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                        minH={{ base: "44px", md: "auto" }}
                        fontSize={{ base: "16px", md: "14px" }}
                      />
                    </FormControl>
                  </SimpleGrid>

                  <FormControl id="email" mb={6}>
                    <FormLabel color="#848E9C" fontSize="sm">Email</FormLabel>
                    <Input
                      type="email"
                      value={formData.email}
                      isReadOnly
                      bg="#0B0E11"
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                      _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                      minH={{ base: "44px", md: "auto" }}
                      fontSize={{ base: "16px", md: "14px" }}
                    />
                  </FormControl>

                  <FormControl id="phone" mb={6}>
                    <FormLabel color="#848E9C" fontSize="sm">Phone Number</FormLabel>
                    <Input
                      type="tel"
                      value={formData.phone}
                      onChange={handleChange}
                      bg="#0B0E11"
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                      _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                      minH={{ base: "44px", md: "auto" }}
                      fontSize={{ base: "16px", md: "14px" }}
                    />
                  </FormControl>

                  <Heading size="sm" color="#EAECEF" mb={4}>Address Information</Heading>

                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6} mb={6}>
                    <FormControl id="country">
                      <FormLabel color="#848E9C" fontSize="sm">
                        {t('profile.address.country', 'Country')}
                      </FormLabel>
                      <Select
                        value={formData.country}
                        onChange={(e) => setFormData({...formData, country: e.target.value})}
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                        _hover={{ borderColor: "#F0B90B" }}
                        _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                        placeholder={t('profile.address.selectCountry', 'Select your country')}
                        minH={{ base: "44px", md: "auto" }}
                        fontSize={{ base: "16px", md: "14px" }}
                      >
                        {COUNTRIES.map((country) => (
                          <option
                            key={country.code}
                            value={country.name}
                            style={{
                              backgroundColor: '#1E2026',
                              color: '#EAECEF',
                              padding: '8px'
                            }}
                          >
                            {country.flag} {country.name}
                          </option>
                        ))}
                      </Select>
                      <Text color="#848E9C" fontSize="xs" mt={1}>
                        {t('profile.address.countryDesc', 'Select your country of residence')}
                      </Text>
                    </FormControl>
                    <FormControl id="city">
                      <FormLabel color="#848E9C" fontSize="sm">
                        {t('profile.address.city', 'City')}
                      </FormLabel>
                      <Input
                        type="text"
                        value={formData.city}
                        onChange={(e) => setFormData({...formData, city: e.target.value})}
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                        _hover={{ borderColor: "#F0B90B" }}
                        _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                        placeholder={t('profile.address.cityPlaceholder', 'Enter your city')}
                        minH={{ base: "44px", md: "auto" }}
                        fontSize={{ base: "16px", md: "14px" }}
                      />
                      <Text color="#848E9C" fontSize="xs" mt={1}>
                        {t('profile.address.cityDesc', 'Enter your city of residence')}
                      </Text>
                    </FormControl>
                  </SimpleGrid>

                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6} mb={6}>
                    <FormControl id="address">
                      <FormLabel color="#848E9C" fontSize="sm">
                        {t('profile.address.address', 'Address')}
                      </FormLabel>
                      <Input
                        type="text"
                        value={formData.address}
                        onChange={(e) => setFormData({...formData, address: e.target.value})}
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                        _hover={{ borderColor: "#F0B90B" }}
                        _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                        placeholder={t('profile.address.addressPlaceholder', 'Enter your street address')}
                        minH={{ base: "44px", md: "auto" }}
                        fontSize={{ base: "16px", md: "14px" }}
                      />
                      <Text color="#848E9C" fontSize="xs" mt={1}>
                        {t('profile.address.addressDesc', 'Enter your full street address')}
                      </Text>
                    </FormControl>
                    <FormControl id="postalCode">
                      <FormLabel color="#848E9C" fontSize="sm">
                        {t('profile.address.postalCode', 'Postal Code')}
                      </FormLabel>
                      <Input
                        type="text"
                        value={formData.postalCode}
                        onChange={(e) => setFormData({...formData, postalCode: e.target.value})}
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                        _hover={{ borderColor: "#F0B90B" }}
                        _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                        placeholder={t('profile.address.postalCodePlaceholder', 'Enter postal code')}
                        minH={{ base: "44px", md: "auto" }}
                        fontSize={{ base: "16px", md: "14px" }}
                      />
                      <Text color="#848E9C" fontSize="xs" mt={1}>
                        {t('profile.address.postalCodeDesc', 'Enter your postal/ZIP code')}
                      </Text>
                    </FormControl>
                  </SimpleGrid>

                  {/* Phone Number Field */}
                  <FormControl id="phone" mb={6}>
                    <FormLabel color="#848E9C" fontSize="sm">
                      {t('profile.address.phone', 'Phone Number')}
                    </FormLabel>
                    <Input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData({...formData, phone: e.target.value})}
                      bg="#0B0E11"
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                      _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                      placeholder={t('profile.address.phonePlaceholder', 'Enter your phone number')}
                      minH={{ base: "44px", md: "auto" }}
                      fontSize={{ base: "16px", md: "14px" }}
                    />
                    <Text color="#848E9C" fontSize="xs" mt={1}>
                      {t('profile.address.phoneDesc', 'Enter your phone number with country code')}
                    </Text>
                  </FormControl>

                  <Button
                    type="submit"
                    bg="#F0B90B"
                    color="#0B0E11"
                    _hover={{ bg: "#F8D12F" }}
                    isLoading={loading}
                    mt={4}
                    minH={{ base: "44px", md: "auto" }}
                    fontSize={{ base: "16px", md: "14px" }}
                    w={{ base: "100%", md: "auto" }}
                  >
                    Save Changes
                  </Button>
                </form>
              </Box>
            )}

            {activeTab === 1 && (
              <Box>
                <Box mb={8}>
                  <Heading size="sm" color="#EAECEF" mb={4}>Password</Heading>
                  <form onSubmit={handlePasswordUpdate}>
                    <Stack spacing={4}>
                      <FormControl id="currentPassword">
                        <FormLabel color="#848E9C" fontSize="sm">Current Password</FormLabel>
                        <InputGroup>
                          <Input
                            type={showPassword ? "text" : "password"}
                            value={formData.currentPassword}
                            onChange={handleChange}
                            bg="#0B0E11"
                            borderColor="#2B3139"
                            color="#EAECEF"
                            _hover={{ borderColor: "#F0B90B" }}
                            _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                          />
                          <InputRightElement>
                            <Icon
                              as={showPassword ? ViewOffIcon : ViewIcon}
                              color="#848E9C"
                              cursor="pointer"
                              onClick={() => setShowPassword(!showPassword)}
                            />
                          </InputRightElement>
                        </InputGroup>
                      </FormControl>
                      <FormControl id="newPassword">
                        <FormLabel color="#848E9C" fontSize="sm">New Password</FormLabel>
                        <InputGroup>
                          <Input
                            type={showNewPassword ? "text" : "password"}
                            value={formData.newPassword}
                            onChange={handleChange}
                            bg="#0B0E11"
                            borderColor="#2B3139"
                            color="#EAECEF"
                            _hover={{ borderColor: "#F0B90B" }}
                            _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                          />
                          <InputRightElement>
                            <Icon
                              as={showNewPassword ? ViewOffIcon : ViewIcon}
                              color="#848E9C"
                              cursor="pointer"
                              onClick={() => setShowNewPassword(!showNewPassword)}
                            />
                          </InputRightElement>
                        </InputGroup>
                      </FormControl>
                      <FormControl id="confirmPassword">
                        <FormLabel color="#848E9C" fontSize="sm">Confirm New Password</FormLabel>
                        <Input
                          type={showNewPassword ? "text" : "password"}
                          value={formData.confirmPassword}
                          onChange={handleChange}
                          bg="#0B0E11"
                          borderColor="#2B3139"
                          color="#EAECEF"
                          _hover={{ borderColor: "#F0B90B" }}
                          _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                        />
                      </FormControl>
                      <Button
                        type="submit"
                        bg="#F0B90B"
                        color="#0B0E11"
                        _hover={{ bg: "#F8D12F" }}
                        isLoading={loading}
                      >
                        Update Password
                      </Button>
                    </Stack>
                  </form>
                </Box>

                <Divider borderColor="#2B3139" my={6} />

                <Box mb={8}>
                  <Heading size="sm" color="#EAECEF" mb={4}>Two-Factor Authentication (2FA)</Heading>
                  <Box bg="#0B0E11" p={4} borderRadius="md" mb={4}>
                    <Flex
                      direction={{ base: "column", md: "row" }}
                      align={{ base: "stretch", md: "center" }}
                      justify="space-between"
                      gap={4}
                    >
                      <HStack spacing={3}>
                        <Icon as={FaGoogle} color="#F0B90B" boxSize={5} />
                        <Box>
                          <Text color="#EAECEF" fontSize={{ base: "md", md: "sm" }}>Google Authenticator</Text>
                          <Text color="#848E9C" fontSize="sm">Protect your account with Google Authenticator</Text>
                        </Box>
                      </HStack>
                      <Switch
                        isChecked={user?.twoFactorEnabled}
                        onChange={handleEnableTwoFactor}
                        colorScheme="yellow"
                        alignSelf={{ base: "flex-start", md: "center" }}
                      />
                    </Flex>
                  </Box>

                  {!user?.twoFactorEnabled && (
                    <Button
                      leftIcon={<Icon as={FaShieldAlt} />}
                      variant="outline"
                      borderColor="#F0B90B"
                      color="#F0B90B"
                      _hover={{ bg: "#F0B90B22" }}
                      onClick={handleEnableTwoFactor}
                    >
                      Enable 2FA
                    </Button>
                  )}
                </Box>

                <Divider borderColor="#2B3139" my={6} />

                <Box>
                  <Heading size="sm" color="#EAECEF" mb={4}>Identity Verification (KYC)</Heading>
                  <Box bg="#0B0E11" p={4} borderRadius="md" mb={4}>
                    <Flex align="center" justify="space-between">
                      <HStack>
                        <Icon as={FaUserCheck} color="#F0B90B" boxSize={5} />
                        <Box>
                          <Text color="#EAECEF">Identity Verification</Text>
                          <Text color="#848E9C" fontSize="sm">Verify your identity to unlock all features</Text>
                        </Box>
                      </HStack>
                      <Badge
                        bg={user?.kycVerified ? "#0ECB8133" : "#F6465D33"}
                        color={user?.kycVerified ? "#0ECB81" : "#F6465D"}
                        px={2}
                        py={1}
                        borderRadius="full"
                      >
                        {user?.kycVerified ? 'Verified' : 'Not Verified'}
                      </Badge>
                    </Flex>
                  </Box>

                  {!user?.kycVerified && (
                    <Button
                      leftIcon={<Icon as={FaUserCheck} />}
                      bg="#F0B90B"
                      color="#0B0E11"
                      _hover={{ bg: "#F8D12F" }}
                      onClick={handleVerifyKYC}
                    >
                      Start Verification
                    </Button>
                  )}
                </Box>
              </Box>
            )}



            {/* Enhanced Transaction History Section */}
            {activeTab === 8 && (
              <Box>
                {/* Header with Real-time Status */}
                <Box mb={6}>
                  <Flex
                    direction={{ base: "column", md: "row" }}
                    justify="space-between"
                    align={{ base: "stretch", md: "center" }}
                    mb={4}
                    gap={4}
                  >
                    <Heading size={{ base: "md", md: "lg" }} color="#F0B90B">
                      {t('transactions.title', 'Transaction History')}
                    </Heading>
                    <VStack spacing={2} align={{ base: "stretch", md: "flex-end" }}>
                      <HStack spacing={2} justify={{ base: "space-between", md: "flex-end" }}>
                        <Icon
                          as={FaSync}
                          color={isRefreshing ? "#F0B90B" : "#848E9C"}
                          boxSize={4}
                          animation={isRefreshing ? "spin 1s linear infinite" : "none"}
                        />
                        <Text color="#848E9C" fontSize="sm">
                          {t('transactions.lastUpdate', 'Last update')}: {lastRefresh.toLocaleTimeString()}
                        </Text>
                      </HStack>
                      <HStack spacing={2} justify={{ base: "space-between", md: "flex-end" }}>
                        <Switch
                          isChecked={autoRefresh}
                          onChange={(e) => setAutoRefresh(e.target.checked)}
                          colorScheme="yellow"
                          size="sm"
                        />
                        <Text color="#848E9C" fontSize="sm">
                          {t('transactions.autoRefresh', 'Auto-refresh')}
                        </Text>
                      </HStack>
                    </VStack>
                  </Flex>
                  <Text color="#848E9C" fontSize={{ base: "sm", md: "md" }}>
                    {t('transactions.description', 'Real-time cryptocurrency transaction monitoring with advanced filtering and detailed analytics.')}
                  </Text>
                </Box>

                {/* Enhanced Transaction Summary Cards */}
                <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4} mb={6}>
                  <Stat
                    bg="#1E2026"
                    p={{ base: 3, md: 4 }}
                    borderRadius="lg"
                    borderWidth="1px"
                    borderColor="#2B3139"
                    _hover={{ borderColor: "#F0B90B", transform: { base: "none", md: "translateY(-2px)" } }}
                    transition="all 0.3s ease"
                  >
                    <StatLabel color="#848E9C" fontSize={{ base: "xs", md: "sm" }}>
                      {t('transactions.stats.totalTransactions', 'Total Transactions')}
                    </StatLabel>
                    <StatNumber color="#EAECEF" fontSize={{ base: "xl", md: "2xl" }} fontWeight="bold">
                      {dataLoading ? '...' : realTransactions.length}
                    </StatNumber>
                    <StatHelpText color="#02C076" fontSize={{ base: "xs", md: "sm" }}>
                      <Icon as={FaArrowUp} mr={1} />
                      {realTransactions.filter(tx => new Date(tx.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)).length} {t('transactions.stats.thisMonth', 'this month')}
                    </StatHelpText>
                  </Stat>

                  <Stat
                    bg="#1E2026"
                    p={{ base: 3, md: 4 }}
                    borderRadius="lg"
                    borderWidth="1px"
                    borderColor="#2B3139"
                    _hover={{ borderColor: "#F0B90B", transform: { base: "none", md: "translateY(-2px)" } }}
                    transition="all 0.3s ease"
                  >
                    <StatLabel color="#848E9C" fontSize={{ base: "xs", md: "sm" }}>
                      {t('transactions.stats.totalVolume', 'Total Volume')}
                    </StatLabel>
                    <StatNumber color="#EAECEF" fontSize={{ base: "xl", md: "2xl" }} fontWeight="bold">
                      {dataLoading ? '...' : realTransactions.length > 0 ?
                        `$${realTransactions.reduce((sum, tx) => sum + (tx.amount || 0), 0).toLocaleString()}` :
                        '$0'
                      }
                    </StatNumber>
                    <StatHelpText color="#02C076" fontSize={{ base: "xs", md: "sm" }}>
                      <Icon as={FaArrowUp} mr={1} />
                      {realTransactions.filter(tx => tx.type === 'deposit').length} {t('transactions.stats.incoming', 'incoming')}
                    </StatHelpText>
                  </Stat>

                  <Stat
                    bg="#1E2026"
                    p={{ base: 3, md: 4 }}
                    borderRadius="lg"
                    borderWidth="1px"
                    borderColor="#2B3139"
                    _hover={{ borderColor: "#F0B90B", transform: { base: "none", md: "translateY(-2px)" } }}
                    transition="all 0.3s ease"
                  >
                    <StatLabel color="#848E9C" fontSize={{ base: "xs", md: "sm" }}>
                      {t('transactions.stats.pendingTransactions', 'Pending')}
                    </StatLabel>
                    <StatNumber color="#EAECEF" fontSize={{ base: "xl", md: "2xl" }} fontWeight="bold">
                      {dataLoading ? '...' : realTransactions.filter(tx => tx.status === 'pending').length}
                    </StatNumber>
                    <StatHelpText color="#F0B90B" fontSize={{ base: "xs", md: "sm" }}>
                      <Icon as={FaSpinner} mr={1} />
                      {realTransactions.filter(tx => tx.status === 'pending').length} {t('transactions.stats.processing', 'processing')}
                    </StatHelpText>
                  </Stat>

                  <Stat
                    bg="#1E2026"
                    p={{ base: 3, md: 4 }}
                    borderRadius="lg"
                    borderWidth="1px"
                    borderColor="#2B3139"
                    _hover={{ borderColor: "#F0B90B", transform: { base: "none", md: "translateY(-2px)" } }}
                    transition="all 0.3s ease"
                  >
                    <StatLabel color="#848E9C" fontSize={{ base: "xs", md: "sm" }}>
                      {t('transactions.stats.activeAssets', 'Active Assets')}
                    </StatLabel>
                    <StatNumber color="#EAECEF" fontSize={{ base: "xl", md: "2xl" }} fontWeight="bold">
                      {dataLoading ? '...' : new Set(realTransactions.map(tx => tx.asset)).size}
                    </StatNumber>
                    <StatHelpText color="#848E9C" fontSize={{ base: "xs", md: "sm" }}>
                      {t('transactions.stats.cryptocurrencies', 'cryptocurrencies')}
                    </StatHelpText>
                  </Stat>
                </SimpleGrid>



                {/* Enhanced Filtering Controls */}
                <Box
                  bg="#1E2026"
                  p={{ base: 3, md: 4 }}
                  borderRadius="lg"
                  borderWidth="1px"
                  borderColor="#2B3139"
                  mb={6}
                >
                  <Flex
                    direction={{ base: "column", md: "row" }}
                    justify="space-between"
                    align={{ base: "stretch", md: "center" }}
                    mb={4}
                    gap={3}
                  >
                    <Heading size={{ base: "sm", md: "md" }} color="#F0B90B">
                      {t('transactions.filters.title', 'Filter & Search')}
                    </Heading>
                    <Button
                      size="sm"
                      variant="ghost"
                      color="#848E9C"
                      _hover={{ color: "#F0B90B" }}
                      onClick={clearFilters}
                      minH={{ base: "44px", md: "auto" }}
                      w={{ base: "100%", md: "auto" }}
                    >
                      {t('transactions.filters.clear', 'Clear All')}
                    </Button>
                  </Flex>

                  <SimpleGrid columns={{ base: 1, sm: 2, lg: 4 }} spacing={4} mb={4}>
                    {/* Search Input */}
                    <InputGroup>
                      <Input
                        placeholder={t('transactions.search.placeholder', 'Search transactions...')}
                        value={transactionFilters.searchQuery}
                        onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                        _hover={{ borderColor: "#F0B90B" }}
                        _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                        size="sm"
                        minH={{ base: "44px", md: "auto" }}
                        fontSize={{ base: "16px", md: "14px" }}
                      />
                      <InputRightElement>
                        <Icon as={FaSearch} color="#848E9C" />
                      </InputRightElement>
                    </InputGroup>

                    {/* Cryptocurrency Filter */}
                    <Select
                      value={transactionFilters.cryptocurrency}
                      onChange={(e) => handleFilterChange('cryptocurrency', e.target.value)}
                      bg="#0B0E11"
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                      _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                      size="sm"
                      minH={{ base: "44px", md: "auto" }}
                      fontSize={{ base: "16px", md: "14px" }}
                    >
                      <option value="all">{t('transactions.filters.allCrypto', 'All Cryptocurrencies')}</option>
                      {Object.keys(cryptocurrencyInfo).map(crypto => (
                        <option key={crypto} value={crypto}>{crypto}</option>
                      ))}
                    </Select>

                    {/* Type Filter */}
                    <Select
                      value={transactionFilters.type}
                      onChange={(e) => handleFilterChange('type', e.target.value)}
                      bg="#0B0E11"
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                      _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                      size="sm"
                      minH={{ base: "44px", md: "auto" }}
                      fontSize={{ base: "16px", md: "14px" }}
                    >
                      <option value="all">{t('transactions.filters.allTypes', 'All Types')}</option>
                      <option value="Deposit">{t('transactions.types.deposit', 'Deposit')}</option>
                      <option value="Withdrawal">{t('transactions.types.withdrawal', 'Withdrawal')}</option>
                      <option value="Investment">{t('transactions.types.investment', 'Investment')}</option>
                      <option value="Earnings">{t('transactions.types.earnings', 'Earnings')}</option>
                      <option value="Referral Commission">{t('transactions.types.commission', 'Commission')}</option>
                    </Select>

                    {/* Status Filter */}
                    <Select
                      value={transactionFilters.status}
                      onChange={(e) => handleFilterChange('status', e.target.value)}
                      bg="#0B0E11"
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                      _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                      size="sm"
                      minH={{ base: "44px", md: "auto" }}
                      fontSize={{ base: "16px", md: "14px" }}
                    >
                      <option value="all">{t('transactions.filters.allStatus', 'All Status')}</option>
                      <option value="Confirmed">{t('transactions.status.confirmed', 'Confirmed')}</option>
                      <option value="Processing">{t('transactions.status.processing', 'Processing')}</option>
                      <option value="Pending">{t('transactions.status.pending', 'Pending')}</option>
                      <option value="Failed">{t('transactions.status.failed', 'Failed')}</option>
                    </Select>
                  </SimpleGrid>
                </Box>

                {/* Enhanced Transaction Table */}
                <Box
                  bg="#1E2026"
                  p={6}
                  borderRadius="lg"
                  borderWidth="1px"
                  borderColor="#2B3139"
                  mb={6}
                >
                  <Flex
                    direction={{ base: "column", md: "row" }}
                    justify="space-between"
                    align={{ base: "stretch", md: "center" }}
                    mb={4}
                    gap={4}
                  >
                    <Box>
                      <Heading size="md" color="#F0B90B">
                        {t('transactions.table.title', 'Transaction List')}
                      </Heading>
                      <Text color="#848E9C" fontSize="sm">
                        ({getFilteredTransactions().length} {t('transactions.table.results', 'results')})
                      </Text>
                    </Box>
                    <HStack spacing={2} justify={{ base: "center", md: "flex-end" }}>
                      <Button
                        size="sm"
                        variant="outline"
                        borderColor="#F0B90B"
                        color="#F0B90B"
                        _hover={{ bg: "#F0B90B22" }}
                        leftIcon={<Icon as={FaFileExport} />}
                        minH="44px"
                      >
                        {t('transactions.export.button', 'Export')}
                      </Button>
                      <Select
                        size="sm"
                        w={{ base: "120px", md: "120px" }}
                        value={transactionPageSize}
                        onChange={(e) => setTransactionPageSize(Number(e.target.value))}
                        bg="#0B0E11"
                        borderColor="#2B3139"
                        color="#EAECEF"
                        minH="44px"
                      >
                        <option value={10}>10</option>
                        <option value={25}>25</option>
                        <option value={50}>50</option>
                        <option value={100}>100</option>
                      </Select>
                    </HStack>
                  </Flex>

                  <Box overflowX="auto" overflowY="hidden">
                    <Box as="table" w="100%" minW={{ base: "800px", lg: "100%" }} color="#EAECEF">
                      <Box as="thead" bg="#0B0E11">
                        <Box as="tr">
                          <Box as="th" textAlign="left" p={3}>
                            <HStack spacing={1}>
                              <Text fontSize="sm" fontWeight="bold">
                                {t('transactions.table.asset', 'Asset & Type')}
                              </Text>
                              <Icon as={FaSort} color="#848E9C" boxSize={3} cursor="pointer" />
                            </HStack>
                          </Box>
                          <Box as="th" textAlign="left" p={3}>
                            <HStack spacing={1}>
                              <Text fontSize="sm" fontWeight="bold">
                                {t('transactions.table.amount', 'Amount')}
                              </Text>
                              <Icon as={FaSort} color="#848E9C" boxSize={3} cursor="pointer" />
                            </HStack>
                          </Box>
                          <Box as="th" textAlign="left" p={3}>
                            <Text fontSize="sm" fontWeight="bold">
                              {t('transactions.table.status', 'Status')}
                            </Text>
                          </Box>
                          <Box as="th" textAlign="left" p={3}>
                            <HStack spacing={1}>
                              <Text fontSize="sm" fontWeight="bold">
                                {t('transactions.table.date', 'Date')}
                              </Text>
                              <Icon as={FaSort} color="#848E9C" boxSize={3} cursor="pointer" />
                            </HStack>
                          </Box>
                          <Box as="th" textAlign="left" p={3}>
                            <Text fontSize="sm" fontWeight="bold">
                              {t('transactions.table.network', 'Network')}
                            </Text>
                          </Box>
                          <Box as="th" textAlign="center" p={3}>
                            <Text fontSize="sm" fontWeight="bold">
                              {t('transactions.table.actions', 'Actions')}
                            </Text>
                          </Box>
                        </Box>
                      </Box>
                      <Box as="tbody">
                        {dataLoading ? (
                          <Box as="tr">
                            <Box as="td" colSpan={6} textAlign="center" p={8}>
                              <VStack spacing={3}>
                                <Spinner color="#F0B90B" size="lg" />
                                <Text color="#848E9C">
                                  {t('transactions.loading', 'Loading transactions...')}
                                </Text>
                              </VStack>
                            </Box>
                          </Box>
                        ) : getFilteredTransactions().length === 0 ? (
                          <Box as="tr">
                            <Box as="td" colSpan={6} textAlign="center" p={8}>
                              <VStack spacing={3}>
                                <Icon as={FaMoneyBillWave} color="#848E9C" boxSize={12} />
                                <Text color="#848E9C">
                                  {transactionFilters.searchQuery || transactionFilters.cryptocurrency !== 'all' || transactionFilters.type !== 'all' || transactionFilters.status !== 'all' ?
                                    t('transactions.noResults', 'No transactions match your filters') :
                                    t('transactions.noData', 'No transactions found')
                                  }
                                </Text>
                              </VStack>
                            </Box>
                          </Box>
                        ) : getFilteredTransactions().map((transaction, index) => (
                          <React.Fragment key={transaction.id}>
                            <Box as="tr" _hover={{ bg: "#2B3139" }} cursor="pointer" onClick={() => toggleRowExpansion(transaction.id)}>
                              {/* Asset & Type Column */}
                              <Box as="td" p={3}>
                                <HStack spacing={3}>
                                  <Box
                                    bg={getTransactionDirectionColor(transaction.direction) + '22'}
                                    p={2}
                                    borderRadius="md"
                                    position="relative"
                                  >
                                    <Icon
                                      as={getTransactionDirectionIcon(transaction.direction)}
                                      color={getTransactionDirectionColor(transaction.direction)}
                                      boxSize={4}
                                    />
                                    {/* Cryptocurrency Icon */}
                                    <Box
                                      position="absolute"
                                      top="-2px"
                                      right="-2px"
                                      bg={cryptocurrencyInfo[transaction.cryptocurrency]?.color || '#848E9C'}
                                      borderRadius="full"
                                      w="16px"
                                      h="16px"
                                      display="flex"
                                      alignItems="center"
                                      justifyContent="center"
                                      fontSize="8px"
                                      fontWeight="bold"
                                      color="white"
                                    >
                                      {cryptocurrencyInfo[transaction.cryptocurrency]?.icon || transaction.cryptocurrency.charAt(0)}
                                    </Box>
                                  </Box>
                                  <VStack align="start" spacing={0}>
                                    <HStack spacing={2}>
                                      <Text fontWeight="medium" fontSize="sm" color="#EAECEF">
                                        {transaction.type}
                                      </Text>
                                      <Icon
                                        as={expandedRows.has(transaction.id) ? FaCompress : FaExpand}
                                        color="#848E9C"
                                        boxSize={3}
                                      />
                                    </HStack>
                                    <Text color={cryptocurrencyInfo[transaction.cryptocurrency]?.color || '#848E9C'} fontSize="xs" fontWeight="bold">
                                      {cryptocurrencyInfo[transaction.cryptocurrency]?.name || transaction.cryptocurrency}
                                    </Text>
                                  </VStack>
                                </HStack>
                              </Box>

                              {/* Amount Column */}
                              <Box as="td" p={3}>
                                <VStack align="start" spacing={1}>
                                  <Text fontWeight="bold" fontSize="sm" color="#EAECEF">
                                    {formatCryptocurrencyAmount(transaction.amount, transaction.cryptocurrency)}
                                  </Text>
                                  {transaction.usdValue && (
                                    <Text color="#848E9C" fontSize="xs">
                                      {formatUSDValue(transaction.usdValue)}
                                    </Text>
                                  )}
                                  {transaction.fee > 0 && (
                                    <Text color="#F84960" fontSize="xs">
                                      Fee: {formatCryptocurrencyAmount(transaction.fee, transaction.cryptocurrency)}
                                    </Text>
                                  )}
                                </VStack>
                              </Box>
                              {/* Status Column */}
                              <Box as="td" p={3}>
                                <VStack align="start" spacing={1}>
                                  <Badge
                                    bg={getStatusColor(transaction.status) + '22'}
                                    color={getStatusColor(transaction.status)}
                                    px={2}
                                    py={1}
                                    borderRadius="full"
                                    fontSize="xs"
                                    fontWeight="bold"
                                  >
                                    {transaction.status}
                                  </Badge>
                                  {(transaction.status === 'Processing' || transaction.status === 'Pending') && (
                                    <VStack spacing={1} w="100%">
                                      <Progress
                                        value={getConfirmationProgress(transaction.confirmations, transaction.requiredConfirmations)}
                                        size="sm"
                                        colorScheme="yellow"
                                        bg="#2B3139"
                                        borderRadius="full"
                                        w="80px"
                                      />
                                      <Text color="#848E9C" fontSize="xs">
                                        {transaction.confirmations}/{transaction.requiredConfirmations} confirmations
                                      </Text>
                                    </VStack>
                                  )}
                                </VStack>
                              </Box>

                              {/* Date Column */}
                              <Box as="td" p={3}>
                                <VStack align="start" spacing={0}>
                                  <Text fontSize="sm" color="#EAECEF">
                                    {new Date(transaction.date).toLocaleDateString()}
                                  </Text>
                                  <Text color="#848E9C" fontSize="xs">
                                    {new Date(transaction.date).toLocaleTimeString()}
                                  </Text>
                                </VStack>
                              </Box>

                              {/* Network Column */}
                              <Box as="td" p={3}>
                                <VStack align="start" spacing={0}>
                                  <Text fontSize="sm" color="#EAECEF">
                                    {transaction.network}
                                  </Text>
                                  {transaction.blockNumber && (
                                    <Text color="#848E9C" fontSize="xs">
                                      Block: {transaction.blockNumber}
                                    </Text>
                                  )}
                                </VStack>
                              </Box>

                              {/* Actions Column */}
                              <Box as="td" p={3} textAlign="center">
                                <HStack spacing={1} justify="center">
                                  <Button
                                    size="xs"
                                    variant="ghost"
                                    color="#F0B90B"
                                    _hover={{ bg: "#F0B90B22" }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setSelectedTransaction(transaction);
                                    }}
                                  >
                                    <Icon as={FaEye} />
                                  </Button>
                                  <Button
                                    size="xs"
                                    variant="ghost"
                                    color="#848E9C"
                                    _hover={{ bg: "#2B3139" }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleCopyToClipboard(transaction.txHash);
                                      toast({
                                        title: t('common.copied', 'Transaction hash copied!'),
                                        status: "success",
                                        duration: 2000,
                                        isClosable: true,
                                      });
                                    }}
                                  >
                                    <Icon as={FaCopy} />
                                  </Button>
                                  <Button
                                    size="xs"
                                    variant="ghost"
                                    color="#848E9C"
                                    _hover={{ bg: "#2B3139" }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      window.open(`https://etherscan.io/tx/${transaction.txHash}`, '_blank');
                                    }}
                                  >
                                    <Icon as={FaExternalLinkAlt} />
                                  </Button>
                                </HStack>
                              </Box>
                            </Box>

                            {/* Expandable Row Details */}
                            {expandedRows.has(transaction.id) && (
                              <Box as="tr" bg="#0B0E11">
                                <Box as="td" colSpan={6} p={4}>
                                  <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
                                    <VStack align="start" spacing={2}>
                                      <Text color="#F0B90B" fontSize="sm" fontWeight="bold">
                                        {t('transactions.details.addresses', 'Addresses')}
                                      </Text>
                                      <Box>
                                        <Text color="#848E9C" fontSize="xs">From:</Text>
                                        <Text color="#EAECEF" fontSize="xs" fontFamily="mono">
                                          {transaction.fromAddress}
                                        </Text>
                                      </Box>
                                      <Box>
                                        <Text color="#848E9C" fontSize="xs">To:</Text>
                                        <Text color="#EAECEF" fontSize="xs" fontFamily="mono">
                                          {transaction.toAddress}
                                        </Text>
                                      </Box>
                                    </VStack>

                                    <VStack align="start" spacing={2}>
                                      <Text color="#F0B90B" fontSize="sm" fontWeight="bold">
                                        {t('transactions.details.technical', 'Technical Details')}
                                      </Text>
                                      <Box>
                                        <Text color="#848E9C" fontSize="xs">Transaction Hash:</Text>
                                        <Text color="#EAECEF" fontSize="xs" fontFamily="mono">
                                          {transaction.txHash}
                                        </Text>
                                      </Box>
                                      {transaction.gasPrice && (
                                        <Box>
                                          <Text color="#848E9C" fontSize="xs">Gas Price:</Text>
                                          <Text color="#EAECEF" fontSize="xs">
                                            {transaction.gasPrice} Gwei
                                          </Text>
                                        </Box>
                                      )}
                                    </VStack>

                                    <VStack align="start" spacing={2}>
                                      <Text color="#F0B90B" fontSize="sm" fontWeight="bold">
                                        {t('transactions.details.description', 'Description')}
                                      </Text>
                                      <Text color="#EAECEF" fontSize="sm">
                                        {transaction.description}
                                      </Text>
                                    </VStack>
                                  </SimpleGrid>
                                </Box>
                              </Box>
                            )}
                          </React.Fragment>
                        ))}
                      </Box>
                    </Box>
                  </Box>

                  {/* Enhanced Pagination */}
                  <Flex justify="space-between" align="center" mt={4}>
                    <VStack align="start" spacing={1}>
                      <Text color="#848E9C" fontSize="sm">
                        {t('transactions.pagination.showing', 'Showing')} 1-{Math.min(transactionPageSize, getFilteredTransactions().length)} {t('transactions.pagination.of', 'of')} {getFilteredTransactions().length} {t('transactions.pagination.transactions', 'transactions')}
                      </Text>
                      {(transactionFilters.searchQuery || transactionFilters.cryptocurrency !== 'all' || transactionFilters.type !== 'all' || transactionFilters.status !== 'all') && (
                        <Text color="#F0B90B" fontSize="xs">
                          {t('transactions.pagination.filtered', 'Filtered from')} {enhancedTransactions.length} {t('transactions.pagination.total', 'total')}
                        </Text>
                      )}
                    </VStack>
                    <HStack spacing={2}>
                      <Button
                        size="sm"
                        variant="ghost"
                        color="#848E9C"
                        _hover={{ color: "#F0B90B" }}
                        isDisabled={currentTransactionPage === 1}
                        leftIcon={<Icon as={FaArrowLeft} />}
                        onClick={() => setCurrentTransactionPage(prev => Math.max(1, prev - 1))}
                      >
                        {t('transactions.pagination.previous', 'Previous')}
                      </Button>
                      <Text color="#EAECEF" fontSize="sm">
                        {currentTransactionPage} / {Math.ceil(getFilteredTransactions().length / transactionPageSize) || 1}
                      </Text>
                      <Button
                        size="sm"
                        variant="ghost"
                        color="#848E9C"
                        _hover={{ color: "#F0B90B" }}
                        isDisabled={currentTransactionPage >= Math.ceil(getFilteredTransactions().length / transactionPageSize)}
                        rightIcon={<Icon as={FaArrowRight} />}
                        onClick={() => setCurrentTransactionPage(prev => Math.min(Math.ceil(getFilteredTransactions().length / transactionPageSize) || 1, prev + 1))}
                      >
                        {t('transactions.pagination.next', 'Next')}
                      </Button>
                    </HStack>
                  </Flex>
                </Box>
              </Box>
            )}

            {/* User Investments */}
            {/* {activeTab === 13 && (
              <Box>
                <UserInvestments />
              </Box>
            )} */}
            {/* KYC Verification */}
            {activeTab === 6 && (
              <Box>
                <KYCVerification
                  kycStatus={user?.kycVerified ? 'verified' : 'not_started'}
                  onSubmit={() => {
                    toast({
                      title: t('kyc.success.submitted', 'KYC Verification Submitted'),
                      description: t('kyc.success.underReview', 'Your verification documents are under review. This process may take 1-3 business days.'),
                      status: 'success',
                      duration: 5000,
                      isClosable: true,
                    });
                  }}
                />
              </Box>
            )}

            {/* Enhanced Referrals Section */}
            {activeTab === 5 && (
              <Box>
                {/* Header */}
                <Box mb={6}>
                  <Heading size="lg" color="#F0B90B" mb={2}>
                    {t('referral.title', 'Referral Program')}
                  </Heading>
                  <Text color="#848E9C" fontSize="md">
                    {t('referral.description', 'Invite friends and earn commission on their investments. Build your passive income stream today!')}
                  </Text>
                </Box>

                {/* Statistics Dashboard */}
                <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4} mb={6}>
                  <Stat
                    bg="#1E2026"
                    p={4}
                    borderRadius="lg"
                    borderWidth="1px"
                    borderColor="#2B3139"
                    _hover={{ borderColor: "#F0B90B" }}
                    transition="all 0.3s ease"
                  >
                    <StatLabel color="#848E9C" fontSize="sm">
                      {t('referral.stats.totalReferrals', 'Total Referrals')}
                    </StatLabel>
                    <StatNumber color="#EAECEF" fontSize="2xl" fontWeight="bold">
                      {referralLoading ? '...' : (referralStats?.totalReferrals || 0)}
                    </StatNumber>
                    <StatHelpText color="#02C076" fontSize="sm">
                      <Icon as={FaArrowUp} mr={1} />
                      {referralStats?.currentTier || 'Bronze'} {t('referral.stats.tier', 'tier')}
                    </StatHelpText>
                  </Stat>

                  <Stat
                    bg="#1E2026"
                    p={4}
                    borderRadius="lg"
                    borderWidth="1px"
                    borderColor="#2B3139"
                    _hover={{ borderColor: "#F0B90B" }}
                    transition="all 0.3s ease"
                  >
                    <StatLabel color="#848E9C" fontSize="sm">
                      {t('referral.stats.activeReferrals', 'Active Referrals')}
                    </StatLabel>
                    <StatNumber color="#EAECEF" fontSize="2xl" fontWeight="bold">
                      {referralLoading ? '...' : (referralStats?.activeReferrals || 0)}
                    </StatNumber>
                    <StatHelpText color="#02C076" fontSize="sm">
                      <Icon as={FaArrowUp} mr={1} />
                      {referralStats?.commissionRate || 3}% {t('referral.stats.commissionRate', 'commission rate')}
                    </StatHelpText>
                  </Stat>

                  <Stat
                    bg="#1E2026"
                    p={4}
                    borderRadius="lg"
                    borderWidth="1px"
                    borderColor="#2B3139"
                    _hover={{ borderColor: "#F0B90B" }}
                    transition="all 0.3s ease"
                  >
                    <StatLabel color="#848E9C" fontSize="sm">
                      {t('referral.stats.totalEarnings', 'Total Earnings')}
                    </StatLabel>
                    <StatNumber color="#EAECEF" fontSize="2xl" fontWeight="bold">
                      ${referralLoading ? '...' : (referralStats?.totalCommissionEarned || 0).toFixed(2)}
                    </StatNumber>
                    <StatHelpText color="#02C076" fontSize="sm">
                      <Icon as={FaArrowUp} mr={1} />
                      ${referralStats?.availableForWithdrawal || 0} {t('referral.stats.available', 'available')}
                    </StatHelpText>
                  </Stat>

                  <Stat
                    bg="#1E2026"
                    p={4}
                    borderRadius="lg"
                    borderWidth="1px"
                    borderColor="#2B3139"
                    _hover={{ borderColor: "#F0B90B" }}
                    transition="all 0.3s ease"
                  >
                    <StatLabel color="#848E9C" fontSize="sm">
                      {t('referral.stats.currentTier', 'Current Tier')}
                    </StatLabel>
                    <StatNumber color="#F0B90B" fontSize="xl" fontWeight="bold">
                      {referralLoading ? '...' : (referralStats?.currentTier || 'Bronze')}
                    </StatNumber>
                    <StatHelpText color="#F0B90B" fontSize="sm">
                      {referralStats?.commissionRate || 3}% {t('referral.stats.commission', 'commission')}
                      {referralStats?.nextTierRequirement && (
                        <Text fontSize="xs" color="#848E9C">
                          {referralStats.nextTierRequirement - (referralStats.totalReferrals || 0)} more to next tier
                        </Text>
                      )}
                    </StatHelpText>
                  </Stat>
                </SimpleGrid>

                {/* Commission Structure */}
                <Box
                  bg="#1E2026"
                  p={6}
                  borderRadius="lg"
                  borderWidth="1px"
                  borderColor="#2B3139"
                  mb={6}
                >
                  <Heading size="md" color="#F0B90B" mb={4}>
                    {t('referral.commissionStructure', 'Commission Structure')}
                  </Heading>

                  <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4} mb={6}>
                    {[
                      { tier: 'Bronze', min: 0, max: 9, rate: '3%', color: '#CD7F32' },
                      { tier: 'Silver', min: 10, max: 24, rate: '7%', color: '#C0C0C0' },
                      { tier: 'Gold', min: 25, max: 49, rate: '10%', color: '#FFD700' },
                      { tier: 'Platinum', min: 50, max: null, rate: '15%', color: '#E5E4E2' }
                    ].map((tier, index) => {
                      const isActive = (referralStats?.totalReferrals || 0) >= tier.min && (tier.max === null || (referralStats?.totalReferrals || 0) <= tier.max);
                      return (
                        <Box
                          key={tier.tier}
                          bg={isActive ? "rgba(240, 185, 11, 0.1)" : "#0B0E11"}
                          p={4}
                          borderRadius="md"
                          borderWidth="2px"
                          borderColor={isActive ? "#F0B90B" : "#2B3139"}
                          textAlign="center"
                          transition="all 0.3s ease"
                        >
                          <Text color={tier.color} fontWeight="bold" fontSize="lg" mb={2}>
                            {tier.tier}
                          </Text>
                          <Text color="#EAECEF" fontSize="sm" mb={1}>
                            {tier.min}-{tier.max || '∞'} {t('referral.referrals', 'referrals')}
                          </Text>
                          <Text color="#F0B90B" fontWeight="bold" fontSize="xl">
                            {tier.rate}
                          </Text>
                          {isActive && (
                            <Badge bg="#02C076" color="white" mt={2}>
                              {t('referral.current', 'Current')}
                            </Badge>
                          )}
                        </Box>
                      );
                    })}
                  </SimpleGrid>

                  <Progress
                    value={Math.min(((referralStats?.totalReferrals || 0) / 50) * 100, 100)}
                    size="lg"
                    colorScheme="yellow"
                    bg="#2B3139"
                    borderRadius="full"
                    mb={2}
                  />
                  <Text color="#848E9C" fontSize="sm" textAlign="center">
                    {referralStats?.totalReferrals || 0}/50 {t('referral.progressToPlat', 'referrals to reach Platinum tier')}
                  </Text>
                </Box>

                {/* Referral Link Management */}
                <Box
                  bg="#1E2026"
                  p={6}
                  borderRadius="lg"
                  borderWidth="1px"
                  borderColor="#2B3139"
                  mb={6}
                >
                  <Heading size="md" color="#F0B90B" mb={4}>
                    {t('common:referral.linkManagement', 'Referral Link Management')}
                  </Heading>

                  <VStack spacing={4} align="stretch">
                    {referralStats?.referralCode ? (
                      <>
                        <FormControl>
                          <FormLabel color="#848E9C" fontSize="sm">
                            {t('common:referral.referralLink', 'Your Referral Link')}
                          </FormLabel>
                          <InputGroup>
                            <Input
                              value={referralStats.referralLink}
                              isReadOnly
                              bg="#0B0E11"
                              borderColor="#2B3139"
                              color="#EAECEF"
                              _hover={{ borderColor: "#F0B90B" }}
                              fontSize="sm"
                            />
                            <InputRightElement>
                              <Button
                                size="sm"
                                variant="ghost"
                                color="#F0B90B"
                                _hover={{ bg: "#F0B90B22" }}
                                onClick={() => {
                                  handleCopyToClipboard(referralStats.referralLink);
                                  toast({
                                    title: t('common.copied', 'Copied to clipboard!'),
                                    status: "success",
                                    duration: 2000,
                                    isClosable: true,
                                  });
                                }}
                              >
                                <Icon as={FaCopy} />
                              </Button>
                            </InputRightElement>
                          </InputGroup>
                        </FormControl>

                        <FormControl>
                          <FormLabel color="#848E9C" fontSize="sm">
                            {t('common:referral.referralCode', 'Your Referral Code')}
                          </FormLabel>
                          <InputGroup>
                            <Input
                              value={referralStats.referralCode}
                              isReadOnly
                              bg="#0B0E11"
                              borderColor="#2B3139"
                              color="#EAECEF"
                              _hover={{ borderColor: "#F0B90B" }}
                              fontSize="sm"
                            />
                            <InputRightElement>
                              <Button
                                size="sm"
                                variant="ghost"
                                color="#F0B90B"
                                _hover={{ bg: "#F0B90B22" }}
                                onClick={() => {
                                  handleCopyToClipboard(referralStats.referralCode);
                                  toast({
                                    title: t('common.copied', 'Copied to clipboard!'),
                                    status: "success",
                                    duration: 2000,
                                    isClosable: true,
                                  });
                                }}
                              >
                                <Icon as={FaCopy} />
                              </Button>
                            </InputRightElement>
                          </InputGroup>
                        </FormControl>

                        {/* Regenerate Code Option */}
                        <Box bg="#0B0E11" p={4} borderRadius="md" mt={4}>
                          <Flex justify="space-between" align="center">
                            <Box>
                              <Text color="#EAECEF" fontWeight="medium" fontSize="sm">
                                {t('referral.regenerateCode', 'Regenerate Referral Code')}
                              </Text>
                              <Text color="#848E9C" fontSize="xs">
                                {t('referral.regenerateCodeDesc', 'Generate a new referral code if needed')}
                              </Text>
                            </Box>
                            <Button
                              size="sm"
                              variant="outline"
                              borderColor="#F0B90B"
                              color="#F0B90B"
                              _hover={{ bg: "#F0B90B22" }}
                              onClick={generateReferralCode}
                              isLoading={referralLoading}
                              loadingText={t('common:referral.generating', 'Generating...')}
                            >
                              {t('referral.regenerate', 'Regenerate')}
                            </Button>
                          </Flex>
                        </Box>
                      </>
                    ) : (
                      <Box textAlign="center" py={8}>
                        <Icon as={FaUserPlus} boxSize={12} color="#848E9C" mb={4} />
                        <Text color="#EAECEF" fontSize="lg" fontWeight="600" mb={2}>
                          {t('common:referral.noCodeYet', 'No Referral Code Yet')}
                        </Text>
                        <Text color="#848E9C" fontSize="sm" mb={6}>
                          {t('common:referral.generateCodeDesc', 'Generate your unique referral code to start earning commissions')}
                        </Text>
                        <Button
                          onClick={generateReferralCode}
                          isLoading={referralLoading}
                          loadingText={t('common:referral.generating', 'Generating...')}
                          bg="linear-gradient(135deg, #FCD535 0%, #F0B90B 100%)"
                          color="#0B0E11"
                          _hover={{
                            bg: "linear-gradient(135deg, #F0B90B 0%, #FCD535 100%)",
                            transform: "translateY(-2px)",
                            boxShadow: "0 8px 25px rgba(240, 185, 11, 0.4)"
                          }}
                          _active={{
                            transform: "translateY(0px)"
                          }}
                          borderRadius="lg"
                          fontWeight="600"
                          px={8}
                          py={6}
                          leftIcon={<Icon as={FaPlus} />}
                        >
                          {t('common:referral.generateCode', 'Generate Referral Code')}
                        </Button>
                      </Box>
                    )}
                  </VStack>
                </Box>

                {/* Referral Performance Summary */}
                <Box
                  bg="#1E2026"
                  p={6}
                  borderRadius="lg"
                  borderWidth="1px"
                  borderColor="#2B3139"
                  mb={6}
                >
                  <Heading size="md" color="#F0B90B" mb={4}>
                    {t('referral.performanceSummary', 'Performance Summary')}
                  </Heading>

                  <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
                    <Box bg="#0B0E11" p={4} borderRadius="md" textAlign="center">
                      <Text color="#848E9C" fontSize="sm" mb={1}>
                        {t('referral.thisMonth', 'This Month')}
                      </Text>
                      <Text color="#02C076" fontSize="2xl" fontWeight="bold">
                        {referralStats?.totalReferrals || 0}
                      </Text>
                      <Text color="#848E9C" fontSize="xs">
                        {t('referral.newReferrals', 'New Referrals')}
                      </Text>
                    </Box>

                    <Box bg="#0B0E11" p={4} borderRadius="md" textAlign="center">
                      <Text color="#848E9C" fontSize="sm" mb={1}>
                        {t('referral.conversionRate', 'Conversion Rate')}
                      </Text>
                      <Text color="#F0B90B" fontSize="2xl" fontWeight="bold">
                        {referralStats?.commissionRate?.toFixed(1) || '3.0'}%
                      </Text>
                      <Text color="#848E9C" fontSize="xs">
                        {t('referral.activeInvestors', 'Active Investors')}
                      </Text>
                    </Box>

                    <Box bg="#0B0E11" p={4} borderRadius="md" textAlign="center">
                      <Text color="#848E9C" fontSize="sm" mb={1}>
                        {t('referral.avgCommission', 'Avg Commission')}
                      </Text>
                      <Text color="#02C076" fontSize="2xl" fontWeight="bold">
                        ${(referralStats?.totalCommissionEarned && referralStats?.totalReferrals
                          ? (referralStats.totalCommissionEarned / referralStats.totalReferrals).toFixed(2)
                          : '0.00')}
                      </Text>
                      <Text color="#848E9C" fontSize="xs">
                        {t('referral.perReferral', 'Per Referral')}
                      </Text>
                    </Box>
                  </SimpleGrid>
                </Box>

                {/* Referral History Table */}
                <Box
                  bg="#1E2026"
                  p={6}
                  borderRadius="lg"
                  borderWidth="1px"
                  borderColor="#2B3139"
                  mb={6}
                >
                  <Heading size="md" color="#F0B90B" mb={4}>
                    {t('referral.history', 'Referral History')}
                  </Heading>

                  <Box overflowX="auto">
                    <Box as="table" w="100%" color="#EAECEF">
                      <Box as="thead" bg="#0B0E11">
                        <Box as="tr">
                          <Box as="th" textAlign="left" p={3}>{t('referral.table.date', 'Date')}</Box>
                          <Box as="th" textAlign="left" p={3}>{t('referral.table.user', 'Referred User')}</Box>
                          <Box as="th" textAlign="left" p={3}>{t('referral.table.status', 'Status')}</Box>
                          <Box as="th" textAlign="left" p={3}>{t('referral.table.investment', 'Investment')}</Box>
                          <Box as="th" textAlign="left" p={3}>{t('referral.table.commission', 'Commission')}</Box>
                        </Box>
                      </Box>
                      <Box as="tbody">
                        {referralLoading ? (
                          <Box as="tr">
                            <Box as="td" colSpan={5} p={8} textAlign="center">
                              <Spinner color="#F0B90B" size="lg" />
                              <Text color="#848E9C" mt={2}>
                                {t('referral.loading', 'Loading referral history...')}
                              </Text>
                            </Box>
                          </Box>
                        ) : referralError ? (
                          <Box as="tr">
                            <Box as="td" colSpan={5} p={8} textAlign="center">
                              <Text color="#F84960" mb={2}>
                                {t('referral.error', 'Failed to load referral history')}
                              </Text>
                              <Button
                                size="sm"
                                variant="outline"
                                colorScheme="yellow"
                                onClick={fetchReferralData}
                              >
                                {t('common.retry', 'Retry')}
                              </Button>
                            </Box>
                          </Box>
                        ) : referralHistory.length === 0 ? (
                          <Box as="tr">
                            <Box as="td" colSpan={5} p={8} textAlign="center">
                              <Icon as={FaUserPlus} boxSize={8} color="#848E9C" mb={2} />
                              <Text color="#848E9C">
                                {t('referral.noHistory', 'No referral history yet')}
                              </Text>
                              <Text color="#848E9C" fontSize="sm">
                                {t('referral.noHistoryDesc', 'Start referring friends to see your commission history here')}
                              </Text>
                            </Box>
                          </Box>
                        ) : (
                          referralHistory.map((referral) => (
                            <Box as="tr" key={referral._id} _hover={{ bg: "#2B3139" }}>
                              <Box as="td" p={3}>
                                {new Date(referral.registrationDate).toLocaleDateString()}
                              </Box>
                              <Box as="td" p={3}>{referral.referredUser.email}</Box>
                              <Box as="td" p={3}>
                                <Badge
                                  bg={
                                    referral.commissionStatus === 'approved' || referral.commissionStatus === 'paid'
                                      ? "#02C076"
                                      : referral.commissionStatus === 'pending'
                                      ? "#F0B90B"
                                      : "#F84960"
                                  }
                                  color="white"
                                  px={2}
                                  py={1}
                                  borderRadius="full"
                                  fontSize="xs"
                                >
                                  {referral.commissionStatus}
                                </Badge>
                              </Box>
                              <Box as="td" p={3}>
                                ${referral.investmentAmount.toFixed(2)} {referral.currency}
                              </Box>
                              <Box as="td" p={3} color="#02C076" fontWeight="bold">
                                ${referral.commissionAmount.toFixed(2)}
                              </Box>
                            </Box>
                          ))
                        )}
                      </Box>
                    </Box>
                  </Box>

                  {/* Pagination */}
                  {referralHistory.length > 0 && referralPagination.pages > 1 && (
                    <Flex justify="center" mt={4}>
                      <HStack spacing={2}>
                        <Button
                          size="sm"
                          variant="outline"
                          borderColor="#2B3139"
                          color="#EAECEF"
                          _hover={{ borderColor: "#F0B90B", color: "#F0B90B" }}
                          isDisabled={referralPagination.page === 1}
                          onClick={() => {
                            setReferralPagination(prev => ({ ...prev, page: prev.page - 1 }));
                            fetchReferralData();
                          }}
                        >
                          {t('common.previous', 'Previous')}
                        </Button>

                        <Text color="#848E9C" fontSize="sm" px={4}>
                          {t('common.pageOf', 'Page {{current}} of {{total}}', {
                            current: referralPagination.page,
                            total: referralPagination.pages
                          })}
                        </Text>

                        <Button
                          size="sm"
                          variant="outline"
                          borderColor="#2B3139"
                          color="#EAECEF"
                          _hover={{ borderColor: "#F0B90B", color: "#F0B90B" }}
                          isDisabled={referralPagination.page === referralPagination.pages}
                          onClick={() => {
                            setReferralPagination(prev => ({ ...prev, page: prev.page + 1 }));
                            fetchReferralData();
                          }}
                        >
                          {t('common.next', 'Next')}
                        </Button>
                      </HStack>
                    </Flex>
                  )}
                </Box>

                {/* Commission Withdrawal Section */}
                <Box
                  bg="#1E2026"
                  p={6}
                  borderRadius="lg"
                  borderWidth="1px"
                  borderColor="#2B3139"
                  mb={6}
                >
                  <Heading size="md" color="#F0B90B" mb={4}>
                    {t('referral.commissionWithdrawal', 'Commission Withdrawal')}
                  </Heading>

                  <VStack spacing={4} align="stretch">
                    <Box bg="#0B0E11" p={4} borderRadius="md">
                      <Flex justify="space-between" align="center" mb={3}>
                        <Text color="#EAECEF" fontWeight="medium">
                          {t('referral.availableCommission', 'Available Commission')}
                        </Text>
                        <Text color="#02C076" fontSize="xl" fontWeight="bold">
                          ${referralStats?.availableForWithdrawal?.toFixed(2) || '0.00'}
                        </Text>
                      </Flex>

                      <Text color="#848E9C" fontSize="sm" mb={4}>
                        {t('referral.minimumWithdrawal', 'Minimum withdrawal amount: $50.00')}
                      </Text>

                      <Button
                        bg="#F0B90B"
                        color="#0B0E11"
                        _hover={{ bg: "#F8D12F" }}
                        isDisabled={(referralStats?.availableForWithdrawal || 0) < 50}
                        onClick={() => {
                          setSelectedCrypto('USDT');
                          setInitialWithdrawalType('commission');
                          onWithdrawOpen();
                        }}
                        w="100%"
                        leftIcon={<Icon as={FaMoneyBillWave} />}
                      >
                        {t('referral.withdrawCommission', 'Withdraw Commission')}
                      </Button>
                    </Box>

                    <Alert status="info" borderRadius="md" bg="#0ECB8122">
                      <AlertIcon color="#0ECB81" />
                      <Box>
                        <AlertTitle color="#0ECB81" fontSize="sm">
                          {t('referral.withdrawalInfo', 'Withdrawal Information')}
                        </AlertTitle>
                        <AlertDescription color="#EAECEF" fontSize="xs">
                          {t('referral.withdrawalInfoDesc', 'Commission withdrawals are processed within 24 hours. Minimum withdrawal amount is $50 USDT equivalent.')}
                        </AlertDescription>
                      </Box>
                    </Alert>
                  </VStack>
                </Box>
              </Box>
            )}

            {/* Analytics Dashboard - Temporarily Disabled */}
            {activeTab === 10 && (
              <Box bg="#1E2026" p={6} borderRadius="lg" borderWidth="1px" borderColor="#2B3139">
                <Alert status="info" bg="#0ECB8122" borderColor="#0ECB81">
                  <AlertIcon color="#0ECB81" />
                  <VStack align="start" spacing={2}>
                    <Text color="#EAECEF" fontWeight="bold">
                      {t('analytics.maintenance.title', 'Analytics Dashboard Under Maintenance')}
                    </Text>
                    <Text color="#848E9C" fontSize="sm">
                      {t('analytics.maintenance.description', 'Our analytics dashboard is currently being updated with enhanced features. Please check back soon.')}
                    </Text>
                  </VStack>
                </Alert>
              </Box>
            )}





            {/* Investment System - Connected to Unified Transaction System */}
            {activeTab === 13 && (
              <InvestmentTab
                onDepositOpen={onDepositOpen}
              />
            )}

            {/* Settings Tab */}
            {activeTab === 7 && (
              <Box>
                <Box mb={6}>
                  <Heading size="md" color="#EAECEF" mb={2}>{t('common.settings', 'Settings')}</Heading>
                  <Text color="#848E9C" fontSize="sm">
                    {t('settings.description', 'Manage your account preferences and application settings.')}
                  </Text>
                </Box>

                <VStack spacing={8} align="stretch">
                  {/* Account Preferences */}
                  <Box
                    bg="#1E2329"
                    p={{ base: 4, md: 6 }}
                    borderRadius="xl"
                    borderWidth="1px"
                    borderColor="#2B3139"
                    boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
                  >
                    <Heading size={{ base: "xs", md: "sm" }} color="#F0B90B" mb={4} display="flex" alignItems="center">
                      <Icon as={FaUserCog} mr={2} />
                      {t('settings.accountPreferences', 'Account Preferences')}
                    </Heading>

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                      <FormControl>
                        <FormLabel color="#848E9C" fontSize="sm">
                          {t('settings.language', 'Language')}
                        </FormLabel>
                        <Select
                          bg="#0B0E11"
                          borderColor="#2B3139"
                          color="#EAECEF"
                          _hover={{ borderColor: "#F0B90B" }}
                          _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                          minH={{ base: "44px", md: "auto" }}
                          fontSize={{ base: "16px", md: "14px" }}
                        >
                          <option value="en">English</option>
                          <option value="de">Deutsch</option>
                          <option value="fr">Français</option>
                        </Select>
                        <Text color="#848E9C" fontSize="xs" mt={1}>
                          {t('settings.languageDesc', 'Choose your preferred language for the interface')}
                        </Text>
                      </FormControl>

                      <FormControl>
                        <FormLabel color="#848E9C" fontSize="sm">
                          {t('settings.timezone', 'Timezone')}
                        </FormLabel>
                        <Select
                          bg="#0B0E11"
                          borderColor="#2B3139"
                          color="#EAECEF"
                          _hover={{ borderColor: "#F0B90B" }}
                          _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                          minH={{ base: "44px", md: "auto" }}
                          fontSize={{ base: "16px", md: "14px" }}
                        >
                          <option value="UTC">UTC</option>
                          <option value="Europe/Istanbul">Europe/Istanbul (UTC+3)</option>
                          <option value="Europe/London">Europe/London (UTC+0)</option>
                          <option value="America/New_York">America/New_York (UTC-5)</option>
                        </Select>
                        <Text color="#848E9C" fontSize="xs" mt={1}>
                          {t('settings.timezoneDesc', 'Set your local timezone for accurate timestamps')}
                        </Text>
                      </FormControl>

                      <FormControl>
                        <FormLabel color="#848E9C" fontSize="sm">
                          {t('settings.currency', 'Display Currency')}
                        </FormLabel>
                        <Select
                          bg="#0B0E11"
                          borderColor="#2B3139"
                          color="#EAECEF"
                          _hover={{ borderColor: "#F0B90B" }}
                          _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                          minH={{ base: "44px", md: "auto" }}
                          fontSize={{ base: "16px", md: "14px" }}
                        >
                          <option value="USD">USD ($)</option>
                          <option value="EUR">EUR (€)</option>
                          <option value="TRY">TRY (₺)</option>
                          <option value="GBP">GBP (£)</option>
                        </Select>
                        <Text color="#848E9C" fontSize="xs" mt={1}>
                          {t('settings.currencyDesc', 'Choose your preferred currency for value display')}
                        </Text>
                      </FormControl>

                      <FormControl>
                        <FormLabel color="#848E9C" fontSize="sm">
                          {t('settings.theme', 'Theme')}
                        </FormLabel>
                        <Select
                          bg="#0B0E11"
                          borderColor="#2B3139"
                          color="#EAECEF"
                          _hover={{ borderColor: "#F0B90B" }}
                          _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                          minH={{ base: "44px", md: "auto" }}
                          fontSize={{ base: "16px", md: "14px" }}
                        >
                          <option value="dark">Dark Theme</option>
                          <option value="light">Light Theme</option>
                          <option value="auto">Auto (System)</option>
                        </Select>
                        <Text color="#848E9C" fontSize="xs" mt={1}>
                          {t('settings.themeDesc', 'Choose your preferred color theme')}
                        </Text>
                      </FormControl>
                    </SimpleGrid>
                  </Box>

                  {/* Notification Settings */}
                  <Box
                    bg="#1E2329"
                    p={{ base: 4, md: 6 }}
                    borderRadius="xl"
                    borderWidth="1px"
                    borderColor="#2B3139"
                    boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
                  >
                    <Heading size={{ base: "xs", md: "sm" }} color="#F0B90B" mb={4} display="flex" alignItems="center">
                      <Icon as={FaBell} mr={2} />
                      {t('settings.notifications', 'Notification Settings')}
                    </Heading>

                    <VStack spacing={4} align="stretch">
                      <Box bg="#0B0E11" p={4} borderRadius="md">
                        <Flex
                          direction={{ base: "column", md: "row" }}
                          justify="space-between"
                          align={{ base: "stretch", md: "center" }}
                          gap={3}
                        >
                          <Box>
                            <Text color="#EAECEF" fontWeight="medium" fontSize={{ base: "md", md: "sm" }}>
                              {t('settings.emailNotifications', 'Email Notifications')}
                            </Text>
                            <Text color="#848E9C" fontSize="sm">
                              {t('settings.emailNotificationsDesc', 'Receive important updates via email')}
                            </Text>
                          </Box>
                          <Switch
                            colorScheme="yellow"
                            size={{ base: "md", md: "lg" }}
                            defaultChecked
                            alignSelf={{ base: "flex-start", md: "center" }}
                          />
                        </Flex>
                      </Box>

                      <Box bg="#0B0E11" p={4} borderRadius="md">
                        <Flex justify="space-between" align="center">
                          <Box>
                            <Text color="#EAECEF" fontWeight="medium">
                              {t('settings.transactionAlerts', 'Transaction Alerts')}
                            </Text>
                            <Text color="#848E9C" fontSize="sm">
                              {t('settings.transactionAlertsDesc', 'Get notified about deposits and withdrawals')}
                            </Text>
                          </Box>
                          <Switch
                            colorScheme="yellow"
                            size="lg"
                            defaultChecked
                          />
                        </Flex>
                      </Box>

                      <Box bg="#0B0E11" p={4} borderRadius="md">
                        <Flex justify="space-between" align="center">
                          <Box>
                            <Text color="#EAECEF" fontWeight="medium">
                              {t('settings.investmentUpdates', 'Investment Updates')}
                            </Text>
                            <Text color="#848E9C" fontSize="sm">
                              {t('settings.investmentUpdatesDesc', 'Receive updates about your investment performance')}
                            </Text>
                          </Box>
                          <Switch
                            colorScheme="yellow"
                            size="lg"
                            defaultChecked
                          />
                        </Flex>
                      </Box>

                      <Box bg="#0B0E11" p={4} borderRadius="md">
                        <Flex justify="space-between" align="center">
                          <Box>
                            <Text color="#EAECEF" fontWeight="medium">
                              {t('settings.marketNews', 'Market News')}
                            </Text>
                            <Text color="#848E9C" fontSize="sm">
                              {t('settings.marketNewsDesc', 'Stay updated with cryptocurrency market news')}
                            </Text>
                          </Box>
                          <Switch
                            colorScheme="yellow"
                            size="lg"
                          />
                        </Flex>
                      </Box>
                    </VStack>
                  </Box>

                  {/* Security Settings */}
                  <Box
                    bg="#1E2329"
                    p={6}
                    borderRadius="xl"
                    borderWidth="1px"
                    borderColor="#2B3139"
                    boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
                  >
                    <Heading size="sm" color="#F0B90B" mb={4} display="flex" alignItems="center">
                      <Icon as={FaShieldAlt} mr={2} />
                      {t('settings.security', 'Security Settings')}
                    </Heading>

                    <VStack spacing={4} align="stretch">
                      <Box bg="#0B0E11" p={4} borderRadius="md">
                        <Flex justify="space-between" align="center">
                          <Box>
                            <Text color="#EAECEF" fontWeight="medium">
                              {t('settings.loginAlerts', 'Login Alerts')}
                            </Text>
                            <Text color="#848E9C" fontSize="sm">
                              {t('settings.loginAlertsDesc', 'Get notified when someone logs into your account')}
                            </Text>
                          </Box>
                          <Switch
                            colorScheme="yellow"
                            size="lg"
                            defaultChecked
                          />
                        </Flex>
                      </Box>

                      <Box bg="#0B0E11" p={4} borderRadius="md">
                        <Flex justify="space-between" align="center">
                          <Box>
                            <Text color="#EAECEF" fontWeight="medium">
                              {t('settings.sessionTimeout', 'Auto Logout')}
                            </Text>
                            <Text color="#848E9C" fontSize="sm">
                              {t('settings.sessionTimeoutDesc', 'Automatically log out after inactivity')}
                            </Text>
                          </Box>
                          <Select
                            w="150px"
                            bg="#0B0E11"
                            borderColor="#2B3139"
                            color="#EAECEF"
                            size="sm"
                          >
                            <option value="15">15 minutes</option>
                            <option value="30">30 minutes</option>
                            <option value="60">1 hour</option>
                            <option value="0">Never</option>
                          </Select>
                        </Flex>
                      </Box>

                      <Box bg="#0B0E11" p={4} borderRadius="md">
                        <Flex justify="space-between" align="center">
                          <Box>
                            <Text color="#EAECEF" fontWeight="medium">
                              {t('settings.ipWhitelist', 'IP Whitelist')}
                            </Text>
                            <Text color="#848E9C" fontSize="sm">
                              {t('settings.ipWhitelistDesc', 'Restrict access to specific IP addresses')}
                            </Text>
                          </Box>
                          <Button
                            size="sm"
                            variant="outline"
                            borderColor="#F0B90B"
                            color="#F0B90B"
                            _hover={{ bg: "#F0B90B22" }}
                          >
                            {t('settings.configure', 'Configure')}
                          </Button>
                        </Flex>
                      </Box>
                    </VStack>
                  </Box>

                  {/* Trading Preferences */}
                  <Box
                    bg="#1E2329"
                    p={6}
                    borderRadius="xl"
                    borderWidth="1px"
                    borderColor="#2B3139"
                    boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
                  >
                    <Heading size="sm" color="#F0B90B" mb={4} display="flex" alignItems="center">
                      <Icon as={FaChartLine} mr={2} />
                      {t('settings.tradingPreferences', 'Trading Preferences')}
                    </Heading>

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                      <FormControl>
                        <FormLabel color="#848E9C" fontSize="sm">
                          {t('settings.defaultInvestmentAmount', 'Default Investment Amount')}
                        </FormLabel>
                        <InputGroup>
                          <Input
                            type="number"
                            placeholder="100"
                            bg="#0B0E11"
                            borderColor="#2B3139"
                            color="#EAECEF"
                            _hover={{ borderColor: "#F0B90B" }}
                            _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                          />
                          <InputRightAddon bg="#2B3139" borderColor="#2B3139" color="#848E9C">
                            USDT
                          </InputRightAddon>
                        </InputGroup>
                        <Text color="#848E9C" fontSize="xs" mt={1}>
                          {t('settings.defaultInvestmentDesc', 'Pre-fill investment forms with this amount')}
                        </Text>
                      </FormControl>

                      <FormControl>
                        <FormLabel color="#848E9C" fontSize="sm">
                          {t('settings.preferredCrypto', 'Preferred Cryptocurrency')}
                        </FormLabel>
                        <Select
                          bg="#0B0E11"
                          borderColor="#2B3139"
                          color="#EAECEF"
                          _hover={{ borderColor: "#F0B90B" }}
                          _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
                        >
                          <option value="BTC">Bitcoin (BTC)</option>
                          <option value="ETH">Ethereum (ETH)</option>
                          <option value="USDT">Tether (USDT)</option>
                          <option value="BNB">Binance Coin (BNB)</option>
                          <option value="SOL">Solana (SOL)</option>
                        </Select>
                        <Text color="#848E9C" fontSize="xs" mt={1}>
                          {t('settings.preferredCryptoDesc', 'Default cryptocurrency for new investments')}
                        </Text>
                      </FormControl>
                    </SimpleGrid>

                    <Divider borderColor="#2B3139" my={4} />

                    <VStack spacing={4} align="stretch">
                      <Box bg="#0B0E11" p={4} borderRadius="md">
                        <Flex justify="space-between" align="center">
                          <Box>
                            <Text color="#EAECEF" fontWeight="medium">
                              {t('settings.autoReinvest', 'Auto-Reinvest Earnings')}
                            </Text>
                            <Text color="#848E9C" fontSize="sm">
                              {t('settings.autoReinvestDesc', 'Automatically reinvest your earnings for compound growth')}
                            </Text>
                          </Box>
                          <Switch
                            colorScheme="yellow"
                            size="lg"
                          />
                        </Flex>
                      </Box>

                      <Box bg="#0B0E11" p={4} borderRadius="md">
                        <Flex justify="space-between" align="center">
                          <Box>
                            <Text color="#EAECEF" fontWeight="medium">
                              {t('settings.riskWarnings', 'Risk Warnings')}
                            </Text>
                            <Text color="#848E9C" fontSize="sm">
                              {t('settings.riskWarningsDesc', 'Show risk warnings before making investments')}
                            </Text>
                          </Box>
                          <Switch
                            colorScheme="yellow"
                            size="lg"
                            defaultChecked
                          />
                        </Flex>
                      </Box>
                    </VStack>
                  </Box>

                  {/* Data & Privacy */}
                  <Box
                    bg="#1E2329"
                    p={6}
                    borderRadius="xl"
                    borderWidth="1px"
                    borderColor="#2B3139"
                    boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
                  >
                    <Heading size="sm" color="#F0B90B" mb={4} display="flex" alignItems="center">
                      <Icon as={FaUserShield} mr={2} />
                      {t('settings.dataPrivacy', 'Data & Privacy')}
                    </Heading>

                    <VStack spacing={4} align="stretch">
                      <Box bg="#0B0E11" p={4} borderRadius="md">
                        <Flex justify="space-between" align="center">
                          <Box>
                            <Text color="#EAECEF" fontWeight="medium">
                              {t('settings.dataCollection', 'Analytics Data Collection')}
                            </Text>
                            <Text color="#848E9C" fontSize="sm">
                              {t('settings.dataCollectionDesc', 'Help us improve by sharing anonymous usage data')}
                            </Text>
                          </Box>
                          <Switch
                            colorScheme="yellow"
                            size="lg"
                            defaultChecked
                          />
                        </Flex>
                      </Box>

                      <Box bg="#0B0E11" p={4} borderRadius="md">
                        <Flex justify="space-between" align="center">
                          <Box>
                            <Text color="#EAECEF" fontWeight="medium">
                              {t('settings.marketingEmails', 'Marketing Emails')}
                            </Text>
                            <Text color="#848E9C" fontSize="sm">
                              {t('settings.marketingEmailsDesc', 'Receive promotional offers and market insights')}
                            </Text>
                          </Box>
                          <Switch
                            colorScheme="yellow"
                            size="lg"
                          />
                        </Flex>
                      </Box>

                      <Divider borderColor="#2B3139" />

                      <HStack spacing={4}>
                        <Button
                          variant="outline"
                          borderColor="#848E9C"
                          color="#848E9C"
                          _hover={{ borderColor: "#F0B90B", color: "#F0B90B" }}
                          leftIcon={<Icon as={FaDownload} />}
                        >
                          {t('settings.exportData', 'Export My Data')}
                        </Button>
                        <Button
                          variant="outline"
                          borderColor="#F6465D"
                          color="#F6465D"
                          _hover={{ bg: "#F6465D22" }}
                          leftIcon={<Icon as={FaTrashAlt} />}
                        >
                          {t('settings.deleteAccount', 'Delete Account')}
                        </Button>
                      </HStack>
                    </VStack>
                  </Box>

                  {/* Save Settings Button */}
                  <Flex justify="flex-end" pt={4}>
                    <Button
                      bg="#F0B90B"
                      color="#0B0E11"
                      _hover={{ bg: "#F8D12F" }}
                      size="lg"
                      leftIcon={<Icon as={FaSave} />}
                      onClick={() => {
                        toast({
                          title: t('settings.saved', 'Settings Saved'),
                          description: t('settings.savedDesc', 'Your preferences have been updated successfully.'),
                          status: 'success',
                          duration: 3000,
                          isClosable: true,
                        });
                      }}
                    >
                      {t('settings.saveChanges', 'Save Changes')}
                    </Button>
                  </Flex>
                </VStack>
              </Box>
            )}

          </Box>
        </GridItem>
      </Grid>
    </Container>
    </Box>
  );
};

export default Profile;
