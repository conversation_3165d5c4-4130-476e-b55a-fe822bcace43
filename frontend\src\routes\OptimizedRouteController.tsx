import React, { useEffect } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorBoundary from '../components/ErrorBoundary';
import useAuth from '../hooks/useAuth';
import AdminLayout from '../layouts/AdminLayout';
import { withSuspense } from './LazyRoutes';
import AdminRoute from '../components/AdminRoute';
// Test pages removed for production

// Import all lazy-loaded components
import {
  LazyHome,
  LazyLogin,
  LazyRegister,
  LazyEmailVerification,
  LazyProfile,

  LazyFAQ,
  LazyAbout,
  LazyContact,
  LazyTermsOfService,
  LazyPrivacyPolicy,
  LazyNotFound,
  LazyUnauthorized,
  LazyMaintenancePage,
  LazyPaymentHistory,
  LazyInvestments,
  LazyTransactions,
  LazyReferrals,
  LazyWallet,
  LazyWalletManagement,

  LazyDepositPage,
  // LazyRouteTestPage, // Removed for production
  LazyAdminDashboard,
  LazyAdminUsers,
  LazyAdminTransactions,
  LazyAdminDeposits,
  LazyAdminWithdrawals,
  LazyAdminReferrals,
  LazyAdminContent,
  LazyAdminSettings,
  LazySystemManagement,
  LazyCryptoAddressManagement,
  LazyHomeManagement,
  LazyProfileManagement,
  LazySiteManagement,
  LazyCommissionSettings,
  LazyTransactionDetail
} from './LazyRoutes';

// Route types for better organization
enum RouteType {
  PUBLIC = 'public',
  PROTECTED = 'protected',
  ADMIN = 'admin'
}

// Route definition interface
interface RouteDefinition {
  path: string;
  component: React.ComponentType<any>;
  type: RouteType;
  title: string;
  description?: string;
  exact?: boolean;
  children?: RouteDefinition[];
}

// Apply Suspense to all components
const Home = withSuspense(LazyHome);
const Login = withSuspense(LazyLogin);
const Register = withSuspense(LazyRegister);
const EmailVerification = withSuspense(LazyEmailVerification);
const Profile = withSuspense(LazyProfile);

const About = withSuspense(LazyAbout);
const Contact = withSuspense(LazyContact);
const FAQ = withSuspense(LazyFAQ);
const TermsOfService = withSuspense(LazyTermsOfService);
const PrivacyPolicy = withSuspense(LazyPrivacyPolicy);
const NotFoundPage = withSuspense(LazyNotFound);
const Unauthorized = withSuspense(LazyUnauthorized);
const MaintenancePage = withSuspense(LazyMaintenancePage);
const PaymentHistory = withSuspense(LazyPaymentHistory);
const Investments = withSuspense(LazyInvestments);
const Transactions = withSuspense(LazyTransactions);
const Referrals = withSuspense(LazyReferrals);
const Wallet = withSuspense(LazyWallet);
const WalletManagement = withSuspense(LazyWalletManagement);
const DepositPage = withSuspense(LazyDepositPage);
// const RouteTestPage = withSuspense(LazyRouteTestPage); // Removed for production

// Admin components with Suspense
const AdminDashboard = withSuspense(LazyAdminDashboard);
const AdminUsers = withSuspense(LazyAdminUsers);
const AdminTransactions = withSuspense(LazyAdminTransactions);
const AdminDeposits = withSuspense(LazyAdminDeposits);
const AdminWithdrawals = withSuspense(LazyAdminWithdrawals);
const AdminReferrals = withSuspense(LazyAdminReferrals);
const AdminContent = withSuspense(LazyAdminContent);
const AdminSettings = withSuspense(LazyAdminSettings);
const SystemManagement = withSuspense(LazySystemManagement);
const CryptoAddressManagement = withSuspense(LazyCryptoAddressManagement);
const HomeManagement = withSuspense(LazyHomeManagement);
const ProfileManagement = withSuspense(LazyProfileManagement);
const SiteManagement = withSuspense(LazySiteManagement);
const CommissionSettings = withSuspense(LazyCommissionSettings);
const TransactionDetail = withSuspense(LazyTransactionDetail);

// Route definitions
const routes: RouteDefinition[] = [
  // Public routes
  {
    path: '/',
    component: Home,
    type: RouteType.PUBLIC,
    title: 'Home | Shipping Finance',
    description: 'Welcome to Shipping Finance - Your trusted crypto investment platform'
  },
  {
    path: '/login',
    component: Login,
    type: RouteType.PUBLIC,
    title: 'Login | Shipping Finance',
    description: 'Login to your Shipping Finance account'
  },

  {
    path: '/register',
    component: Register,
    type: RouteType.PUBLIC,
    title: 'Register | Shipping Finance',
    description: 'Create a new Shipping Finance account'
  },
  {
    path: '/verify-email',
    component: EmailVerification,
    type: RouteType.PUBLIC,
    title: 'Email Verification | Shipping Finance',
    description: 'Verify your email address to complete registration'
  },
  {
    path: '/about',
    component: About,
    type: RouteType.PUBLIC,
    title: 'About Us | Shipping Finance',
    description: 'Learn more about Shipping Finance and our mission'
  },
  {
    path: '/contact',
    component: Contact,
    type: RouteType.PUBLIC,
    title: 'Contact Us | Shipping Finance',
    description: 'Get in touch with the Shipping Finance team'
  },
  {
    path: '/faq',
    component: FAQ,
    type: RouteType.PUBLIC,
    title: 'FAQ | Shipping Finance',
    description: 'Frequently asked questions about Shipping Finance'
  },
  {
    path: '/terms',
    component: TermsOfService,
    type: RouteType.PUBLIC,
    title: 'Terms of Service | Shipping Finance',
    description: 'Terms of Service for Shipping Finance'
  },
  {
    path: '/privacy',
    component: PrivacyPolicy,
    type: RouteType.PUBLIC,
    title: 'Privacy Policy | Shipping Finance',
    description: 'Privacy Policy for Shipping Finance'
  },
  {
    path: '/unauthorized',
    component: Unauthorized,
    type: RouteType.PUBLIC,
    title: 'Unauthorized | Shipping Finance',
    description: 'You do not have permission to access this page'
  },
  {
    path: '/maintenance',
    component: MaintenancePage,
    type: RouteType.PUBLIC,
    title: 'Maintenance | Shipping Finance',
    description: 'System is currently under maintenance'
  },
  // Test routes removed for production

  // Protected routes
  {
    path: '/profile',
    component: Profile,
    type: RouteType.PROTECTED,
    title: 'Profile | Shipping Finance',
    description: 'Manage your Shipping Finance profile'
  },
  {
    path: '/payments',
    component: PaymentHistory,
    type: RouteType.PROTECTED,
    title: 'Payment History | Shipping Finance',
    description: 'View your payment history and earnings'
  },
  {
    path: '/investments',
    component: Investments,
    type: RouteType.PROTECTED,
    title: 'Investments | Shipping Finance',
    description: 'Manage your investments and view earnings'
  },
  {
    path: '/transactions',
    component: Transactions,
    type: RouteType.PROTECTED,
    title: 'Transactions | Shipping Finance',
    description: 'View your transaction history and details'
  },
  {
    path: '/referrals',
    component: Referrals,
    type: RouteType.PROTECTED,
    title: 'Referrals | Shipping Finance',
    description: 'Manage your referrals and earn commissions'
  },
  {
    path: '/wallet',
    component: Wallet,
    type: RouteType.PROTECTED,
    title: 'Wallet | Shipping Finance',
    description: 'Manage your crypto assets, deposits, and withdrawals'
  },
  {
    path: '/wallet-management',
    component: WalletManagement,
    type: RouteType.PROTECTED,
    title: 'Wallet Management | Shipping Finance',
    description: 'Manage your cryptocurrency withdrawal addresses'
  },

  {
    path: '/deposit/:currency',
    component: DepositPage,
    type: RouteType.PROTECTED,
    title: 'Crypto Deposit | Shipping Finance',
    description: 'Deposit cryptocurrency to your wallet'
  },

  // Admin routes
  {
    path: '/admin',
    component: AdminDashboard,
    type: RouteType.ADMIN,
    title: 'Admin Dashboard | Shipping Finance',
    description: 'Shipping Finance administration dashboard',
    exact: true
  },
  {
    path: '/admin/users',
    component: AdminUsers,
    type: RouteType.ADMIN,
    title: 'User Management | Admin | Shipping Finance',
    description: 'Manage users on Shipping Finance'
  },
  {
    path: '/admin/transactions',
    component: AdminTransactions,
    type: RouteType.ADMIN,
    title: 'Transactions | Admin | Shipping Finance',
    description: 'Manage transactions on Shipping Finance'
  },
  {
    path: '/admin/deposits',
    component: AdminDeposits,
    type: RouteType.ADMIN,
    title: 'Deposits | Admin | Shipping Finance',
    description: 'Manage deposits on Shipping Finance'
  },
  {
    path: '/admin/settings',
    component: AdminSettings,
    type: RouteType.ADMIN,
    title: 'Settings | Admin | Shipping Finance',
    description: 'Manage settings on Shipping Finance'
  },
  {
    path: '/admin/transaction/:id',
    component: TransactionDetail,
    type: RouteType.ADMIN,
    title: 'Transaction Detail | Admin | Shipping Finance',
    description: 'View transaction details on Shipping Finance'
  },
  {
    path: '/admin/system-management',
    component: SystemManagement,
    type: RouteType.ADMIN,
    title: 'System Management | Admin | Shipping Finance',
    description: 'Manage system settings on Shipping Finance'
  }
];

// Protected route wrapper component with enhanced debugging
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, loading } = useAuth();
  const location = useLocation();

  console.log('🔒 ProtectedRoute Check:', {
    loading,
    hasUser: !!user,
    userEmail: user?.email,
    userId: user?._id,
    currentPath: location.pathname,
    timestamp: new Date().toISOString()
  });

  if (loading) {
    console.log('⏳ ProtectedRoute: Still loading authentication...');
    return <LoadingSpinner />;
  }

  if (!user) {
    console.log('❌ ProtectedRoute: No user found, redirecting to login');
    console.log('Current path:', location.pathname);

    // Save the location they were trying to access for redirect after login
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  console.log('✅ ProtectedRoute: User authenticated, allowing access');
  console.log('User:', user.email, 'accessing:', location.pathname);

  return <>{children}</>;
};



// Route controller component
const OptimizedRouteController = () => {
  const location = useLocation();

  // Update document title and meta description based on current route
  const updateDocumentMeta = () => {
    const currentRoute = routes.find(route => {
      if (route.exact) {
        return route.path === location.pathname;
      }
      return location.pathname.startsWith(route.path);
    });

    if (currentRoute) {
      document.title = currentRoute.title;

      // Update meta description
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription && currentRoute.description) {
        metaDescription.setAttribute('content', currentRoute.description);
      }
    } else {
      document.title = 'Shipping Finance';
    }
  };

  // Update meta tags when location changes
  useEffect(() => {
    updateDocumentMeta();
  }, [location]);

  return (
    <Routes>
      {/* Public Routes */}
      {routes
        .filter(route => route.type === RouteType.PUBLIC)
        .map(route => (
          <Route
            key={route.path}
            path={route.path}
            element={
              <ErrorBoundary>
                <route.component />
              </ErrorBoundary>
            }
          />
        ))}

      {/* Protected Routes */}
      {routes
        .filter(route => route.type === RouteType.PROTECTED)
        .map(route => (
          <Route
            key={route.path}
            path={route.path}
            element={
              <ProtectedRoute>
                <ErrorBoundary>
                  <route.component />
                </ErrorBoundary>
              </ProtectedRoute>
            }
          />
        ))}

      {/* Admin Routes */}
      <Route
        path="/admin"
        element={
          <AdminRoute>
            <ErrorBoundary>
              <AdminLayout />
            </ErrorBoundary>
          </AdminRoute>
        }
      >
        {/* Index route for /admin */}
        <Route index element={<AdminDashboard />} />

        {/* Nested admin routes */}
        <Route path="users" element={<AdminUsers />} />
        <Route path="transactions" element={<AdminTransactions />} />
        <Route path="deposits" element={<AdminDeposits />} />
        <Route path="withdrawals" element={<AdminWithdrawals />} />
        <Route path="crypto-addresses" element={<CryptoAddressManagement />} />
        <Route path="referrals" element={<AdminReferrals />} />
        <Route path="content" element={<AdminContent />} />
        <Route path="settings" element={<AdminSettings />} />
        <Route path="transaction/:id" element={<TransactionDetail />} />
        <Route path="home-management" element={<HomeManagement />} />
        <Route path="profile-management" element={<ProfileManagement />} />
        <Route path="site-management" element={<SiteManagement />} />
        <Route path="commission" element={<CommissionSettings />} />
        <Route path="system-management" element={<SystemManagement />} />
      </Route>

      {/* Dashboard redirect to home */}
      <Route path="/dashboard" element={<Navigate to="/" replace />} />

      {/* 404 Route - Catch all unmatched routes */}
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
};

export default OptimizedRouteController;
export { RouteType, routes };
