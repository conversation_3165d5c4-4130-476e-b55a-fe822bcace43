import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { configureSafariAxios, getSafariHeaders, isSafari, isIOSSafari } from '../utils/safariUtils';

// Create axios instance with increased timeout - Use environment variable directly
const API_URL = import.meta.env.VITE_API_URL || '/api';
console.log('🔧 API Service URL:', API_URL);

const api: AxiosInstance = axios.create({
  baseURL: API_URL,
  timeout: 30000, // Increased from 10000 to 30000 ms (30 seconds)
  headers: {
    'Content-Type': 'application/json',
    ...getSafariHeaders(), // Add Safari-specific headers
  },
  withCredentials: true, // Important for cookies
});

// Configure Safari-specific axios settings
configureSafariAxios(api);

// Request interceptor for adding auth token
api.interceptors.request.use(
  (config) => {
    console.log('📤 API Request Starting:', config.method?.toUpperCase(), config.url);
    console.log('📤 Request Base URL:', config.baseURL);
    console.log('📤 Full URL:', `${config.baseURL}${config.url}`);

    // Try to get token from localStorage
    let token = localStorage.getItem('token');
    console.log('Token from localStorage:', token ? 'Found' : 'Not found');

    // If token is not found in localStorage, try to get it from user object
    if (!token) {
      const userStr = localStorage.getItem('user');
      console.log('User from localStorage:', userStr ? 'Found' : 'Not found');

      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          console.log('User parsed successfully:', user ? 'Yes' : 'No');
          console.log('User token exists:', user && user.token ? 'Yes' : 'No');

          if (user && user.token) {
            token = user.token;
            // Save token to localStorage for future use
            if (token) {
              localStorage.setItem('token', token);
              console.log('Token extracted from user and saved to localStorage');
            }
          }
        } catch (e) {
          console.error('Error parsing user from localStorage:', e);
        }
      }
    }

    // Removed debug token generation code for security

    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('Authorization header added:', `Bearer ${token.substring(0, 10)}...`);

      // Log the full headers for debugging
      console.log('Request headers:', config.headers);
    } else {
      console.warn('No token found in localStorage or user object');
    }

    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log('📥 API Response Success:', response.status, response.config.url);
    console.log('📥 Response Data:', response.data);

    // Log Safari-specific response info
    if ((isSafari() || isIOSSafari()) && process.env.NODE_ENV === 'development') {
      console.log('📥 Safari Response Headers:', response.headers);
    }

    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.status, error.response?.data);

    // Handle Safari-specific CORS errors
    if (isSafari() || isIOSSafari()) {
      const isCorsError =
        error.message?.includes('CORS') ||
        error.message?.includes('Cross-Origin') ||
        error.code === 'ERR_NETWORK' ||
        error.response?.status === 0;

      if (isCorsError) {
        console.error('🦎 Safari CORS Error detected:', {
          userAgent: navigator.userAgent,
          error: error.message,
          status: error.response?.status,
          headers: error.response?.headers,
          config: {
            method: error.config?.method,
            url: error.config?.url,
            baseURL: error.config?.baseURL
          }
        });
        error.customMessage = 'Safari tarayıcısında bağlantı sorunu. Lütfen sayfayı yenileyin veya farklı bir tarayıcı deneyin.';
      }
    }

    // Improved error handling with more detailed messages
    if (error.code === 'ECONNABORTED') {
      console.error('Request timeout - connection aborted');
      error.customMessage = 'Sunucu yanıt vermiyor. Lütfen daha sonra tekrar deneyin.';
    } else if (error.message && error.message.includes('Network Error')) {
      console.error('Network error detected');
      error.customMessage = 'Ağ bağlantısı hatası. İnternet bağlantınızı kontrol edin.';
    } else if (error.response && error.response.status === 401) {
      console.warn('Unauthorized error detected, redirecting to login');
      error.customMessage = 'Oturum süresi doldu veya geçersiz kimlik bilgileri.';

      // Clear token and user data
      localStorage.removeItem('token');
      localStorage.removeItem('user');

      // Add a small delay before redirecting to allow logs to be sent
      setTimeout(() => {
        window.location.href = '/login?error=session_expired';
      }, 100);
    }

    return Promise.reject(error);
  }
);

/**
 * API service for authentication endpoints
 */
export const authService = {
  login: async (credentials: { email: string; password: string }) => {
    try {
      const response = await api.post('/users/login', credentials);
      return response;
    } catch (error: any) {
      console.error('Login error:', error.response?.data || error.message);
      throw error.customMessage ? { message: error.customMessage } : error;
    }
  },
  register: async (userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phoneNumber?: string;
    country?: string;
    city?: string;
    referralCode?: string;
    marketingConsent?: boolean;
  }) => {
    try {
      const response = await api.post('/users/register', userData);
      return response;
    } catch (error: any) {
      console.error('Registration error:', error.response?.data || error.message);
      throw error.customMessage ? { message: error.customMessage } : (error.response?.data || {
        status: 'error',
        message: 'Kayıt işlemi başarısız oldu. Lütfen tekrar deneyin.'
      });
    }
  },
  forgotPassword: (email: string) => api.post('/users/forgot-password', { email }),
  resetPassword: (token: string, password: string) =>
    api.post('/users/reset-password', { token, password }),
  verifyEmail: (token: string) => api.get(`/users/email-verification/verify/${token}`),
  sendVerificationEmail: () => api.post('/users/email-verification/send'),
  resendVerificationEmail: () => api.post('/users/email-verification/resend'),
  getVerificationStatus: () => api.get('/users/email-verification/status'),
  getProfile: () => api.get('/users/profile'),
  updateProfile: (userData: any) => api.put('/users/profile', userData),
};

/**
 * API service for wallet-related endpoints
 */
export const walletService = {
  getBalance: () => api.get('/wallets/info'),
  getTransactions: (params?: any) => api.get('/wallets/transactions', { params }),
  deposit: (data: { token: string; amount: number; mode: 'commission' | 'interest' }) =>
    api.post('/wallets/deposit', data),
  withdraw: (data: { token: string; amount: number }) =>
    api.post('/wallets/withdraw', data),
  toggleMode: (data: { token: string }) =>
    api.post('/wallets/toggle-mode', data),
};

/**
 * API service for transaction-related endpoints
 */
export const transactionService = {
  getAll: async (params?: any) => {
    try {
      return await api.get('/transactions', { params });
    } catch (error: any) {
      console.warn('Backend API not available for transactions, using mock data:', error.message);

      // Mock transaction data
      const mockTransactions = [
        {
          id: 'tx_001',
          user: '<EMAIL>',
          amount: 1000,
          type: 'deposit',
          status: 'approved',
          date: '2024-01-15',
          wallet: 'BTC',
          txHash: '1a2b3c4d5e6f7g8h9i0j'
        },
        {
          id: 'tx_002',
          user: '<EMAIL>',
          amount: 500,
          type: 'withdrawal',
          status: 'pending',
          date: '2024-01-14',
          wallet: 'ETH',
          txHash: null
        },
        {
          id: 'tx_003',
          user: '<EMAIL>',
          amount: 750,
          type: 'deposit',
          status: 'approved',
          date: '2024-01-13',
          wallet: 'USDT',
          txHash: '9z8y7x6w5v4u3t2s1r0q'
        }
      ];

      return {
        data: {
          data: mockTransactions,
          total: mockTransactions.length
        }
      };
    }
  },
  getById: async (id: string) => {
    try {
      return await api.get(`/transactions/${id}`);
    } catch (error: any) {
      console.warn('Backend API not available for transaction details, using mock data:', error.message);
      return {
        data: {
          id: id,
          user: '<EMAIL>',
          amount: 1000,
          type: 'deposit',
          status: 'pending',
          date: new Date().toISOString().split('T')[0],
          wallet: 'BTC'
        }
      };
    }
  },
  getAdminTransactions: async (params?: any) => {
    try {
      return await api.get('/transactions/admin/all', { params });
    } catch (error: any) {
      console.warn('Backend API not available for admin transactions, using mock data:', error.message);
      return transactionService.getAll(params);
    }
  },
  updateStatus: async (id: string, data: { status: string; txHash?: string; notes?: string }) => {
    try {
      return await api.put(`/transactions/${id}/status`, data);
    } catch (error: any) {
      console.warn('Backend API not available for transaction status update, using mock response:', error.message);
      return {
        data: {
          success: true,
          message: 'Transaction status updated successfully (mock)',
          data: { id, ...data, updatedAt: new Date().toISOString() }
        }
      };
    }
  },
  calculateCommission: async (data: { asset: string; amount: number }) => {
    try {
      return await api.post('/transactions/calculate-commission', data);
    } catch (error: any) {
      console.warn('Backend API not available for commission calculation, using mock response:', error.message);
      return {
        data: {
          commission: data.amount * 0.01, // 1% commission
          asset: data.asset,
          amount: data.amount
        }
      };
    }
  },
  calculateInterest: async (data: { asset: string; amount: number; days: number }) => {
    try {
      return await api.post('/transactions/calculate-interest', data);
    } catch (error: any) {
      console.warn('Backend API not available for interest calculation, using mock response:', error.message);
      return {
        data: {
          interest: data.amount * 0.01 * data.days, // 1% daily interest
          asset: data.asset,
          amount: data.amount,
          days: data.days
        }
      };
    }
  },
};

/**
 * API service for investment-related endpoints
 */
export const investmentService = {
  getAll: (params?: any) => api.get('/investments', { params }),
  getById: (id: string) => api.get(`/investments/${id}`),
  create: (data: any) => api.post('/investments', data),
  createInvestment: (data: any) => api.post('/investments', data), // Fix: Add missing method alias
  uploadReceipt: (id: string, formData: FormData) =>
    api.post(`/investments/${id}/receipt`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }),
  updateTxHash: (id: string, txHash: string) =>
    api.put(`/investments/${id}/txhash`, { txHash }),
  updateTransactionHash: (id: string, txHash: string) =>
    api.put(`/investments/${id}/txhash`, { txHash }), // Fix: Add missing method alias
  getDepositAddress: (currency: string, network: string) =>
    api.get(`/crypto/addresses/deposit/${currency}?network=${network}`), // Fix: Remove duplicate /api prefix
};

/**
 * API service for investment package endpoints
 */
export const investmentPackageService = {
  // Package management
  getPackages: (params?: any) => api.get('/investment-packages/packages', { params }),
  getPackageById: (id: string) => api.get(`/investment-packages/${id}`),
  getUserPackages: (params?: any) => api.get('/investment-packages/user-packages', { params }),
  getComprehensive: () => api.get('/investment-packages/comprehensive'),
  createPackage: (data: any) => api.post('/investment-packages/create', data),
  createFromDeposit: (data: any) => api.post('/investment-packages/create-from-deposit', data),

  // Earnings and statistics
  getEarnings: () => api.get('/investment-packages/earnings'),
  getEarningsHistory: (params?: any) => api.get('/investment-packages/earnings-history', { params }),
  getDistributions: (params?: any) => api.get('/investment-packages/distributions', { params }),
  getInterestDistributions: (params?: any) => api.get('/investment-packages/interest-distributions', { params }),

  // Balances and withdrawals
  getBalances: () => api.get('/investment-packages/balances'),
  getBalanceByCurrency: (currency: string) => api.get(`/investment-packages/balances/${currency}`),
  checkWithdrawalEligibility: (currency: string) => api.get(`/investment-packages/withdrawal-eligibility/${currency}`),
  requestWithdrawal: (data: any) => api.post('/investment-packages/withdraw', data),
  withdrawFromPackage: (packageId: string, data: any) => api.post(`/investment-packages/${packageId}/withdraw`, data),
  reinvestPackage: (packageId: string, data: any) => api.post(`/investment-packages/${packageId}/reinvest`, data),

  // Analytics
  getPackageAnalytics: () => api.get('/investment-packages/analytics'),
  getPerformanceMetrics: () => api.get('/investment-packages/performance'),
};

/**
 * API service for user-related endpoints
 */
export const userService = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (userData: any) => api.put('/users/profile', userData),
  getReferrals: () => api.get('/users/referrals'),
  getReferralInfo: () => api.get('/users/referral-info'),
  applyReferralCode: (referralCode: string) => api.post('/users/apply-referral', { referralCode }),
  getReferralCommissions: (params?: any) => api.get('/commissions', { params }),
  getReferralTotalCommission: () => api.get('/commissions/total'),
};

/**
 * API service for referral-related endpoints
 */
export const referralService = {
  // Referral validation
  validateReferralCode: (referralCode: string) => api.post('/referrals/validate', { referralCode }),

  // User referral management
  getReferralHistory: (params?: any) => api.get('/user/referrals/history', { params }),
  getReferralStats: () => api.get('/user/referrals/stats'),
  getReferralCode: () => api.get('/user/referrals/code'),
  generateReferralCode: () => api.post('/user/referrals/generate-code'),

  // Commission management
  getCommissionBalance: () => api.get('/user/referrals/commission-balance'),
  getCommissionBalanceByCurrency: (currency: string) => api.get(`/user/referrals/commission-balance/${currency}`),
  requestCommissionWithdrawal: (data: any) => api.post('/user/referrals/withdraw-commission', data),
  validateCommissionWithdrawal: (data: any) => api.post('/user/referrals/validate-withdrawal', data),
  getCommissionWithdrawalHistory: (params?: any) => api.get('/user/referrals/withdrawal-history', { params }),

  // Analytics
  getReferralAnalytics: () => api.get('/user/referrals/analytics'),
};



/**
 * API service for user system configuration endpoints
 * These endpoints provide system information filtered for user access
 */
export const userSystemService = {
  getSystemConfig: () => api.get('/user-system/config'),
  getPublicSystemInfo: () => api.get('/user-system/public'),
  getCommissionRates: () => api.get('/user-system/commission-rates'),
  getTransactionSettings: () => api.get('/user-system/transaction-settings'),
};

/**
 * API service for admin-related endpoints
 */
export const adminService = {
  getDashboardStats: async () => {
    try {
      return await api.get('/admin/dashboard');
    } catch (error: any) {
      console.warn('Backend API not available for dashboard stats, using mock data:', error.message);
      return {
        data: {
          totalUsers: 150,
          totalDeposits: 25000,
          totalWithdrawals: 12000,
          pendingTransactions: 8,
          activeInvestments: 45
        }
      };
    }
  },
  getUsers: async (params?: any) => {
    try {
      return await api.get('/admin/users', { params });
    } catch (error: any) {
      console.warn('Backend API not available for users, using mock data:', error.message);

      // Mock user data
      const mockUsers = [
        {
          id: 'user_001',
          name: 'John Doe',
          email: '<EMAIL>',
          joinDate: '2024-01-10',
          status: 'active',
          totalDeposits: 5000
        },
        {
          id: 'user_002',
          name: 'Jane Smith',
          email: '<EMAIL>',
          joinDate: '2024-01-08',
          status: 'active',
          totalDeposits: 3000
        },
        {
          id: 'user_003',
          name: 'Bob Wilson',
          email: '<EMAIL>',
          joinDate: '2024-01-05',
          status: 'inactive',
          totalDeposits: 1500
        }
      ];

      return {
        data: {
          data: mockUsers,
          total: mockUsers.length
        }
      };
    }
  },
  getUserById: async (id: string) => {
    try {
      return await api.get(`/admin/users/${id}`);
    } catch (error: any) {
      console.warn('Backend API not available for user details, using mock data:', error.message);
      return {
        data: {
          id: id,
          name: 'Mock User',
          email: '<EMAIL>',
          joinDate: '2024-01-01',
          status: 'active',
          totalDeposits: 1000
        }
      };
    }
  },
  updateUser: async (id: string, userData: any) => {
    try {
      return await api.put(`/admin/users/${id}`, userData);
    } catch (error: any) {
      console.warn('Backend API not available for user update, using mock response:', error.message);
      return {
        data: {
          success: true,
          message: 'User updated successfully (mock)',
          data: { id, ...userData, updatedAt: new Date().toISOString() }
        }
      };
    }
  },
  toggleAdminStatus: async (id: string) => {
    try {
      return await api.put(`/admin/users/${id}/toggle-admin`);
    } catch (error: any) {
      console.warn('Backend API not available for admin status toggle, using mock response:', error.message);
      return {
        data: {
          success: true,
          message: 'Admin status toggled successfully (mock)'
        }
      };
    }
  },
  deleteUser: async (id: string) => {
    try {
      return await api.delete(`/admin/users/${id}`);
    } catch (error: any) {
      console.warn('Backend API not available for user deletion, using mock response:', error.message);
      return {
        data: {
          success: true,
          message: 'User deleted successfully (mock)'
        }
      };
    }
  },
  getDeposits: async (params?: any) => {
    try {
      return await api.get('/admin/deposits', { params });
    } catch (error: any) {
      console.warn('Backend API not available for deposits, using mock data:', error.message);
      return transactionService.getAll(params);
    }
  },
  getWithdrawals: async (params?: any) => {
    try {
      return await api.get('/admin/withdrawals', { params });
    } catch (error: any) {
      console.warn('Backend API not available for withdrawals, using mock data:', error.message);
      return transactionService.getAll(params);
    }
  },
  updateDepositStatus: async (id: string, data: { status: string; adminNotes?: string; txHash?: string; walletAddress?: string }) => {
    try {
      return await api.put(`/admin/deposits/${id}/status`, data);
    } catch (error: any) {
      console.warn('Backend API not available for deposit status update, using mock response:', error.message);
      return {
        data: {
          success: true,
          message: 'Deposit status updated successfully (mock)',
          data: { id, ...data, updatedAt: new Date().toISOString() }
        }
      };
    }
  },
  updateWithdrawalStatus: async (id: string, data: { status: string; txHash?: string; adminNotes?: string; walletAddress?: string }) => {
    try {
      return await api.put(`/admin/withdrawals/${id}/status`, data);
    } catch (error: any) {
      console.warn('Backend API not available for withdrawal status update, using mock response:', error.message);
      return {
        data: {
          success: true,
          message: 'Withdrawal status updated successfully (mock)',
          data: { id, ...data, updatedAt: new Date().toISOString() }
        }
      };
    }
  },
  // Updated to use admin routes for better security
  getSystemConfig: async () => {
    try {
      return await api.get('/admin/system/config');
    } catch (error: any) {
      console.warn('Backend API not available for system config, using mock data:', error.message);
      return {
        data: {
          siteName: 'CryptoYield Platform',
          maintenanceMode: false,
          registrationEnabled: true,
          minDepositAmount: 10,
          maxDepositAmount: 100000,
          dailyInterestRate: 0.01
        }
      };
    }
  },
  updateSystemConfig: async (data: any) => {
    try {
      return await api.put('/admin/system/config', data);
    } catch (error: any) {
      console.warn('Backend API not available for system config update, using mock response:', error.message);
      return {
        data: {
          success: true,
          message: 'System config updated successfully (mock)',
          data: { ...data, updatedAt: new Date().toISOString() }
        }
      };
    }
  },
  getCryptoAddresses: async () => {
    try {
      return await api.get('/admin/system/crypto-addresses');
    } catch (error: any) {
      console.warn('Backend API not available for crypto addresses, using mock data:', error.message);
      return {
        data: {
          BTC: ['**********************************'],
          ETH: ['******************************************'],
          USDT: ['TKQvCcXdgmAQwu4U5RgQJdFfvGbmzXxC9X'],
          BNB: ['bnb1jxfh2g85q3v0tdq56fnevx6xcxtcnhtsmcu64m'],
          TRX: ['TKQvCcXdgmAQwu4U5RgQJdFfvGbmzXxC9X'],
          SOL: ['********************************'],
          DOGE: ['DH5yaieqoZN36fDVciNyRueRGvGLR3mr7L']
        }
      };
    }
  },
  updateCryptoAddresses: async (currency: string, addresses: string[]) => {
    try {
      return await api.put(`/admin/system/crypto-addresses/${currency}`, { addresses });
    } catch (error: any) {
      console.warn('Backend API not available for crypto address update, using mock response:', error.message);
      return {
        data: {
          success: true,
          message: 'Crypto addresses updated successfully (mock)',
          data: { currency, addresses, updatedAt: new Date().toISOString() }
        }
      };
    }
  },
};

export default api;