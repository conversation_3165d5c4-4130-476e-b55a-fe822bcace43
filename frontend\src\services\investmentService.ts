import { apiClient } from '../utils/apiClient';

/**
 * API service for investment-related endpoints
 */
export const investmentService = {
  /**
   * Create a new investment
   * @param data Investment data
   */
  createInvestment: async (data: {
    currency: string;
    amount: number;
    description?: string;
    network?: string;
  }) => {
    try {
      // Reset circuit breaker before making the request
      apiClient.resetCircuitBreaker();
      console.log('Creating investment with data:', data);

      // Try to make the API call first
      try {
        const response = await apiClient.post('/investments', data);
        console.log('Investment creation response:', response);
        return response;
      } catch (apiError: any) {
        console.warn('Backend API not available, using mock response:', apiError.message);

        // Return mock response when backend is not available
        // Format MUST match backend exactly: { success: true, data: { investment: {...} } }
        const mockResponse = {
          data: {
            success: true,
            message: 'Investment created successfully (mock)',
            data: {
              investment: {
                _id: 'mock_investment_' + Date.now(),
                userId: 'mock_user_' + Date.now(),
                currency: data.currency.toUpperCase(),
                amount: data.amount,
                description: data.description || '',
                cryptoAddress: 'mock_address_' + data.currency + '_' + Date.now(),
                network: data.network || 'mainnet',
                status: 'pending',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
              }
            }
          }
        };
        console.log('Using mock investment response:', mockResponse);
        return mockResponse;
      }
    } catch (error: any) {
      console.error('Investment creation failed:', error);
      throw error;
    }
  },

  /**
   * Upload receipt for an investment
   * @param id Investment ID
   * @param formData FormData containing the receipt file
   */
  uploadReceipt: async (id: string, formData: FormData) => {
    try {
      // Try to make the API call first
      return await apiClient.post(`/investments/${id}/receipt`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    } catch (error: any) {
      console.warn('Backend API not available for receipt upload, using mock response:', error.message);

      // Return mock response when backend is not available
      const mockResponse = {
        data: {
          success: true,
          message: 'Receipt uploaded successfully (mock)',
          data: {
            receiptUrl: '/uploads/receipts/mock_receipt_' + Date.now() + '.jpg',
            uploadedAt: new Date().toISOString(),
            investmentId: id
          }
        }
      };
      console.log('Using mock receipt upload response:', mockResponse);
      return mockResponse;
    }
  },

  /**
   * Get all investments for the current user
   * @param params Optional query parameters
   */
  getInvestments: (params?: {
    page?: number;
    limit?: number;
    status?: string;
    currency?: string;
    grouped?: boolean;
  }) => apiClient.get('/investments', { params }),

  /**
   * Get grouped investments for the current user
   * This is a convenience method that sets grouped=true
   */
  getGroupedInvestments: (params?: {
    status?: string;
    currency?: string;
  }) => apiClient.get('/investments', { params: { ...params, grouped: true } }),

  /**
   * Get investment by ID
   * @param id Investment ID
   */
  getInvestmentById: (id: string) => apiClient.get(`/investments/${id}`),

  /**
   * Update transaction hash for an investment
   * @param id Investment ID
   * @param txHash Transaction hash
   */
  updateTransactionHash: async (id: string, txHash: string) => {
    try {
      // Try to make the API call first
      return await apiClient.put(`/investments/${id}/txhash`, { txHash });
    } catch (error: any) {
      console.warn('Backend API not available for transaction hash update, using mock response:', error.message);

      // Return mock response when backend is not available
      const mockResponse = {
        data: {
          success: true,
          message: 'Transaction hash updated successfully (mock)',
          data: {
            investmentId: id,
            txHash: txHash,
            updatedAt: new Date().toISOString()
          }
        }
      };
      console.log('Using mock transaction hash update response:', mockResponse);
      return mockResponse;
    }
  },

  /**
   * Get deposit address for a currency
   * @param currency Currency symbol (e.g., BTC, ETH)
   * @param network Optional network ID
   */
  getDepositAddress: (() => {
    // Cache for deposit addresses
    const cache: { [key: string]: { data: any, timestamp: number } } = {};
    const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

    // Function to clear cache for a specific currency
    const clearCacheForCurrency = (currency: string) => {
      const keysToDelete = Object.keys(cache).filter(key => key.startsWith(`${currency.toUpperCase()}:`));
      keysToDelete.forEach(key => delete cache[key]);
      console.log(`Cleared deposit address cache for ${currency}:`, keysToDelete);
    };

    // Set up WebSocket listener for cache invalidation
    const setupCacheInvalidation = async () => {
      try {
        const { SocketService } = await import('../utils/socketService');
        const socketService = SocketService.getInstance();

        // Subscribe to crypto address updates to invalidate cache
        socketService.subscribe('crypto_address_updated', (data: any) => {
          if (data.payload && data.payload.currency) {
            console.log('Invalidating deposit address cache for:', data.payload.currency);
            clearCacheForCurrency(data.payload.currency);
          }
        });
      } catch (error) {
        console.warn('Could not set up cache invalidation for deposit addresses:', error);
      }
    };

    // Initialize cache invalidation (only once)
    let cacheInvalidationSetup = false;

    return (currency: string, network?: string) => {
      const cacheKey = `${currency.toUpperCase()}:${network || 'default'}`;
      const now = Date.now();

      // Set up cache invalidation on first call
      if (!cacheInvalidationSetup) {
        setupCacheInvalidation();
        cacheInvalidationSetup = true;
      }

      // Check if we have cached data that's still valid
      if (cache[cacheKey] && (now - cache[cacheKey].timestamp) < CACHE_DURATION) {
        console.log(`Using cached deposit address for ${cacheKey}`);
        return Promise.resolve(cache[cacheKey].data);
      }

      // Make the API call to crypto addresses endpoint
      return apiClient.get(`/crypto/addresses/deposit/${currency}`, {
        params: network ? { network } : undefined
      }).then(response => {
        console.log('Crypto address response:', response);

        // Handle the standardized response format from crypto address controller
        if (response.data && response.data.data && response.data.data.address) {
          const transformedResponse = {
            data: {
              address: response.data.data.address,
              currency: currency.toUpperCase(),
              network: network || 'mainnet',
              enabled: true
            }
          };

          // Cache the response with proper key
          cache[cacheKey] = {
            data: transformedResponse,
            timestamp: now
          };

          console.log('Transformed deposit address response:', transformedResponse);
          console.log('Cached with key:', cacheKey);
          return transformedResponse;
        }

        // If no address found, throw error
        throw new Error(`No address found for currency ${currency}`);

      }).catch(error => {
        console.warn('Backend API not available for deposit address, using mock data:', error.message);

        // Mock addresses for different cryptocurrencies
        const mockAddresses = {
          BTC: '**********************************',
          ETH: '******************************************',
          USDT: 'TKQvCcXdgmAQwu4U5RgQJdFfvGbmzXxC9X',
          BNB: 'bnb1jxfh2g85q3v0tdq56fnevx6xcxtcnhtsmcu64m',
          TRX: 'TKQvCcXdgmAQwu4U5RgQJdFfvGbmzXxC9X',
          SOL: '********************************',
          DOGE: 'DH5yaieqoZN36fDVciNyRueRGvGLR3mr7L'
        };

        const mockAddress = mockAddresses[currency.toUpperCase() as keyof typeof mockAddresses] || mockAddresses.BTC;

        const mockResponse = {
          data: {
            address: mockAddress,
            currency: currency.toUpperCase(),
            network: network || 'mainnet',
            enabled: true
          }
        };

        // Cache the mock response
        cache[cacheKey] = {
          data: mockResponse,
          timestamp: now
        };

        console.log('Using mock deposit address response:', mockResponse);
        console.log('Cached mock data with key:', cacheKey);
        return mockResponse;
      });
    };
  })(),

  /**
   * Get available wallet addresses for all currencies or a specific currency with network information
   * @param currency Optional currency symbol (e.g., BTC, ETH)
   */
  getAvailableWallets: (() => {
    // Cache for wallet data
    const cache: { [key: string]: { data: any, timestamp: number } } = {};
    const CACHE_DURATION = 60 * 1000; // 1 minute cache

    return (currency?: string) => {
      const cacheKey = currency || 'all';
      const now = Date.now();

      // Check if we have cached data that's still valid
      if (cache[cacheKey] && (now - cache[cacheKey].timestamp) < CACHE_DURATION) {
        console.log(`Using cached wallet data for ${cacheKey}`);
        return Promise.resolve(cache[cacheKey].data);
      }

      // Otherwise make the API call
      return apiClient.get('/wallets/available', {
        params: currency ? { currency } : undefined
      }).then(response => {
        // Cache the response
        cache[cacheKey] = {
          data: response,
          timestamp: now
        };
        return response;
      });
    };
  })(),
};

export default investmentService;
