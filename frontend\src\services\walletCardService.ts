import api from './api';
import { enhancedWalletService } from './enhancedWalletService';
import { walletService } from './walletService';
import { realTimeInterestService } from './realTimeInterestService';

/**
 * WalletCard Service
 * %100 gerçek veri akışı ile Investment Transaction modülü entegrasyonu
 * Perfect synchronization with admin approval workflow
 */

export interface EnhancedWalletData {
  userId: string;
  assets: Array<{
    symbol: string;
    balance: number;
    commissionBalance: number;
    interestBalance: number;
    mode: 'commission' | 'interest';
    network?: string;
    // Enhanced fields for perfect sync
    principalAmount: number;
    totalEarned: number;
    dailyInterestRate: number;
    lastInterestDate?: string;
    isLocked: boolean;
    lockExpiryDate?: string;
    daysUntilUnlock: number;
    totalInvestments: number;
    activePackages: number;
  }>;
  totalCommissionEarned: number;
  totalInterestEarned: number;
  realTimeData: {
    [currency: string]: {
      totalPrincipal: number;
      totalInterest: number;
      totalCommission: number;
      dailyInterest: number;
      realTimeInterest: number;
      nextDistributionTime: string;
      isWithdrawable: boolean;
      minimumThreshold: number;
      lastUpdated: string;
    };
  };
}

export interface WithdrawableBalance {
  asset: string;
  principalAmount: number;
  interestAmount: number;
  commissionAmount: number;
  totalWithdrawable: number;
  principalLocked: boolean;
  principalLockUntil: string | null;
  daysUntilUnlock: number;
  meetsMinimumThreshold: boolean;
  minimumThreshold: number;
  lastCalculated: string;
}

export interface WalletSummary {
  totalAssets: number;
  totalPrincipalUSD: number;
  totalInterestUSD: number;
  totalCommissionUSD: number;
  totalWithdrawableUSD: number;
  assetsWithActiveInvestments: number;
  assetsWithLocks: number;
  nextInterestDistribution: string | null;
  assetBreakdown: Array<{
    symbol: string;
    principalAmount: number;
    totalEarned: number;
    commissionBalance: number;
    interestBalance: number;
    isLocked: boolean;
    daysUntilUnlock: number;
    activePackages: number;
    dailyInterest: number;
    isWithdrawable: boolean;
  }>;
}

class WalletCardService {
  private lastUpdateTime: number = 0;
  private cacheTimeout: number = 5000; // 5 seconds cache
  private cachedData: EnhancedWalletData | null = null;

  /**
   * Get enhanced wallet data with perfect Investment Package sync
   */
  async getEnhancedWalletData(forceRefresh: boolean = false): Promise<EnhancedWalletData> {
    try {
      const now = Date.now();
      
      // Return cached data if not expired and not forcing refresh
      if (!forceRefresh && this.cachedData && (now - this.lastUpdateTime) < this.cacheTimeout) {
        console.log('💾 WalletCardService: Returning cached data');
        return this.cachedData;
      }

      console.log('🔄 WalletCardService: Fetching fresh enhanced wallet data');

      try {
        // Try enhanced endpoint first
        const response = await enhancedWalletService.getEnhancedWalletData();
        
        if (response.data && response.data.status === 'success') {
          this.cachedData = response.data.data.wallet;
          this.lastUpdateTime = now;
          
          console.log('✅ WalletCardService: Enhanced wallet data received:', {
            assetsCount: this.cachedData.assets.length,
            realTimeDataCurrencies: Object.keys(this.cachedData.realTimeData),
            lastUpdated: response.data.data.metadata?.lastUpdated
          });

          // Update real-time interest service for perfect sync
          this.updateRealTimeService();

          return this.cachedData;
        } else {
          throw new Error('Invalid response format');
        }
      } catch (enhancedError) {
        console.warn('⚠️ WalletCardService: Enhanced endpoint failed, falling back to basic wallet:', enhancedError);
        
        // Fallback to basic wallet endpoint
        const basicWallet = await walletService.getWalletBalance();
        
        // Convert basic wallet to enhanced format
        this.cachedData = {
          userId: basicWallet.userId || 'unknown',
          assets: (basicWallet.assets || []).map((asset: any) => ({
            symbol: asset.symbol,
            balance: asset.balance || 0,
            commissionBalance: asset.commissionBalance || 0,
            interestBalance: asset.interestBalance || 0,
            mode: asset.mode || 'commission',
            network: asset.network,
            // Enhanced fields with defaults
            principalAmount: asset.balance || 0,
            totalEarned: (asset.commissionBalance || 0) + (asset.interestBalance || 0),
            dailyInterestRate: 0.01, // 1% default
            lastInterestDate: asset.updatedAt,
            isLocked: false,
            lockExpiryDate: null,
            daysUntilUnlock: 0,
            totalInvestments: asset.balance || 0,
            activePackages: asset.balance > 0 ? 1 : 0
          })),
          totalCommissionEarned: basicWallet.totalCommissionEarned || 0,
          totalInterestEarned: basicWallet.totalInterestEarned || 0,
          realTimeData: {}
        };
        
        this.lastUpdateTime = now;
        
        console.log('✅ WalletCardService: Fallback wallet data created:', {
          assetsCount: this.cachedData.assets.length,
          source: 'basic_wallet_fallback'
        });

        return this.cachedData;
      }

    } catch (error) {
      console.error('❌ WalletCardService: Error fetching wallet data (both enhanced and basic failed):', error);
      throw error;
    }
  }

  /**
   * Get withdrawable balance for specific asset with 30-day lock validation
   */
  async getWithdrawableBalance(asset: string): Promise<WithdrawableBalance> {
    try {
      console.log(`🔍 WalletCardService: Getting withdrawable balance for ${asset}`);

      const response = await enhancedWalletService.getWithdrawableBalance(asset.toUpperCase());
      
      if (response.data.status === 'success') {
        const balance = response.data.data.withdrawableBalance;
        
        console.log(`✅ WalletCardService: Withdrawable balance for ${asset}:`, {
          totalWithdrawable: balance.totalWithdrawable,
          principalLocked: balance.principalLocked,
          daysUntilUnlock: balance.daysUntilUnlock
        });

        return balance;
      } else {
        throw new Error('Invalid response format');
      }

    } catch (error) {
      console.error(`❌ WalletCardService: Error getting withdrawable balance for ${asset}:`, error);
      throw error;
    }
  }

  /**
   * Get withdrawable balances for all assets
   */
  async getAllWithdrawableBalances(): Promise<{
    balances: WithdrawableBalance[];
    summary: {
      totalAssets: number;
      assetsWithdrawable: number;
      assetsLocked: number;
      totalWithdrawableUSD: number;
    };
  }> {
    try {
      console.log('🔍 WalletCardService: Getting all withdrawable balances');

      const response = await enhancedWalletService.getAllWithdrawableBalances();
      
      if (response.data.status === 'success') {
        const data = response.data.data;
        
        console.log('✅ WalletCardService: All withdrawable balances received:', {
          totalAssets: data.summary.totalAssets,
          assetsWithdrawable: data.summary.assetsWithdrawable,
          totalWithdrawableUSD: data.summary.totalWithdrawableUSD
        });

        return {
          balances: data.balances,
          summary: data.summary
        };
      } else {
        throw new Error('Invalid response format');
      }

    } catch (error) {
      console.error('❌ WalletCardService: Error getting all withdrawable balances:', error);
      throw error;
    }
  }

  /**
   * Get wallet summary with investment package integration
   */
  async getWalletSummary(): Promise<WalletSummary> {
    try {
      console.log('📊 WalletCardService: Getting wallet summary');

      const response = await enhancedWalletService.getWalletSummary();
      
      if (response.data.status === 'success') {
        const summary = response.data.data.summary;
        
        console.log('✅ WalletCardService: Wallet summary received:', {
          totalAssets: summary.totalAssets,
          totalPrincipalUSD: summary.totalPrincipalUSD,
          totalInterestUSD: summary.totalInterestUSD,
          assetsWithActiveInvestments: summary.assetsWithActiveInvestments
        });

        return summary;
      } else {
        throw new Error('Invalid response format');
      }

    } catch (error) {
      console.error('❌ WalletCardService: Error getting wallet summary:', error);
      throw error;
    }
  }

  /**
   * Force refresh wallet data (clear cache and fetch fresh data)
   */
  async forceRefreshWalletData(): Promise<EnhancedWalletData> {
    try {
      console.log('🔄 WalletCardService: Force refreshing wallet data');

      // Clear cache
      this.cachedData = null;
      this.lastUpdateTime = 0;

      // Force refresh from server
      const response = await enhancedWalletService.forceRefreshWalletData();
      
      if (response.data.status === 'success') {
        this.cachedData = response.data.data.wallet;
        this.lastUpdateTime = Date.now();
        
        console.log('✅ WalletCardService: Wallet data force refreshed');

        // Update real-time service
        this.updateRealTimeService();

        return this.cachedData;
      } else {
        throw new Error('Invalid response format');
      }

    } catch (error) {
      console.error('❌ WalletCardService: Error force refreshing wallet data:', error);
      throw error;
    }
  }

  /**
   * Calculate real-time interest for display
   */
  calculateRealTimeInterest(asset: string): {
    dailyInterest: number;
    realTimeInterest: number;
    totalEarned: number;
    nextDistribution: Date;
  } {
    if (!this.cachedData || !this.cachedData.realTimeData[asset]) {
      return {
        dailyInterest: 0,
        realTimeInterest: 0,
        totalEarned: 0,
        nextDistribution: new Date()
      };
    }

    const data = this.cachedData.realTimeData[asset];
    
    return {
      dailyInterest: data.dailyInterest,
      realTimeInterest: data.realTimeInterest,
      totalEarned: data.totalInterest,
      nextDistribution: new Date(data.nextDistributionTime)
    };
  }

  /**
   * Get asset display data for WalletCard
   */
  getAssetDisplayData(symbol: string): {
    symbol: string;
    principalAmount: number;
    totalEarned: number;
    commissionBalance: number;
    interestBalance: number;
    dailyInterest: number;
    realTimeInterest: number;
    isLocked: boolean;
    daysUntilUnlock: number;
    activePackages: number;
    isWithdrawable: boolean;
    lastInterestDate?: string;
    nextDistributionTime?: string;
  } | null {
    if (!this.cachedData) {
      return null;
    }

    const asset = this.cachedData.assets.find(a => a.symbol === symbol);
    const realTimeData = this.cachedData.realTimeData[symbol];

    if (!asset) {
      return null;
    }

    return {
      symbol: asset.symbol,
      principalAmount: asset.principalAmount,
      totalEarned: asset.totalEarned,
      commissionBalance: asset.commissionBalance,
      interestBalance: asset.interestBalance,
      dailyInterest: realTimeData?.dailyInterest || 0,
      realTimeInterest: realTimeData?.realTimeInterest || 0,
      isLocked: asset.isLocked,
      daysUntilUnlock: asset.daysUntilUnlock,
      activePackages: asset.activePackages,
      isWithdrawable: realTimeData?.isWithdrawable || false,
      lastInterestDate: asset.lastInterestDate,
      nextDistributionTime: realTimeData?.nextDistributionTime
    };
  }

  /**
   * Update real-time interest service for perfect sync
   */
  private updateRealTimeService(): void {
    if (!this.cachedData) return;

    try {
      // Convert enhanced wallet data to real-time service format
      const packages = this.cachedData.assets
        .filter(asset => asset.activePackages > 0)
        .map(asset => ({
          _id: `wallet-${asset.symbol}`,
          amount: asset.principalAmount,
          currency: asset.symbol,
          activatedAt: asset.lastInterestDate || new Date().toISOString(),
          totalEarned: asset.totalEarned,
          accumulatedInterest: asset.totalEarned,
          lastInterestDistribution: asset.lastInterestDate,
          interestRate: asset.dailyInterestRate,
          status: 'active',
          activeDays: asset.isLocked ? Math.max(0, 30 - asset.daysUntilUnlock) : 30,
          compoundEnabled: true
        }));

      realTimeInterestService.updatePackages(packages);
      
      console.log('🔄 WalletCardService: Real-time service updated with wallet data');

    } catch (error) {
      console.error('❌ WalletCardService: Error updating real-time service:', error);
    }
  }

  /**
   * Clear cache (useful for logout or refresh)
   */
  clearCache(): void {
    this.cachedData = null;
    this.lastUpdateTime = 0;
    console.log('🗑️ WalletCardService: Cache cleared');
  }

  /**
   * Check if data is cached and fresh
   */
  isCacheFresh(): boolean {
    return this.cachedData !== null && (Date.now() - this.lastUpdateTime) < this.cacheTimeout;
  }

  /**
   * Get cached data without API call
   */
  getCachedData(): EnhancedWalletData | null {
    return this.cachedData;
  }
}

export const walletCardService = new WalletCardService();
export default walletCardService;
