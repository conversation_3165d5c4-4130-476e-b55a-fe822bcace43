import { apiClient } from '../utils/apiClient';

/**
 * Enhanced API service for wallet-related endpoints with unified transaction system
 */
export const walletService = {
  // ===== LEGACY WALLET ENDPOINTS =====

  /**
   * Connect wallet
   * @param address Wallet address
   */
  connectWallet: (address: string) => apiClient.post('/wallets/connect', { address }),

  /**
   * Get wallet info (legacy)
   */
  getWalletBalance: () => apiClient.get('/wallets/info'),

  /**
   * Toggle between commission and interest mode
   * @param asset Asset symbol
   * @param mode Mode ('commission' or 'interest')
   */
  toggleMode: (asset: string, mode: 'commission' | 'interest') =>
    apiClient.post('/wallets/toggle-mode', { asset, mode }),

  /**
   * Deposit asset (legacy)
   * @param data Deposit data
   */
  depositAsset: (data: {
    asset: string;
    amount: number;
    txHash?: string;
    network?: string;
  }) => apiClient.post('/wallets/deposit', data),

  /**
   * Get transaction history (legacy)
   * @param params Optional query parameters
   */
  getTransactionHistory: (params?: {
    page?: number;
    limit?: number;
    type?: string;
    asset?: string;
  }) => apiClient.get('/wallets/transactions', { params }),

  // ===== NEW UNIFIED TRANSACTION SYSTEM =====

  /**
   * Get enhanced wallet balances with real-time data
   */
  getEnhancedWalletBalances: () => apiClient.get('/transactions/wallet/balances'),

  /**
   * Get withdrawable balance for specific cryptocurrency
   * @param cryptocurrency Cryptocurrency symbol (BTC, ETH, etc.)
   * @param type Withdrawal type ('balance', 'interest', 'commission')
   */
  getWithdrawableBalance: (cryptocurrency: string, type: 'balance' | 'interest' | 'commission' = 'balance') =>
    apiClient.get(`/transactions/wallet/withdrawable/${cryptocurrency}?type=${type}`),

  /**
   * Sync wallet data across all components
   */
  syncWalletData: () => apiClient.post('/transactions/wallet/sync'),

  /**
   * Create deposit transaction
   * @param data Deposit transaction data
   */
  createDeposit: (data: {
    cryptocurrency: string;
    network: string;
    amount: number;
    walletAddress: string;
    txHash?: string;
    autoInvestmentEnabled?: boolean;
  }) => apiClient.post('/transactions/deposit', data),

  /**
   * Validate withdrawal request
   * @param data Withdrawal validation data
   */
  validateWithdrawal: (data: {
    cryptocurrency: string;
    amount: number;
    withdrawalType: 'balance' | 'interest' | 'commission';
    investmentPackageId?: string;
  }) => apiClient.post('/transactions/withdrawal/validate', data),

  /**
   * Create withdrawal transaction
   * @param data Withdrawal transaction data
   */
  createWithdrawal: (data: {
    cryptocurrency: string;
    network: string;
    amount: number;
    walletAddress: string;
    withdrawalType: 'balance' | 'interest' | 'commission';
    investmentPackageId?: string;
  }) => apiClient.post('/transactions/withdrawal', data),

  /**
   * Get unified transaction history
   * @param params Query parameters
   */
  getUnifiedTransactionHistory: (params?: {
    type?: 'deposit' | 'withdrawal';
    cryptocurrency?: string;
    page?: number;
    limit?: number;
  }) => apiClient.get('/transactions/history', { params }),

  /**
   * Get transaction details
   * @param transactionId Transaction ID
   */
  getTransactionDetails: (transactionId: string) =>
    apiClient.get(`/transactions/${transactionId}`),
};

export default walletService;
